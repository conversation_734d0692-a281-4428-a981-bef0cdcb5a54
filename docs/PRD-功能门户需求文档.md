# 功能门户（Portal）管理模块需求文档

## 1. 需求概述

### 1.1 模块简介
功能门户（Portal）管理模块是Spring Vue应用的核心工作区域管理系统，为用户提供统一的工作入口和资源组织方式。该模块采用两级管理结构：门户（Portal）和查询计划（QueryPlan），支持用户创建多个工作门户，每个门户下可以配置多个查询计划来管理不同类型的任务和资源。

### 1.2 核心功能
- **门户管理**：门户的添加、修改、删除和检索，门户包含名字、描述等基本信息
- **查询计划管理**：门户下一级是查询计划的添加、修改、删除和检索，包括名字、描述等信息
- **抽象对象查询**：查询计划面向抽象的对象，对象包含名字、类型和多个标签（tags），类型包括CiTask、CDTask等
- **智能查询服务**：查询服务可以基于名字、类型、标签执行查询工作，紧凑设计，查询请求组合进单独一个对象
- **可视化界面**：门户主页是棋盘布局，每个门户是一个圆角矩形，内容显示门户名字和描述
- **导航跳转**：进入门户是棋盘布局的对象查询计划，每个查询计划是一个圆角矩形，内容显示查询计划的名字和描述
- **类型路由**：点击查询计划，根据不同类型进入特定对象的管理页面，比如CiTask管理页面

### 1.3 业务价值
- 提供统一的工作入口，提升用户体验
- 支持多工作区域管理，满足不同项目需求
- 通过查询计划实现资源的智能组织和快速访问
- 与现有DevOps、MCP模块深度集成，形成完整的工作流

## 2. 功能规格说明

### 2.1 门户（Portal）管理

#### 2.1.1 门户实体定义
```typescript
interface Portal {
  id?: number
  name: string           // 门户名称，必填，最大长度100字符
  description?: string   // 门户描述，可选，最大长度500字符
  color?: string        // 门户主题色，可选，默认为蓝色
  icon?: string         // 门户图标，可选，默认为文件夹图标
  status: string        // 门户状态：active, inactive, archived
  userId: number        // 所属用户ID
  createdAt: string     // 创建时间
  updatedAt: string     // 更新时间
}
```

#### 2.1.2 门户功能需求
- **创建门户**：用户可以创建新的工作门户，设置名称、描述、主题色和图标
- **编辑门户**：用户可以修改门户的基本信息，包括名称、描述、主题色和图标
- **删除门户**：用户可以删除不需要的门户，删除前需要确认操作
- **查看门户列表**：用户可以查看自己创建的所有门户，支持分页和搜索
- **门户状态管理**：支持激活、停用、归档等状态管理

### 2.2 查询计划（QueryPlan）管理

#### 2.2.1 查询计划实体定义
```typescript
interface QueryPlan {
  id?: number
  name: string           // 查询计划名称，必填，最大长度100字符
  description?: string   // 查询计划描述，可选，最大长度500字符
  portalId: number      // 所属门户ID
  targetType: string    // 目标对象类型：CiTask, CdTask, McpServer, CloudPlatform等
  queryConfig: QueryConfig // 查询配置
  color?: string        // 计划主题色，可选
  icon?: string         // 计划图标，可选
  status: string        // 计划状态：active, inactive
  userId: number        // 所属用户ID
  createdAt: string     // 创建时间
  updatedAt: string     // 更新时间
}

interface QueryConfig {
  nameFilter?: string      // 名称过滤器，支持模糊匹配
  typeFilter?: string[]    // 类型过滤器，支持多选
  tagFilters?: string[]    // 标签过滤器，支持多选
  statusFilter?: string[]  // 状态过滤器，支持多选
  sortBy?: string         // 排序字段
  sortOrder?: 'asc' | 'desc' // 排序方向
  limit?: number          // 结果数量限制
}
```

#### 2.2.2 查询计划功能需求
- **创建查询计划**：用户可以在门户下创建查询计划，配置查询条件和目标类型
- **编辑查询计划**：用户可以修改查询计划的配置，包括查询条件和显示设置
- **删除查询计划**：用户可以删除不需要的查询计划
- **执行查询**：系统根据查询计划配置，动态查询匹配的对象
- **查询结果展示**：以卡片形式展示查询结果，支持分页和排序

### 2.3 抽象对象查询服务

#### 2.3.1 查询对象接口定义
```typescript
interface QueryableObject {
  id: string | number
  name: string
  type: string
  tags: string[]
  status: string
  description?: string
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
}

interface QueryRequest {
  targetType: string
  nameFilter?: string
  typeFilter?: string[]
  tagFilters?: string[]
  statusFilter?: string[]
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  page?: number
  size?: number
}

interface QueryResponse {
  items: QueryableObject[]
  total: number
  page: number
  size: number
  hasNext: boolean
  hasPrevious: boolean
}
```

#### 2.3.2 支持的对象类型
- **CiTask**：CI任务对象，来源于DevOps模块
- **CdTask**：CD任务对象，来源于DevOps模块
- **McpServer**：MCP服务器对象，来源于MCP模块
- **CloudPlatform**：云平台对象，来源于云平台模块
- **DevOpsProject**：DevOps项目对象，来源于DevOps模块
- **DevOpsApplication**：DevOps应用对象，来源于DevOps模块

## 3. 用户界面设计

### 3.1 门户主页设计

#### 3.1.1 布局结构
- **页面标题**：显示"我的门户"，右侧有"创建门户"按钮
- **门户网格**：采用响应式网格布局，每行显示3-4个门户卡片
- **门户卡片**：圆角矩形设计，包含门户图标、名称、描述和操作按钮

#### 3.1.2 门户卡片设计
```
┌─────────────────────────────────┐
│  🏢  门户名称                    │
│                                 │
│  门户描述信息...                │
│                                 │
│  📊 3个查询计划  ⏰ 2天前更新    │
│                                 │
│  [进入] [编辑] [删除]           │
└─────────────────────────────────┘
```

### 3.2 查询计划页面设计

#### 3.2.1 页面布局
- **面包屑导航**：首页 > 门户名称 > 查询计划
- **页面标题**：显示门户名称，右侧有"创建查询计划"按钮
- **计划网格**：采用响应式网格布局，每行显示2-3个查询计划卡片

#### 3.2.2 查询计划卡片设计
```
┌─────────────────────────────────┐
│  🔍  查询计划名称                │
│                                 │
│  目标类型：CiTask               │
│  查询条件：状态=运行中           │
│                                 │
│  📈 找到12个结果  ⏰ 1小时前     │
│                                 │
│  [查看结果] [编辑] [删除]       │
└─────────────────────────────────┘
```

### 3.3 查询结果页面设计

#### 3.3.1 页面布局
- **面包屑导航**：首页 > 门户名称 > 查询计划名称 > 查询结果
- **筛选工具栏**：提供快速筛选和排序选项
- **结果列表**：以表格或卡片形式展示查询结果

#### 3.3.2 结果项设计
```
┌─────────────────────────────────┐
│  📋  对象名称        [运行中]    │
│                                 │
│  类型：CiTask                   │
│  标签：build, test, deploy      │
│  描述：构建和测试任务...         │
│                                 │
│  ⏰ 创建于 2024-01-15           │
│  [查看详情] [快速操作]          │
└─────────────────────────────────┘
```

## 4. 数据模型设计

### 4.1 数据库表结构

#### 4.1.1 门户表（portals）
```sql
CREATE TABLE portals (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '门户名称',
    description VARCHAR(500) COMMENT '门户描述',
    color VARCHAR(20) DEFAULT '#1890ff' COMMENT '主题色',
    icon VARCHAR(50) DEFAULT 'folder' COMMENT '图标',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);
```

#### 4.1.2 查询计划表（query_plans）
```sql
CREATE TABLE query_plans (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '查询计划名称',
    description VARCHAR(500) COMMENT '查询计划描述',
    portal_id BIGINT NOT NULL COMMENT '所属门户ID',
    target_type VARCHAR(50) NOT NULL COMMENT '目标对象类型',
    query_config JSON COMMENT '查询配置',
    color VARCHAR(20) DEFAULT '#52c41a' COMMENT '主题色',
    icon VARCHAR(50) DEFAULT 'search' COMMENT '图标',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (portal_id) REFERENCES portals(id) ON DELETE CASCADE,
    INDEX idx_portal_id (portal_id),
    INDEX idx_user_id (user_id),
    INDEX idx_target_type (target_type),
    INDEX idx_status (status)
);
```

### 4.2 实体关系图

```mermaid
erDiagram
    User ||--o{ Portal : owns
    Portal ||--o{ QueryPlan : contains
    QueryPlan }o--|| TargetType : queries

    User {
        bigint id PK
        string username
        string email
        string full_name
    }

    Portal {
        bigint id PK
        string name
        string description
        string color
        string icon
        string status
        bigint user_id FK
        timestamp created_at
        timestamp updated_at
    }

    QueryPlan {
        bigint id PK
        string name
        string description
        bigint portal_id FK
        string target_type
        json query_config
        string color
        string icon
        string status
        bigint user_id FK
        timestamp created_at
        timestamp updated_at
    }

    TargetType {
        string type_name PK
        string display_name
        string description
        json schema
    }
```

## 5. API接口规范

### 5.1 门户管理API

#### 5.1.1 创建门户
```http
POST /api/portals
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "开发项目门户",
  "description": "用于管理开发相关的任务和资源",
  "color": "#1890ff",
  "icon": "code"
}

Response:
{
  "id": 1,
  "name": "开发项目门户",
  "description": "用于管理开发相关的任务和资源",
  "color": "#1890ff",
  "icon": "code",
  "status": "active",
  "userId": 123,
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

#### 5.1.2 获取门户列表
```http
GET /api/portals?page=0&size=10&search=开发
Authorization: Bearer {token}

Response:
{
  "content": [
    {
      "id": 1,
      "name": "开发项目门户",
      "description": "用于管理开发相关的任务和资源",
      "color": "#1890ff",
      "icon": "code",
      "status": "active",
      "userId": 123,
      "queryPlanCount": 3,
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ],
  "page": 0,
  "size": 10,
  "total": 1,
  "totalPages": 1,
  "hasNext": false,
  "hasPrevious": false
}
```

#### 5.1.3 获取门户详情
```http
GET /api/portals/{id}
Authorization: Bearer {token}

Response:
{
  "id": 1,
  "name": "开发项目门户",
  "description": "用于管理开发相关的任务和资源",
  "color": "#1890ff",
  "icon": "code",
  "status": "active",
  "userId": 123,
  "queryPlanCount": 3,
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

#### 5.1.4 更新门户
```http
PUT /api/portals/{id}
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "开发项目门户（更新）",
  "description": "更新后的描述",
  "color": "#52c41a",
  "icon": "project"
}

Response:
{
  "id": 1,
  "name": "开发项目门户（更新）",
  "description": "更新后的描述",
  "color": "#52c41a",
  "icon": "project",
  "status": "active",
  "userId": 123,
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T11:45:00Z"
}
```

#### 5.1.5 删除门户
```http
DELETE /api/portals/{id}
Authorization: Bearer {token}

Response: 204 No Content
```

### 5.2 查询计划管理API

#### 5.2.1 创建查询计划
```http
POST /api/portals/{portalId}/query-plans
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "运行中的CI任务",
  "description": "查询所有运行中的CI构建任务",
  "targetType": "CiTask",
  "queryConfig": {
    "statusFilter": ["running", "pending"],
    "tagFilters": ["build", "test"],
    "sortBy": "createdAt",
    "sortOrder": "desc",
    "limit": 20
  },
  "color": "#faad14",
  "icon": "build"
}

Response:
{
  "id": 1,
  "name": "运行中的CI任务",
  "description": "查询所有运行中的CI构建任务",
  "portalId": 1,
  "targetType": "CiTask",
  "queryConfig": {
    "statusFilter": ["running", "pending"],
    "tagFilters": ["build", "test"],
    "sortBy": "createdAt",
    "sortOrder": "desc",
    "limit": 20
  },
  "color": "#faad14",
  "icon": "build",
  "status": "active",
  "userId": 123,
  "createdAt": "2024-01-15T11:00:00Z",
  "updatedAt": "2024-01-15T11:00:00Z"
}
```

#### 5.2.2 获取查询计划列表
```http
GET /api/portals/{portalId}/query-plans?page=0&size=10
Authorization: Bearer {token}

Response:
{
  "content": [
    {
      "id": 1,
      "name": "运行中的CI任务",
      "description": "查询所有运行中的CI构建任务",
      "portalId": 1,
      "targetType": "CiTask",
      "queryConfig": {
        "statusFilter": ["running", "pending"],
        "tagFilters": ["build", "test"],
        "sortBy": "createdAt",
        "sortOrder": "desc",
        "limit": 20
      },
      "color": "#faad14",
      "icon": "build",
      "status": "active",
      "userId": 123,
      "resultCount": 12,
      "lastExecuted": "2024-01-15T12:30:00Z",
      "createdAt": "2024-01-15T11:00:00Z",
      "updatedAt": "2024-01-15T11:00:00Z"
    }
  ],
  "page": 0,
  "size": 10,
  "total": 1,
  "totalPages": 1,
  "hasNext": false,
  "hasPrevious": false
}
```

#### 5.2.3 执行查询计划
```http
POST /api/query-plans/{id}/execute
Authorization: Bearer {token}

Response:
{
  "items": [
    {
      "id": "ci-task-001",
      "name": "前端构建任务",
      "type": "CiTask",
      "tags": ["build", "frontend", "vue"],
      "status": "running",
      "description": "Vue.js前端项目构建任务",
      "metadata": {
        "projectId": 1,
        "branch": "main",
        "commit": "abc123",
        "duration": 180
      },
      "createdAt": "2024-01-15T12:00:00Z",
      "updatedAt": "2024-01-15T12:30:00Z"
    }
  ],
  "total": 12,
  "page": 0,
  "size": 20,
  "hasNext": false,
  "hasPrevious": false
}
```

### 5.3 抽象查询服务API

#### 5.3.1 通用查询接口
```http
POST /api/query/search
Content-Type: application/json
Authorization: Bearer {token}

{
  "targetType": "CiTask",
  "nameFilter": "构建",
  "statusFilter": ["running", "completed"],
  "tagFilters": ["build"],
  "sortBy": "createdAt",
  "sortOrder": "desc",
  "page": 0,
  "size": 10
}

Response:
{
  "items": [
    {
      "id": "ci-task-001",
      "name": "前端构建任务",
      "type": "CiTask",
      "tags": ["build", "frontend", "vue"],
      "status": "running",
      "description": "Vue.js前端项目构建任务",
      "metadata": {
        "projectId": 1,
        "branch": "main",
        "commit": "abc123"
      },
      "createdAt": "2024-01-15T12:00:00Z",
      "updatedAt": "2024-01-15T12:30:00Z"
    }
  ],
  "total": 25,
  "page": 0,
  "size": 10,
  "hasNext": true,
  "hasPrevious": false
}
```

#### 5.3.2 获取支持的对象类型
```http
GET /api/query/target-types
Authorization: Bearer {token}

Response:
[
  {
    "type": "CiTask",
    "displayName": "CI任务",
    "description": "持续集成构建任务",
    "availableFields": ["name", "status", "tags", "projectId", "branch"],
    "availableStatuses": ["pending", "running", "completed", "failed", "cancelled"]
  },
  {
    "type": "CdTask",
    "displayName": "CD任务",
    "description": "持续部署任务",
    "availableFields": ["name", "status", "tags", "applicationId", "environment"],
    "availableStatuses": ["pending", "running", "completed", "failed", "rolled_back"]
  }
]
```

## 6. 技术实现要求

### 6.1 后端技术要求

#### 6.1.1 技术栈
- **框架**：Spring Boot 3.x + Spring WebFlux
- **数据库**：Spring Data R2DBC（支持H2/PostgreSQL）
- **安全**：Spring Security + JWT认证
- **文档**：所有注释和文档使用中文
- **代码规范**：不使用Lombok，手动编写getter/setter

#### 6.1.2 架构模式
- **分层架构**：Controller -> Service -> Repository
- **实体分离**：数据库实体（Entity）与前端DTO（Model）分离
- **响应式编程**：使用Mono/Flux进行异步处理
- **异常处理**：统一异常处理和错误响应

#### 6.1.3 代码组织
```
backend/src/main/java/com/example/springvueapp/portal/
├── controller/          # 控制器层
│   ├── PortalController.java
│   └── QueryPlanController.java
├── service/            # 服务层
│   ├── PortalService.java
│   ├── QueryPlanService.java
│   └── QueryService.java
├── repository/         # 数据访问层
│   ├── PortalRepository.java
│   └── QueryPlanRepository.java
├── entity/            # 数据库实体
│   ├── PortalEntity.java
│   └── QueryPlanEntity.java
├── model/             # 前端DTO
│   ├── Portal.java
│   ├── QueryPlan.java
│   └── QueryRequest.java
├── mapper/            # 实体映射
│   ├── PortalMapper.java
│   └── QueryPlanMapper.java
└── enums/             # 枚举定义
    ├── PortalStatus.java
    └── TargetType.java
```

### 6.2 前端技术要求

#### 6.2.1 技术栈
- **框架**：Vue 3 + TypeScript + Composition API
- **状态管理**：Pinia
- **路由**：Vue Router 4
- **样式**：Tailwind CSS
- **HTTP客户端**：Axios

#### 6.2.2 代码组织
```
frontend/src/modules/portal/
├── components/         # 组件
│   ├── PortalCard.vue
│   ├── QueryPlanCard.vue
│   ├── QueryResultList.vue
│   └── PortalForm.vue
├── views/             # 页面组件
│   ├── PortalList.vue
│   ├── QueryPlanList.vue
│   └── QueryResult.vue
├── stores/            # 状态管理
│   ├── portalStore.ts
│   └── queryStore.ts
├── api/               # API服务
│   ├── portalApi.ts
│   └── queryApi.ts
├── types/             # 类型定义
│   ├── portal.ts
│   └── query.ts
├── router/            # 路由配置
│   └── index.ts
└── index.ts           # 模块入口
```

### 6.3 测试要求

#### 6.3.1 单元测试覆盖
- **后端测试**：Service层和Repository层的单元测试
- **前端测试**：Store和API服务的单元测试
- **测试覆盖率**：要求达到80%以上
- **测试质量**：测试真实业务逻辑，而非简单的通过测试

#### 6.3.2 测试工具
- **后端**：JUnit 5 + Mockito + WebTestClient
- **前端**：Vitest + Vue Test Utils

## 7. 集成要求

### 7.1 与现有模块集成

#### 7.1.1 DevOps模块集成
- 查询DevOps项目、应用、组件、资源
- 查询CI/CD任务和执行状态
- 支持跳转到DevOps管理页面

#### 7.1.2 MCP模块集成
- 查询MCP服务器配置和实例
- 查询MCP工具和执行记录
- 支持跳转到MCP管理页面

#### 7.1.3 云平台模块集成
- 查询云平台配置和连接状态
- 查询云平台资源和使用情况
- 支持跳转到云平台管理页面

### 7.2 全局状态管理

#### 7.2.1 当前门户上下文
```typescript
interface PortalContext {
  currentPortal: Portal | null
  currentQueryPlan: QueryPlan | null
  breadcrumbs: BreadcrumbItem[]
}

interface BreadcrumbItem {
  name: string
  path: string
  icon?: string
}
```

#### 7.2.2 上下文Store
- 用户进入门户后，Store中保存当前门户信息
- 其他模块可以从Store获取当前门户信息
- 确保在当前门户下创建的查询计划能正确关联到该门户

## 8. 部署和配置

### 8.1 数据库配置
- 在现有数据库配置中添加Portal模块表
- 更新R2DBC Repository扫描路径
- 添加数据库初始化脚本

### 8.2 路由配置
- 更新前端路由配置，添加Portal模块路由
- 配置路由守卫，确保认证用户才能访问
- 更新主页布局，添加Portal模块入口

### 8.3 Native Image配置
- 更新NativeHintsConfiguration，添加Portal相关类
- 确保GraalVM编译时包含所有必要的反射配置

## 9. 验收标准

### 9.1 功能验收
- [ ] 用户可以创建、编辑、删除门户
- [ ] 用户可以在门户下创建、编辑、删除查询计划
- [ ] 查询计划可以正确查询各种类型的对象
- [ ] 查询结果可以正确显示和分页
- [ ] 点击查询结果可以跳转到对应的管理页面

### 9.2 性能验收
- [ ] 门户列表加载时间 < 1秒
- [ ] 查询计划执行时间 < 2秒
- [ ] 查询结果展示时间 < 1秒
- [ ] 支持并发用户数 > 100

### 9.3 质量验收
- [ ] 单元测试覆盖率 > 80%
- [ ] 所有测试用例通过
- [ ] 代码符合项目规范
- [ ] 文档完整且准确

### 9.4 兼容性验收
- [ ] 支持Chrome、Firefox、Safari等主流浏览器
- [ ] 支持响应式设计，适配移动端
- [ ] 与现有模块无冲突
- [ ] GraalVM Native Image编译成功