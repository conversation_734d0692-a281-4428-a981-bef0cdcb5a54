<template>
  <div class="main-layout">
    <!-- 顶部标题栏 -->
    <header class="layout-header">
      <div class="header-content">
        <div class="header-left">
          <!-- Logo和应用名称 -->
          <div class="app-logo">
            <Icon name="appstore" class="logo-icon" />
            <span class="app-name">Spring Vue App</span>
          </div>

          <!-- 面包屑导航 -->
          <div class="breadcrumb-wrapper" v-if="showBreadcrumb">
            <nav class="breadcrumb">
              <span
                v-for="(item, index) in breadcrumbs"
                :key="index"
                class="breadcrumb-item"
              >
                <router-link v-if="index < breadcrumbs.length - 1" :to="item.path" class="breadcrumb-link">
                  <Icon v-if="item.icon" :name="item.icon" />
                  {{ item.name }}
                </router-link>
                <span v-else class="breadcrumb-current">
                  <Icon v-if="item.icon" :name="item.icon" />
                  {{ item.name }}
                </span>
                <span v-if="index < breadcrumbs.length - 1" class="breadcrumb-separator">/</span>
              </span>
            </nav>
          </div>
        </div>

        <div class="header-right">
          <!-- 门户切换器 -->
          <div class="portal-switcher" v-if="hasCurrentPortal">
            <Select
              v-model:value="selectedPortalId"
              :options="portalSelectOptions"
              placeholder="选择工作区域"
              style="width: 200px"
              @change="handlePortalChange"
            >
              <template #option="{ option }">
                <div class="portal-option">
                  <div class="portal-color" :style="{ backgroundColor: option.data?.color }"></div>
                  {{ option.label }}
                </div>
              </template>
            </Select>
          </div>

          <!-- 用户菜单 -->
          <div class="user-menu-wrapper">
            <div class="dropdown-wrapper">
              <Button type="text" class="user-menu-trigger" @click="toggleUserMenu">
                <Icon name="user" />
                {{ currentUser?.username || '用户' }}
                <Icon name="down" />
              </Button>
              <div v-if="userMenuVisible" class="dropdown-menu user-dropdown">
                <div class="menu-item" @click="handleUserProfile">
                  <Icon name="user" />
                  个人资料
                </div>
                <div class="menu-item" @click="handleSettings">
                  <Icon name="setting" />
                  系统设置
                </div>
                <div class="menu-divider"></div>
                <div class="menu-item danger" @click="handleLogout">
                  <Icon name="logout" />
                  退出登录
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="layout-body">
      <!-- 左侧菜单栏 -->
      <aside
        class="layout-sider"
        :class="{ collapsed: siderCollapsed }"
      >
        <div class="sider-content">
          <!-- 折叠按钮 -->
          <div class="sider-trigger" @click="toggleSider">
            <Icon :name="siderCollapsed ? 'menu-unfold' : 'menu-fold'" />
          </div>

          <!-- 主导航菜单 -->
          <nav class="main-menu">
            <div
              v-if="!isPortalMode"
              class="menu-item"
              :class="{ active: selectedMenuKeys.includes('portal') }"
              @click="handleMenuClick('portal')"
            >
              <Icon name="appstore" />
              <span v-if="!siderCollapsed">工作区域</span>
            </div>

            <div
              v-if="!isPortalMode"
              class="menu-item"
              :class="{ active: selectedMenuKeys.includes('mcp') }"
              @click="handleMenuClick('mcp')"
            >
              <Icon name="cloud" />
              <span v-if="!siderCollapsed">MCP服务器</span>
            </div>

            <div
              v-if="!isPortalMode"
              class="menu-item"
              :class="{ active: selectedMenuKeys.includes('devops') }"
              @click="handleMenuClick('devops')"
            >
              <Icon name="code" />
              <span v-if="!siderCollapsed">DevOps管理</span>
            </div>

            <div
              v-if="!isPortalMode"
              class="menu-item"
              :class="{ active: selectedMenuKeys.includes('cloudplatform') }"
              @click="handleMenuClick('cloudplatform')"
            >
              <Icon name="database" />
              <span v-if="!siderCollapsed">云平台管理</span>
            </div>

            <!-- 门户模式下的菜单 -->
            <template v-if="isPortalMode && hasCurrentPortal">
              <div
                class="menu-item"
                :class="{ active: selectedMenuKeys.includes('portal-overview') }"
                @click="handleMenuClick({ key: 'portal-overview' })"
              >
                <Icon name="dashboard" />
                <span v-if="!siderCollapsed">门户概览</span>
              </div>

              <div
                class="menu-item"
                :class="{ active: selectedMenuKeys.includes('query-plans') }"
                @click="handleMenuClick({ key: 'query-plans' })"
              >
                <Icon name="search" />
                <span v-if="!siderCollapsed">查询计划</span>
              </div>

              <div
                class="menu-item"
                :class="{ active: selectedMenuKeys.includes('portal-settings') }"
                @click="handleMenuClick({ key: 'portal-settings' })"
              >
                <Icon name="setting" />
                <span v-if="!siderCollapsed">门户设置</span>
              </div>

              <div class="menu-divider"></div>

              <div
                class="menu-item exit-portal-item"
                @click="handleMenuClick({ key: 'exit-portal-mode' })"
              >
                <Icon name="arrow-left" />
                <span v-if="!siderCollapsed">退出门户模式</span>
              </div>
            </template>
          </nav>
        </div>
      </aside>

      <!-- 右侧内容区域 -->
      <main class="layout-content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Button from '../ui/Button.vue'
import Icon from '../ui/Icon.vue'
import Select from '../ui/Select.vue'
import { message } from '../../utils/message'
import { useAuthStore } from '../../modules/common/stores/auth'
import { usePortalContextStore } from '../../modules/common/stores/portalContextStore'
import { usePortalStore } from '../../modules/portal/stores/portalStore'
import type { Portal } from '../../modules/portal/types/portal'

// 组合式API
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const portalContextStore = usePortalContextStore()
const portalStore = usePortalStore()

// 响应式数据
const siderCollapsed = ref(false)
const selectedMenuKeys = ref<string[]>([])
const openMenuKeys = ref<string[]>([])
const availablePortals = ref<Portal[]>([])
const userMenuVisible = ref(false)

// 计算属性
const currentUser = computed(() => authStore.user)
const isPortalMode = computed(() => portalContextStore.isPortalMode)
const hasCurrentPortal = computed(() => portalContextStore.hasCurrentPortal)
const currentPortal = computed(() => portalContextStore.currentPortal)
const breadcrumbs = computed(() => portalContextStore.breadcrumbs)
const showBreadcrumb = computed(() => breadcrumbs.value.length > 1)

const portalSelectOptions = computed(() => {
  return availablePortals.value.map(portal => ({
    label: portal.name,
    value: portal.id,
    data: {
      color: portal.color
    }
  }))
})

const selectedPortalId = computed({
  get: () => currentPortal.value?.id,
  set: (value) => {
    if (value) {
      const portal = availablePortals.value.find(p => p.id === value)
      if (portal) {
        portalContextStore.setCurrentPortal(portal)
      }
    }
  }
})

// 方法
const toggleSider = () => {
  siderCollapsed.value = !siderCollapsed.value
}

const toggleUserMenu = () => {
  userMenuVisible.value = !userMenuVisible.value
}

const handleMenuClick = (keyOrEvent: string | { key: string }) => {
  const key = typeof keyOrEvent === 'string' ? keyOrEvent : keyOrEvent.key
  userMenuVisible.value = false
  switch (key) {
    case 'portal':
      router.push('/portal')
      break
    case 'mcp':
      router.push('/mcp')
      break
    case 'devops':
      router.push('/devops')
      break
    case 'cloudplatform':
      router.push('/cloudplatform')
      break
    case 'portal-overview':
      if (currentPortal.value) {
        router.push(`/portal/${currentPortal.value.id}`)
      }
      break
    case 'query-plans':
      if (currentPortal.value) {
        router.push(`/portal/${currentPortal.value.id}/query-plans`)
      }
      break
    case 'portal-settings':
      if (currentPortal.value) {
        router.push(`/portal/${currentPortal.value.id}/settings`)
      }
      break
    case 'exit-portal-mode':
      portalContextStore.exitPortalMode()
      router.push('/portal')
      break
  }
}

const handlePortalChange = (portalId: number) => {
  const portal = availablePortals.value.find(p => p.id === portalId)
  if (portal) {
    portalContextStore.setCurrentPortal(portal)
    router.push(`/portal/${portalId}`)
  }
}

const handleUserProfile = () => {
  userMenuVisible.value = false
  message.info('个人资料功能开发中')
}

const handleSettings = () => {
  userMenuVisible.value = false
  message.info('系统设置功能开发中')
}

const handleLogout = () => {
  userMenuVisible.value = false
  authStore.logout()
  router.push('/login')
}

const updateSelectedMenuKeys = () => {
  const path = route.path
  
  if (path.startsWith('/portal')) {
    if (isPortalMode.value) {
      if (path.includes('/query-plan')) {
        selectedMenuKeys.value = ['query-plans']
      } else if (path.includes('/settings')) {
        selectedMenuKeys.value = ['portal-settings']
      } else {
        selectedMenuKeys.value = ['portal-overview']
      }
    } else {
      selectedMenuKeys.value = ['portal']
    }
  } else if (path.startsWith('/mcp')) {
    selectedMenuKeys.value = ['mcp']
  } else if (path.startsWith('/devops')) {
    selectedMenuKeys.value = ['devops']
  } else if (path.startsWith('/cloudplatform')) {
    selectedMenuKeys.value = ['cloudplatform']
  }
}

const loadAvailablePortals = async () => {
  try {
    availablePortals.value = await portalStore.getAllPortals()
  } catch (error) {
    console.error('加载门户列表失败:', error)
  }
}

// 生命周期
onMounted(() => {
  updateSelectedMenuKeys()
  loadAvailablePortals()
})

// 监听器
watch(
  () => route.path,
  () => {
    updateSelectedMenuKeys()
  }
)

watch(
  () => route.params,
  (newParams) => {
    // 根据路由参数更新门户上下文
    if (newParams.id && route.path.startsWith('/portal/')) {
      portalContextStore.enterPortalMode()
    }
  }
)
</script>

<style scoped>
.main-layout {
  height: 100vh;
}

.layout-header {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 0;
  height: 64px;
  line-height: 64px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.app-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.logo-icon {
  font-size: 24px;
}

.app-name {
  white-space: nowrap;
}

.breadcrumb-wrapper {
  margin-left: 24px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.portal-switcher {
  display: flex;
  align-items: center;
}

.portal-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.portal-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.user-menu-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
  padding: 0 12px;
}

.layout-body {
  height: calc(100vh - 64px);
  display: flex;
}

.layout-sider {
  background: #fff;
  border-right: 1px solid #f0f0f0;
  flex: 0 0 auto;
}

.sider-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sider-trigger {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s;
}

.sider-trigger:hover {
  background-color: #f5f5f5;
}

.main-menu {
  flex: 1;
  border-right: none;
}

.exit-portal-item {
  color: #666 !important;
}

.layout-content {
  background: #f5f5f5;
  overflow: auto;
  flex: 1;
}

.content-wrapper {
  height: 100%;
  min-height: calc(100vh - 64px);
}

/* 自定义组件样式 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.breadcrumb-link {
  color: #1890ff;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 4px;
}

.breadcrumb-link:hover {
  color: #40a9ff;
}

.breadcrumb-current {
  color: rgba(0, 0, 0, 0.65);
  display: flex;
  align-items: center;
  gap: 4px;
}

.breadcrumb-separator {
  color: rgba(0, 0, 0, 0.45);
  margin: 0 8px;
}

.main-menu {
  padding: 16px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.65);
  transition: all 0.3s;
  border-radius: 0;
}

.menu-item:hover {
  background-color: #f5f5f5;
  color: #1890ff;
}

.menu-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
  border-right: 3px solid #1890ff;
}

.menu-divider {
  height: 1px;
  background: #f0f0f0;
  margin: 8px 24px;
}

.layout-sider.collapsed .menu-item {
  padding: 12px 16px;
  justify-content: center;
}

.layout-sider.collapsed .menu-item span {
  display: none;
}

.user-dropdown {
  min-width: 160px;
}

.portal-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.portal-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
</style>
