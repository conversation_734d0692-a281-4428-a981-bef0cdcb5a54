<template>
  <div class="select-wrapper" ref="selectRef">
    <div 
      :class="selectClasses"
      @click="toggleDropdown"
    >
      <div class="select-selection">
        <div class="select-selection-item" v-if="selectedOption">
          <slot name="option" :option="selectedOption">
            {{ selectedOption.label }}
          </slot>
        </div>
        <div class="select-placeholder" v-else>
          {{ placeholder }}
        </div>
      </div>
      
      <div class="select-arrow">
        <svg class="arrow-icon" :class="{ 'arrow-open': dropdownVisible }" viewBox="0 0 1024 1024">
          <path d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3 0.1-12.7-6.4-12.7z"/>
        </svg>
      </div>
    </div>
    
    <teleport to="body">
      <div 
        v-if="dropdownVisible" 
        class="select-dropdown"
        :style="dropdownStyle"
      >
        <div class="select-dropdown-content">
          <div
            v-for="option in options"
            :key="option.value"
            :class="[
              'select-option',
              { 'select-option-selected': option.value === modelValue }
            ]"
            @click="selectOption(option)"
          >
            <slot name="option" :option="option">
              {{ option.label }}
            </slot>
          </div>
          
          <div v-if="options.length === 0" class="select-empty">
            暂无数据
          </div>
        </div>
      </div>
    </teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

interface SelectOption {
  label: string
  value: any
  disabled?: boolean
  data?: Record<string, any>
}

interface Props {
  modelValue?: any
  options?: SelectOption[]
  placeholder?: string
  disabled?: boolean
  size?: 'small' | 'default' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  options: () => [],
  placeholder: '请选择',
  size: 'default'
})

const emit = defineEmits<{
  'update:modelValue': [value: any]
  change: [value: any, option: SelectOption]
}>()

const selectRef = ref<HTMLElement>()
const dropdownVisible = ref(false)
const dropdownStyle = ref({})

const selectClasses = computed(() => [
  'select-selector',
  `select-${props.size}`,
  {
    'select-focused': dropdownVisible.value,
    'select-disabled': props.disabled
  }
])

const selectedOption = computed(() => {
  return props.options.find(option => option.value === props.modelValue)
})

const toggleDropdown = () => {
  if (props.disabled) return
  
  dropdownVisible.value = !dropdownVisible.value
  
  if (dropdownVisible.value) {
    nextTick(() => {
      updateDropdownPosition()
    })
  }
}

const selectOption = (option: SelectOption) => {
  if (option.disabled) return
  
  emit('update:modelValue', option.value)
  emit('change', option.value, option)
  dropdownVisible.value = false
}

const updateDropdownPosition = () => {
  if (!selectRef.value) return
  
  const rect = selectRef.value.getBoundingClientRect()
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft
  
  dropdownStyle.value = {
    position: 'absolute',
    top: `${rect.bottom + scrollTop}px`,
    left: `${rect.left + scrollLeft}px`,
    width: `${rect.width}px`,
    zIndex: 1050
  }
}

const handleClickOutside = (event: MouseEvent) => {
  if (!selectRef.value?.contains(event.target as Node)) {
    dropdownVisible.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  window.addEventListener('scroll', updateDropdownPosition)
  window.addEventListener('resize', updateDropdownPosition)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('scroll', updateDropdownPosition)
  window.removeEventListener('resize', updateDropdownPosition)
})
</script>

<style scoped>
.select-wrapper {
  position: relative;
  width: 100%;
}

.select-selector {
  display: flex;
  align-items: center;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s;
}

.select-selector:hover:not(.select-disabled) {
  border-color: #4096ff;
}

.select-focused {
  border-color: #4096ff;
  box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
}

.select-disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.25);
}

/* 尺寸 */
.select-small {
  height: 24px;
  padding: 0 7px;
}

.select-default {
  height: 32px;
  padding: 0 11px;
}

.select-large {
  height: 40px;
  padding: 0 11px;
}

.select-selection {
  flex: 1;
  overflow: hidden;
}

.select-selection-item {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.88);
}

.select-placeholder {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.25);
}

.select-arrow {
  margin-left: 8px;
  flex-shrink: 0;
}

.arrow-icon {
  width: 12px;
  height: 12px;
  fill: rgba(0, 0, 0, 0.25);
  transition: transform 0.2s;
}

.arrow-open {
  transform: rotate(180deg);
}

.select-dropdown {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #d9d9d9;
}

.select-dropdown-content {
  max-height: 256px;
  overflow-y: auto;
  padding: 4px 0;
}

.select-option {
  padding: 5px 12px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.88);
  cursor: pointer;
  transition: background-color 0.2s;
}

.select-option:hover {
  background-color: #f5f5f5;
}

.select-option-selected {
  background-color: #e6f4ff;
  color: #1890ff;
  font-weight: 500;
}

.select-empty {
  padding: 5px 12px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.25);
  text-align: center;
}
</style>
