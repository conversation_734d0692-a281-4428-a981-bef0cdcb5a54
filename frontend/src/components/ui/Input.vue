<template>
  <div class="input-wrapper">
    <div :class="inputClasses">
      <slot name="prefix" v-if="$slots.prefix"></slot>
      
      <input
        v-if="!isTextarea"
        ref="inputRef"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :maxlength="maxlength"
        :readonly="readonly"
        class="input-element"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown.enter="handleEnter"
      />
      
      <textarea
        v-else
        ref="textareaRef"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :maxlength="maxlength"
        :readonly="readonly"
        :rows="rows"
        class="input-element textarea-element"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      ></textarea>
      
      <slot name="suffix" v-if="$slots.suffix"></slot>
      
      <!-- 搜索按钮 -->
      <button
        v-if="search"
        class="search-button"
        @click="handleSearch"
      >
        <svg class="search-icon" viewBox="0 0 1024 1024">
          <path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"/>
        </svg>
      </button>
    </div>
    
    <!-- 字符计数 -->
    <div v-if="showCount && maxlength" class="input-count">
      {{ (modelValue || '').length }} / {{ maxlength }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  modelValue?: string
  type?: string
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  maxlength?: number
  showCount?: boolean
  size?: 'small' | 'default' | 'large'
  search?: boolean
  rows?: number
  isTextarea?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  size: 'default',
  rows: 3,
  isTextarea: false
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  search: [value: string]
  pressEnter: [event: KeyboardEvent]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
}>()

const inputRef = ref<HTMLInputElement>()
const textareaRef = ref<HTMLTextAreaElement>()
const focused = ref(false)

const inputClasses = computed(() => [
  'input-container',
  `input-${props.size}`,
  {
    'input-focused': focused.value,
    'input-disabled': props.disabled,
    'input-search': props.search
  }
])

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement | HTMLTextAreaElement
  emit('update:modelValue', target.value)
}

const handleFocus = (event: FocusEvent) => {
  focused.value = true
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  focused.value = false
  emit('blur', event)
}

const handleEnter = (event: KeyboardEvent) => {
  emit('pressEnter', event)
  if (props.search) {
    handleSearch()
  }
}

const handleSearch = () => {
  emit('search', props.modelValue || '')
}
</script>

<style scoped>
.input-wrapper {
  width: 100%;
}

.input-container {
  display: flex;
  align-items: center;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fff;
  transition: all 0.2s;
  position: relative;
}

.input-container:hover:not(.input-disabled) {
  border-color: #4096ff;
}

.input-focused {
  border-color: #4096ff;
  box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
}

.input-disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

/* 尺寸 */
.input-small {
  height: 24px;
  padding: 0 7px;
}

.input-default {
  height: 32px;
  padding: 0 11px;
}

.input-large {
  height: 40px;
  padding: 0 11px;
}

.input-element {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.88);
}

.input-element::placeholder {
  color: rgba(0, 0, 0, 0.25);
}

.input-element:disabled {
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.25);
}

.textarea-element {
  resize: vertical;
  min-height: 32px;
  padding: 4px 0;
  line-height: 1.5;
}

.search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 100%;
  border: none;
  background: #1890ff;
  color: #fff;
  cursor: pointer;
  border-radius: 0 4px 4px 0;
  margin-right: -11px;
  transition: background-color 0.2s;
}

.search-button:hover {
  background: #40a9ff;
}

.search-icon {
  width: 14px;
  height: 14px;
  fill: currentColor;
}

.input-search {
  padding-right: 0;
}

.input-count {
  margin-top: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  text-align: right;
}
</style>
