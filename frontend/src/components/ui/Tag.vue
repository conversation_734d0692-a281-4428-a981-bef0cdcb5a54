<template>
  <span :class="tagClasses">
    <slot></slot>
    <button v-if="closable" class="tag-close" @click="handleClose">
      <svg class="close-icon" viewBox="0 0 1024 1024">
        <path d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3.1-3.6-7.6-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 0 0 203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3.1 3.6 7.6 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"/>
      </svg>
    </button>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  color?: string
  closable?: boolean
  size?: 'small' | 'default'
}

const props = withDefaults(defineProps<Props>(), {
  color: 'default',
  size: 'default'
})

const emit = defineEmits<{
  close: []
}>()

const tagClasses = computed(() => [
  'tag',
  `tag-${props.size}`,
  `tag-${props.color}`,
  {
    'tag-closable': props.closable
  }
])

const handleClose = (event: MouseEvent) => {
  event.stopPropagation()
  emit('close')
}
</script>

<style scoped>
.tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
  white-space: nowrap;
  cursor: default;
  transition: all 0.2s;
}

/* 尺寸 */
.tag-small {
  height: 16px;
  padding: 0 4px;
  font-size: 11px;
}

.tag-default {
  height: 22px;
  padding: 0 7px;
}

/* 颜色 */
.tag-default {
  background-color: #fafafa;
  border-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.88);
}

.tag-blue {
  background-color: #e6f4ff;
  border-color: #91caff;
  color: #1677ff;
}

.tag-green {
  background-color: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.tag-red {
  background-color: #fff2f0;
  border-color: #ffb3b3;
  color: #ff4d4f;
}

.tag-orange {
  background-color: #fff7e6;
  border-color: #ffd591;
  color: #fa8c16;
}

.tag-purple {
  background-color: #f9f0ff;
  border-color: #d3adf7;
  color: #722ed1;
}

.tag-cyan {
  background-color: #e6fffb;
  border-color: #87e8de;
  color: #13c2c2;
}

.tag-magenta {
  background-color: #fff0f6;
  border-color: #ffb3d1;
  color: #eb2f96;
}

.tag-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 12px;
  height: 12px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 2px;
  transition: background-color 0.2s;
  margin-left: 2px;
}

.tag-close:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

.close-icon {
  width: 8px;
  height: 8px;
  fill: currentColor;
  opacity: 0.5;
}

.tag-closable {
  padding-right: 4px;
}
</style>
