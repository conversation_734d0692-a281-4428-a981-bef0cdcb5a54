<template>
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <span v-if="loading" class="loading-spinner"></span>
    <slot name="icon" v-if="!loading"></slot>
    <span v-if="$slots.default" class="button-text">
      <slot></slot>
    </span>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type?: 'primary' | 'default' | 'text' | 'link'
  size?: 'small' | 'default' | 'large'
  disabled?: boolean
  loading?: boolean
  danger?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  size: 'default',
  disabled: false,
  loading: false,
  danger: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonClasses = computed(() => [
  'btn',
  `btn-${props.type}`,
  `btn-${props.size}`,
  {
    'btn-disabled': props.disabled || props.loading,
    'btn-loading': props.loading,
    'btn-danger': props.danger
  }
])

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped>
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  outline: none;
  user-select: none;
}

/* 尺寸 */
.btn-small {
  height: 24px;
  padding: 0 7px;
  font-size: 12px;
}

.btn-default {
  height: 32px;
  padding: 0 15px;
}

.btn-large {
  height: 40px;
  padding: 0 15px;
  font-size: 16px;
}

/* 类型 */
.btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
  color: #fff;
}

.btn-primary:hover:not(.btn-disabled) {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.btn-primary:active:not(.btn-disabled) {
  background-color: #096dd9;
  border-color: #096dd9;
}

.btn-default {
  background-color: #fff;
  border-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.88);
}

.btn-default:hover:not(.btn-disabled) {
  border-color: #4096ff;
  color: #4096ff;
}

.btn-text {
  background-color: transparent;
  border-color: transparent;
  color: rgba(0, 0, 0, 0.88);
}

.btn-text:hover:not(.btn-disabled) {
  background-color: rgba(0, 0, 0, 0.06);
}

.btn-link {
  background-color: transparent;
  border-color: transparent;
  color: #1890ff;
  text-decoration: none;
}

.btn-link:hover:not(.btn-disabled) {
  color: #40a9ff;
}

/* 危险按钮 */
.btn-danger.btn-primary {
  background-color: #ff4d4f;
  border-color: #ff4d4f;
}

.btn-danger.btn-primary:hover:not(.btn-disabled) {
  background-color: #ff7875;
  border-color: #ff7875;
}

.btn-danger.btn-default {
  color: #ff4d4f;
  border-color: #ff4d4f;
}

.btn-danger.btn-default:hover:not(.btn-disabled) {
  background-color: #ff4d4f;
  color: #fff;
}

/* 禁用状态 */
.btn-disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 加载状态 */
.loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.button-text {
  display: inline-block;
}
</style>
