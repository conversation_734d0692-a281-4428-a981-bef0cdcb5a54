<template>
  <div class="split-panel" ref="splitPanelRef">
    <div 
      class="panel-left" 
      :style="{ width: leftWidth + 'px' }"
    >
      <slot name="left"></slot>
    </div>
    
    <div 
      class="resizer" 
      @mousedown="startResizing"
      @dblclick="resetPanel"
    ></div>
    
    <div 
      class="panel-right" 
      :style="{ width: rightWidth + 'px' }"
    >
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface Props {
  initialLeftWidth?: number
  minLeftWidth?: number
  minRightWidth?: number
  resetLeftWidth?: number
}

const props = withDefaults(defineProps<Props>(), {
  initialLeftWidth: 300,
  minLeftWidth: 200,
  minRightWidth: 200,
  resetLeftWidth: 300
})

const splitPanelRef = ref<HTMLElement | null>(null)
const leftWidth = ref(props.initialLeftWidth)
const panelWidth = ref(0)
const isResizing = ref(false)

const rightWidth = computed(() => {
  return panelWidth.value - leftWidth.value
})

const startResizing = (e: MouseEvent) => {
  isResizing.value = true
  e.preventDefault()
}

const resize = (e: MouseEvent) => {
  if (!isResizing.value || !splitPanelRef.value) return
  
  const splitPanelRect = splitPanelRef.value.getBoundingClientRect()
  const newLeftWidth = e.clientX - splitPanelRect.left
  
  // Apply min/max constraints
  if (newLeftWidth >= props.minLeftWidth && 
      (panelWidth.value - newLeftWidth) >= props.minRightWidth) {
    leftWidth.value = newLeftWidth
  }
}

const stopResizing = () => {
  isResizing.value = false
}

const resetPanel = () => {
  leftWidth.value = props.resetLeftWidth
}

const updatePanelWidth = () => {
  if (splitPanelRef.value) {
    panelWidth.value = splitPanelRef.value.offsetWidth
  }
}

onMounted(() => {
  updatePanelWidth()
  window.addEventListener('mousemove', resize)
  window.addEventListener('mouseup', stopResizing)
  window.addEventListener('resize', updatePanelWidth)
})

onUnmounted(() => {
  window.removeEventListener('mousemove', resize)
  window.removeEventListener('mouseup', stopResizing)
  window.removeEventListener('resize', updatePanelWidth)
})
</script>

<style scoped>
.split-panel {
  display: flex;
  flex-direction: row;
  height: 100%;
  width: 100%;
  position: relative;
}

.panel-left {
  height: 100%;
  overflow: auto;
}

.panel-right {
  height: 100%;
  overflow: auto;
  flex: 1;
}

.resizer {
  width: 5px;
  height: 100%;
  background: #e5e7eb;
  cursor: col-resize;
  transition: background 0.2s;
  position: relative;
}

.resizer:hover {
  background: #94a3b8;
}

.resizer::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1px;
  height: 30px;
  background: #94a3b8;
  box-shadow: 1px 0 0 #94a3b8, -1px 0 0 #94a3b8;
}
</style>