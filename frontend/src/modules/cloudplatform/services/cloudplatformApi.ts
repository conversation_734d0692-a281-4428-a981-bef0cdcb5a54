/**
 * 云平台管理模块的API服务层
 * 封装所有与云平台管理相关的API调用
 */

import type {
  CloudPlatformDTO,
  CreateCloudPlatformRequest,
  UpdateCloudPlatformRequest,
  ConnectionTestResult,
  PlatformTypeDTO,
  CloudPlatformStatistics,
  CloudPlatformQueryParams,
  PageResponse,
  ApiResponse
} from '@/modules/cloudplatform/types/cloudplatform'
import apiClient from '@/utils/axios'

// API基础配置
const API_BASE_URL = '/api/cloud-platforms'

// 云平台管理API
export const cloudPlatformApi = {
  // 获取云平台列表（分页）
  async getList(params?: CloudPlatformQueryParams): Promise<ApiResponse<PageResponse<CloudPlatformDTO>>> {
    const response = await apiClient.get<ApiResponse<PageResponse<CloudPlatformDTO>>>(`${API_BASE_URL}`, { params })
    return response.data
  },

  // 获取所有云平台（不分页）
  async getAll(): Promise<ApiResponse<CloudPlatformDTO[]>> {
    const response = await apiClient.get<ApiResponse<CloudPlatformDTO[]>>(`${API_BASE_URL}/all`)
    return response.data
  },

  // 根据ID获取云平台详情
  async getById(id: number): Promise<ApiResponse<CloudPlatformDTO>> {
    const response = await apiClient.get<ApiResponse<CloudPlatformDTO>>(`${API_BASE_URL}/${id}`)
    return response.data
  },

  // 创建云平台
  async create(data: CreateCloudPlatformRequest): Promise<ApiResponse<CloudPlatformDTO>> {
    const response = await apiClient.post<ApiResponse<CloudPlatformDTO>>(`${API_BASE_URL}`, data)
    return response.data
  },

  // 更新云平台
  async update(id: number, data: UpdateCloudPlatformRequest): Promise<ApiResponse<CloudPlatformDTO>> {
    const response = await apiClient.put<ApiResponse<CloudPlatformDTO>>(`${API_BASE_URL}/${id}`, data)
    return response.data
  },

  // 删除云平台
  async delete(id: number): Promise<ApiResponse<void>> {
    const response = await apiClient.delete<ApiResponse<void>>(`${API_BASE_URL}/${id}`)
    return response.data
  },

  // 批量删除云平台
  async batchDelete(ids: number[]): Promise<ApiResponse<void>> {
    const response = await apiClient.delete<ApiResponse<void>>(`${API_BASE_URL}/batch`, { data: { ids } })
    return response.data
  },

  // 测试云平台连接
  async testConnection(id: number): Promise<ApiResponse<ConnectionTestResult>> {
    const response = await apiClient.post<ApiResponse<ConnectionTestResult>>(`${API_BASE_URL}/${id}/test-connection`)
    return response.data
  },

  // 批量测试连接
  async batchTestConnection(ids: number[]): Promise<ApiResponse<ConnectionTestResult[]>> {
    const response = await apiClient.post<ApiResponse<ConnectionTestResult[]>>(`${API_BASE_URL}/batch-test`, { ids })
    return response.data
  },

  // 获取支持的平台类型
  async getPlatformTypes(): Promise<ApiResponse<PlatformTypeDTO[]>> {
    const response = await apiClient.get<ApiResponse<PlatformTypeDTO[]>>(`${API_BASE_URL}/types`)
    return response.data
  },

  // 获取统计信息
  async getStatistics(): Promise<ApiResponse<CloudPlatformStatistics>> {
    const response = await apiClient.get<ApiResponse<CloudPlatformStatistics>>(`${API_BASE_URL}/statistics`)
    return response.data
  },

  // 根据类型获取云平台列表
  async getByType(type: string): Promise<ApiResponse<CloudPlatformDTO[]>> {
    const response = await apiClient.get<ApiResponse<CloudPlatformDTO[]>>(`${API_BASE_URL}/type/${type}`)
    return response.data
  },

  // 根据状态获取云平台列表
  async getByStatus(status: string): Promise<ApiResponse<CloudPlatformDTO[]>> {
    const response = await apiClient.get<ApiResponse<CloudPlatformDTO[]>>(`${API_BASE_URL}/status/${status}`)
    return response.data
  },

  // 根据区域获取云平台列表
  async getByRegion(region: string): Promise<ApiResponse<CloudPlatformDTO[]>> {
    const response = await apiClient.get<ApiResponse<CloudPlatformDTO[]>>(`${API_BASE_URL}/region/${region}`)
    return response.data
  },

  // 搜索云平台
  async search(keyword: string): Promise<ApiResponse<CloudPlatformDTO[]>> {
    const response = await apiClient.get<ApiResponse<CloudPlatformDTO[]>>(`${API_BASE_URL}/search`, {
      params: { keyword }
    })
    return response.data
  },

  // 导出云平台配置
  async export(ids?: number[]): Promise<Blob> {
    const response = await apiClient.get(`${API_BASE_URL}/export`, {
      params: ids ? { ids: ids.join(',') } : {},
      responseType: 'blob'
    })
    return response.data
  },

  // 导入云平台配置
  async import(file: File): Promise<ApiResponse<{ success: number; failed: number; errors: string[] }>> {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await apiClient.post<ApiResponse<{ success: number; failed: number; errors: string[] }>>(
      `${API_BASE_URL}/import`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    )
    return response.data
  },

  // 同步云平台状态
  async syncStatus(id: number): Promise<ApiResponse<CloudPlatformDTO>> {
    const response = await apiClient.post<ApiResponse<CloudPlatformDTO>>(`${API_BASE_URL}/${id}/sync`)
    return response.data
  },

  // 批量同步状态
  async batchSyncStatus(ids: number[]): Promise<ApiResponse<CloudPlatformDTO[]>> {
    const response = await apiClient.post<ApiResponse<CloudPlatformDTO[]>>(`${API_BASE_URL}/batch-sync`, { ids })
    return response.data
  },

  // 获取云平台健康状态
  async getHealthStatus(id: number): Promise<ApiResponse<{ healthy: boolean; details: Record<string, any> }>> {
    const response = await apiClient.get<ApiResponse<{ healthy: boolean; details: Record<string, any> }>>(`${API_BASE_URL}/${id}/health`)
    return response.data
  },

  // 获取云平台资源使用情况
  async getResourceUsage(id: number): Promise<ApiResponse<Record<string, any>>> {
    const response = await apiClient.get<ApiResponse<Record<string, any>>>(`${API_BASE_URL}/${id}/resources`)
    return response.data
  }
}

// 导出默认API实例
export default cloudPlatformApi