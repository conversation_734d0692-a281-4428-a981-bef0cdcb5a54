<template>
  <div class="cloud-platform-dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1>云平台仪表板</h1>
          <p>云平台管理概览和统计信息</p>
        </div>
        <div class="header-actions">
          <button @click="refreshData" :disabled="loading.statistics" class="btn btn-secondary">
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
            </svg>
            刷新数据
          </button>
          <router-link to="/cloud-platforms" class="btn btn-primary">
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
            </svg>
            管理云平台
          </router-link>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card total">
          <div class="stat-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics?.totalCount || 0 }}</div>
            <div class="stat-label">总平台数</div>
          </div>
        </div>

        <div class="stat-card connected">
          <div class="stat-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics?.connectedCount || 0 }}</div>
            <div class="stat-label">已连接</div>
          </div>
        </div>

        <div class="stat-card disconnected">
          <div class="stat-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics?.disconnectedCount || 0 }}</div>
            <div class="stat-label">未连接</div>
          </div>
        </div>

        <div class="stat-card error">
          <div class="stat-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics?.errorCount || 0 }}</div>
            <div class="stat-label">异常</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="charts-grid">
        <!-- 平台类型分布 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>平台类型分布</h3>
          </div>
          <div class="chart-body">
            <div v-if="typeDistributionData.length === 0" class="empty-chart">
              <svg class="empty-icon" viewBox="0 0 20 20" fill="currentColor">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
              </svg>
              <p>暂无数据</p>
            </div>
            <div v-else class="distribution-list">
              <div
                v-for="item in typeDistributionData"
                :key="item.type"
                class="distribution-item"
              >
                <div class="distribution-info">
                  <span class="distribution-label">{{ item.name }}</span>
                  <span class="distribution-count">{{ item.count }}</span>
                </div>
                <div class="distribution-bar">
                  <div
                    class="distribution-fill"
                    :style="{ width: `${item.percentage}%` }"
                    :class="item.type.toLowerCase()"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 区域分布 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>区域分布</h3>
          </div>
          <div class="chart-body">
            <div v-if="regionDistributionData.length === 0" class="empty-chart">
              <svg class="empty-icon" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M12 1.586l-4 4v12.828l4-4V1.586zM3.707 3.293A1 1 0 002 4v10a1 1 0 00.293.707L6 18.414V5.586L3.707 3.293zM17.707 5.293L14 1.586v12.828l2.293 2.293A1 1 0 0018 16V6a1 1 0 00-.293-.707z" clip-rule="evenodd" />
              </svg>
              <p>暂无数据</p>
            </div>
            <div v-else class="distribution-list">
              <div
                v-for="item in regionDistributionData"
                :key="item.region"
                class="distribution-item"
              >
                <div class="distribution-info">
                  <span class="distribution-label">{{ item.region || '未指定' }}</span>
                  <span class="distribution-count">{{ item.count }}</span>
                </div>
                <div class="distribution-bar">
                  <div
                    class="distribution-fill region"
                    :style="{ width: `${item.percentage}%` }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="activity-section">
      <div class="activity-card">
        <div class="card-header">
          <h3>最近添加的云平台</h3>
          <router-link to="/cloud-platforms" class="view-all-link">
            查看全部
          </router-link>
        </div>
        <div class="card-body">
          <div v-if="loading.list" class="loading-state">
            <div class="loading-spinner">加载中...</div>
          </div>
          <div v-else-if="recentPlatforms.length === 0" class="empty-state">
            <svg class="empty-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
            </svg>
            <p>暂无云平台</p>
            <router-link to="/cloud-platforms" class="btn btn-primary">
              添加第一个云平台
            </router-link>
          </div>
          <div v-else class="platforms-list">
            <div
              v-for="platform in recentPlatforms"
              :key="platform.id"
              class="platform-item"
              @click="$router.push(`/cloud-platforms/${platform.id}`)"
            >
              <div class="platform-info">
                <div class="platform-name">{{ platform.name }}</div>
                <div class="platform-meta">
                  <span class="platform-type" :class="platform.type.toLowerCase()">
                    {{ getPlatformTypeName(platform.type) }}
                  </span>
                  <span class="platform-url">{{ platform.url }}</span>
                </div>
              </div>
              <div class="platform-status">
                <span class="status-badge" :class="platform.status.toLowerCase()">
                  {{ getStatusText(platform.status) }}
                </span>
                <div class="platform-time">
                  {{ formatDate(platform.createdAt) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useCloudPlatformStore } from '@/modules/cloudplatform/stores/cloudplatform'

// Store
const store = useCloudPlatformStore()

// 响应式数据
const {
  platforms,
  statistics,
  platformTypes,
  loading,
  errors
} = storeToRefs(store)

// 计算属性
const recentPlatforms = computed(() => {
  return platforms.value
    .slice()
    .sort((a, b) => new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime())
    .slice(0, 5)
})

const typeDistributionData = computed(() => {
  if (!statistics.value?.typeDistribution) return []
  
  const total = statistics.value.totalCount
  return Object.entries(statistics.value.typeDistribution)
    .map(([type, count]) => ({
      type,
      name: getPlatformTypeName(type),
      count,
      percentage: total > 0 ? (count / total) * 100 : 0
    }))
    .sort((a, b) => b.count - a.count)
})

const regionDistributionData = computed(() => {
  if (!statistics.value?.regionDistribution) return []
  
  const total = statistics.value.totalCount
  return Object.entries(statistics.value.regionDistribution)
    .map(([region, count]) => ({
      region,
      count,
      percentage: total > 0 ? (count / total) * 100 : 0
    }))
    .sort((a, b) => b.count - a.count)
})

// 方法
const refreshData = async () => {
  await Promise.all([
    store.fetchPlatforms({ page: 0, size: 10 }),
    store.fetchStatistics(),
    store.fetchPlatformTypes()
  ])
}

const getPlatformTypeName = (type: string) => {
  const platformType = platformTypes.value.find(t => t.code === type)
  return platformType?.displayName || type
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'CONNECTED': '已连接',
    'DISCONNECTED': '未连接',
    'ERROR': '异常',
    'TESTING': '测试中'
  }
  return statusMap[status] || status
}

const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.cloud-platform-dashboard {
  padding: 24px;
}

.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-title h1 {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: #1f2937;
}

.header-title p {
  color: #6b7280;
  margin: 0;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-section {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.2s;
}

.stat-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon svg {
  width: 28px;
  height: 28px;
}

.stat-card.total .stat-icon {
  background: #dbeafe;
  color: #2563eb;
}

.stat-card.connected .stat-icon {
  background: #dcfce7;
  color: #16a34a;
}

.stat-card.disconnected .stat-icon {
  background: #fef3c7;
  color: #d97706;
}

.stat-card.error .stat-icon {
  background: #fee2e2;
  color: #dc2626;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.charts-section {
  margin-bottom: 32px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.chart-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.chart-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.chart-body {
  padding: 24px;
}

.empty-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #9ca3af;
}

.empty-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 12px;
}

.empty-chart p {
  margin: 0;
  font-size: 14px;
}

.distribution-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.distribution-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.distribution-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.distribution-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.distribution-count {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.distribution-bar {
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.distribution-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.distribution-fill.kubernetes {
  background: #2563eb;
}

.distribution-fill.docker {
  background: #3730a3;
}

.distribution-fill.region {
  background: #059669;
}

.activity-section {
  margin-bottom: 32px;
}

.activity-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.card-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.view-all-link {
  color: #2563eb;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

.view-all-link:hover {
  color: #1d4ed8;
}

.card-body {
  padding: 24px;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6b7280;
}

.loading-spinner {
  font-size: 14px;
}

.empty-state {
  gap: 16px;
}

.platforms-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.platform-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.platform-item:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.platform-info {
  flex: 1;
}

.platform-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.platform-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
}

.platform-type {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 500;
  text-transform: uppercase;
}

.platform-type.kubernetes {
  background: #dbeafe;
  color: #1d4ed8;
}

.platform-type.docker {
  background: #e0e7ff;
  color: #3730a3;
}

.platform-url {
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', monospace;
}

.platform-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.connected {
  background: #dcfce7;
  color: #16a34a;
}

.status-badge.disconnected {
  background: #fef3c7;
  color: #d97706;
}

.status-badge.error {
  background: #fee2e2;
  color: #dc2626;
}

.platform-time {
  font-size: 12px;
  color: #9ca3af;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.btn-primary:hover:not(:disabled) {
  background: #1d4ed8;
}

.btn-secondary {
  background: white;
  color: #374151;
  border-color: #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background: #f9fafb;
}

.btn-icon {
  width: 16px;
  height: 16px;
}
</style>
