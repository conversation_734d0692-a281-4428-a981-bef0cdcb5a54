<template>
  <div class="cloud-platform-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <div class="title-row">
            <h1>{{ platform?.name || '云平台详情' }}</h1>
            <span class="status-badge" :class="platform?.status.toLowerCase()">
              {{ getStatusText(platform?.status) }}
            </span>
          </div>
          <p>{{ platform?.description || '暂无描述' }}</p>
        </div>
        <div class="header-actions">
          <button
            @click="handleTest"
            :disabled="loading.test"
            class="btn btn-secondary"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd" />
            </svg>
            {{ loading.test ? '测试中...' : '测试连接' }}
          </button>
          <button
            @click="handleEdit"
            class="btn btn-primary"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.828-2.828z" />
            </svg>
            编辑
          </button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading.detail" class="loading-container">
      <div class="loading-spinner">
        <svg class="animate-spin" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.25"/>
          <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
        </svg>
        <span>加载中...</span>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="errors.detail" class="error-container">
      <div class="error-content">
        <svg class="error-icon" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
        </svg>
        <h3>加载失败</h3>
        <p>{{ errors.detail }}</p>
        <button @click="refreshData" class="btn btn-primary">重试</button>
      </div>
    </div>

    <!-- 详情内容 -->
    <div v-else-if="platform" class="detail-content">
      <!-- 基本信息卡片 -->
      <div class="info-card">
        <div class="card-header">
          <h2>基本信息</h2>
        </div>
        <div class="card-body">
          <div class="info-grid">
            <div class="info-item">
              <label>平台名称</label>
              <div class="info-value">{{ platform.name }}</div>
            </div>
            <div class="info-item">
              <label>平台类型</label>
              <div class="info-value">
                <span class="platform-type" :class="platform.type.toLowerCase()">
                  {{ getPlatformTypeName(platform.type) }}
                </span>
              </div>
            </div>
            <div class="info-item">
              <label>连接地址</label>
              <div class="info-value">
                <code class="platform-url">{{ platform.url }}</code>
              </div>
            </div>
            <div class="info-item">
              <label>区域</label>
              <div class="info-value">{{ platform.region || '-' }}</div>
            </div>
            <div class="info-item">
              <label>版本</label>
              <div class="info-value">{{ platform.version || '-' }}</div>
            </div>
            <div class="info-item">
              <label>连接状态</label>
              <div class="info-value">
                <span class="status-badge" :class="platform.status.toLowerCase()">
                  {{ getStatusText(platform.status) }}
                </span>
              </div>
            </div>
            <div class="info-item">
              <label>创建时间</label>
              <div class="info-value">{{ formatDate(platform.createdAt) }}</div>
            </div>
            <div class="info-item">
              <label>更新时间</label>
              <div class="info-value">{{ formatDate(platform.updatedAt) }}</div>
            </div>
          </div>
          
          <div v-if="platform.description" class="info-item full-width">
            <label>描述信息</label>
            <div class="info-value">{{ platform.description }}</div>
          </div>
          
          <div v-if="platform.tags && Object.keys(platform.tags).length > 0" class="info-item full-width">
            <label>标签</label>
            <div class="info-value">
              <div class="tags-list">
                <span v-for="(value, key) in platform.tags" :key="key" class="tag">
                  {{ key }}: {{ value }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 连接测试结果 -->
      <div v-if="testResult" class="info-card">
        <div class="card-header">
          <h2>连接测试</h2>
          <span class="test-time">{{ formatDate(testResult.timestamp) }}</span>
        </div>
        <div class="card-body">
          <div class="test-result" :class="testResult.success ? 'success' : 'error'">
            <div class="test-result-header">
              <svg v-if="testResult.success" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <svg v-else viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
              <span>{{ testResult.success ? '连接成功' : '连接失败' }}</span>
            </div>
            <div v-if="testResult.message" class="test-result-message">
              {{ testResult.message }}
            </div>
            <div class="test-result-details">
              <div v-if="testResult.version" class="detail-item">
                <strong>版本:</strong> {{ testResult.version }}
              </div>
              <div v-if="testResult.responseTime" class="detail-item">
                <strong>响应时间:</strong> {{ testResult.responseTime }}ms
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 健康状态 -->
      <div v-if="healthStatus" class="info-card">
        <div class="card-header">
          <h2>健康状态</h2>
          <span class="health-badge" :class="healthStatus.healthy ? 'healthy' : 'unhealthy'">
            {{ healthStatus.healthy ? '健康' : '异常' }}
          </span>
        </div>
        <div class="card-body">
          <div class="health-details">
            <pre>{{ JSON.stringify(healthStatus.details, null, 2) }}</pre>
          </div>
        </div>
      </div>

      <!-- 资源使用情况 -->
      <div v-if="resourceUsage" class="info-card">
        <div class="card-header">
          <h2>资源使用情况</h2>
        </div>
        <div class="card-body">
          <div class="resource-details">
            <pre>{{ JSON.stringify(resourceUsage, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模态框 -->
    <CloudPlatformModal
      v-if="showEditModal"
      :visible="showEditModal"
      mode="edit"
      :platform="platform"
      :platform-types="platformTypes"
      @close="showEditModal = false"
      @submit="handleEditSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useCloudPlatformStore } from '@/modules/cloudplatform/stores/cloudplatform'
import CloudPlatformModal from '@/modules/cloudplatform/components/CloudPlatformModal.vue'

// Router
const route = useRoute()
const router = useRouter()

// Store
const store = useCloudPlatformStore()

// 响应式数据
const {
  currentPlatform: platform,
  platformTypes,
  connectionTestResults,
  loading,
  errors
} = storeToRefs(store)

// 本地状态
const showEditModal = ref(false)
const healthStatus = ref<{ healthy: boolean; details: Record<string, any> } | null>(null)
const resourceUsage = ref<Record<string, any> | null>(null)

// 计算属性
const platformId = computed(() => {
  const id = route.params.id
  return typeof id === 'string' ? parseInt(id, 10) : null
})

const testResult = computed(() => {
  if (!platformId.value) return null
  return connectionTestResults.value.get(platformId.value) || null
})

// 方法
const refreshData = async () => {
  if (!platformId.value) return
  
  try {
    await Promise.all([
      store.fetchPlatformById(platformId.value),
      store.fetchPlatformTypes()
    ])
    
    // 获取健康状态和资源使用情况
    await loadAdditionalData()
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

const loadAdditionalData = async () => {
  if (!platformId.value) return
  
  try {
    // 这里调用健康状态和资源使用情况的API
    // 暂时使用模拟数据
    healthStatus.value = {
      healthy: true,
      details: {
        nodes: 3,
        pods: 45,
        services: 12,
        lastCheck: new Date().toISOString()
      }
    }
    
    resourceUsage.value = {
      cpu: { used: '2.5', total: '8', percentage: 31.25 },
      memory: { used: '4.2GB', total: '16GB', percentage: 26.25 },
      storage: { used: '120GB', total: '500GB', percentage: 24 }
    }
  } catch (error) {
    console.error('加载附加数据失败:', error)
  }
}

const handleTest = async () => {
  if (!platformId.value) return
  
  try {
    await store.testConnection(platformId.value)
  } catch (error) {
    console.error('连接测试失败:', error)
  }
}

const handleEdit = () => {
  showEditModal.value = true
}

const handleEditSubmit = async () => {
  showEditModal.value = false
  await refreshData()
}

const getPlatformTypeName = (type: string) => {
  const platformType = platformTypes.value.find(t => t.code === type)
  return platformType?.displayName || type
}

const getStatusText = (status?: string) => {
  if (!status) return '-'
  const statusMap: Record<string, string> = {
    'CONNECTED': '已连接',
    'DISCONNECTED': '未连接',
    'ERROR': '异常',
    'TESTING': '测试中'
  }
  return statusMap[status] || status
}

const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 监听路由参数变化
watch(() => route.params.id, () => {
  refreshData()
})

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.cloud-platform-detail {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.header-title h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #1f2937;
}

.header-title p {
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: #6b7280;
}

.loading-spinner svg {
  width: 32px;
  height: 32px;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.error-icon {
  width: 48px;
  height: 48px;
  color: #dc2626;
}

.error-content h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.error-content p {
  color: #6b7280;
  margin: 0;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-card {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.card-header h2 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.test-time {
  color: #6b7280;
  font-size: 12px;
}

.health-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.health-badge.healthy {
  background: #dcfce7;
  color: #16a34a;
}

.health-badge.unhealthy {
  background: #fee2e2;
  color: #dc2626;
}

.card-body {
  padding: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.info-value {
  font-size: 14px;
  color: #1f2937;
}

.platform-type {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.platform-type.kubernetes {
  background: #dbeafe;
  color: #1d4ed8;
}

.platform-type.docker {
  background: #e0e7ff;
  color: #3730a3;
}

.platform-url {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  background: #f3f4f6;
  padding: 4px 6px;
  border-radius: 3px;
  word-break: break-all;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.connected {
  background: #dcfce7;
  color: #16a34a;
}

.status-badge.disconnected {
  background: #fef3c7;
  color: #d97706;
}

.status-badge.error {
  background: #fee2e2;
  color: #dc2626;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag {
  display: inline-block;
  background: #eff6ff;
  color: #1d4ed8;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.test-result {
  border-radius: 6px;
  padding: 16px;
  border: 1px solid;
}

.test-result.success {
  background: #f0fdf4;
  border-color: #bbf7d0;
  color: #166534;
}

.test-result.error {
  background: #fef2f2;
  border-color: #fecaca;
  color: #991b1b;
}

.test-result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  margin-bottom: 8px;
}

.test-result-header svg {
  width: 16px;
  height: 16px;
}

.test-result-message {
  font-size: 14px;
  margin-bottom: 8px;
}

.test-result-details {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.detail-item {
  font-size: 12px;
}

.health-details,
.resource-details {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
}

.health-details pre,
.resource-details pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  color: #374151;
  white-space: pre-wrap;
  word-break: break-word;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.btn-primary:hover:not(:disabled) {
  background: #1d4ed8;
}

.btn-secondary {
  background: white;
  color: #374151;
  border-color: #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background: #f9fafb;
}

.btn-icon {
  width: 16px;
  height: 16px;
}
</style>
