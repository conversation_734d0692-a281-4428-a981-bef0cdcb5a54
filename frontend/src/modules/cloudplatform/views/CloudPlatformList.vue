<template>
  <div class="cloud-platform-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1>云平台管理</h1>
          <p>管理和配置多云平台连接，支持Kubernetes、<PERSON><PERSON>、云服务商等</p>
        </div>
        <div class="header-actions">
          <button
            @click="showCreateModal = true"
            class="btn btn-primary"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            添加云平台
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid" v-if="statistics">
      <div class="stat-card">
        <div class="stat-icon connected">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.connectedCount }}</div>
          <div class="stat-label">已连接</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon disconnected">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.disconnectedCount }}</div>
          <div class="stat-label">未连接</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon error">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.errorCount }}</div>
          <div class="stat-label">异常</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon total">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.totalCount }}</div>
          <div class="stat-label">总计</div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-item">
          <label>平台类型</label>
          <select v-model="queryParams.type" @change="handleFilter">
            <option value="">全部类型</option>
            <option v-for="type in platformTypes" :key="type.code" :value="type.code">
              {{ type.displayName }}
            </option>
          </select>
        </div>
        
        <div class="filter-item">
          <label>连接状态</label>
          <select v-model="queryParams.status" @change="handleFilter">
            <option value="">全部状态</option>
            <option value="CONNECTED">已连接</option>
            <option value="DISCONNECTED">未连接</option>
            <option value="ERROR">异常</option>
          </select>
        </div>
        
        <div class="filter-item">
          <label>区域</label>
          <input
            type="text"
            v-model="queryParams.region"
            placeholder="输入区域名称"
            @input="handleFilter"
          />
        </div>
        
        <div class="filter-item">
          <label>关键词</label>
          <input
            type="text"
            v-model="queryParams.keyword"
            placeholder="搜索名称或描述"
            @input="handleFilter"
          />
        </div>
        
        <div class="filter-actions">
          <button @click="resetFilter" class="btn btn-secondary">重置</button>
          <button @click="refreshData" class="btn btn-secondary">刷新</button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <div class="table-header">
        <div class="table-title">云平台列表</div>
        <div class="table-actions">
          <button
            @click="handleBatchTest"
            :disabled="selectedPlatforms.length === 0 || loading.test"
            class="btn btn-secondary"
          >
            批量测试
          </button>
          <button
            @click="handleBatchDelete"
            :disabled="selectedPlatforms.length === 0"
            class="btn btn-danger"
          >
            批量删除
          </button>
        </div>
      </div>
      
      <table class="data-table">
        <thead>
          <tr>
            <th>
              <input
                type="checkbox"
                :checked="isAllSelected"
                @change="handleSelectAll"
              />
            </th>
            <th>名称</th>
            <th>类型</th>
            <th>地址</th>
            <th>区域</th>
            <th>状态</th>
            <th>版本</th>
            <th>创建时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading.list">
            <td colspan="9" class="loading-row">
              <div class="loading-spinner">加载中...</div>
            </td>
          </tr>
          <tr v-else-if="platforms.length === 0">
            <td colspan="9" class="empty-row">
              <div class="empty-state">
                <svg class="empty-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                </svg>
                <p>暂无云平台数据</p>
                <button @click="showCreateModal = true" class="btn btn-primary">
                  添加第一个云平台
                </button>
              </div>
            </td>
          </tr>
          <tr
            v-else
            v-for="platform in platforms"
            :key="platform.id"
            :class="{ selected: selectedPlatforms.includes(platform.id!) }"
          >
            <td>
              <input
                type="checkbox"
                :checked="selectedPlatforms.includes(platform.id!)"
                @change="handleSelectPlatform(platform.id!)"
              />
            </td>
            <td>
              <div class="platform-name">
                <strong>{{ platform.name }}</strong>
                <div class="platform-description">{{ platform.description }}</div>
              </div>
            </td>
            <td>
              <span class="platform-type" :class="platform.type.toLowerCase()">
                {{ getPlatformTypeName(platform.type) }}
              </span>
            </td>
            <td>
              <code class="platform-url">{{ platform.url }}</code>
            </td>
            <td>{{ platform.region || '-' }}</td>
            <td>
              <span class="status-badge" :class="platform.status.toLowerCase()">
                {{ getStatusText(platform.status) }}
              </span>
            </td>
            <td>{{ platform.version || '-' }}</td>
            <td>{{ formatDate(platform.createdAt) }}</td>
            <td>
              <div class="action-buttons">
                <button
                  @click="handleTest(platform.id!)"
                  :disabled="loading.test"
                  class="btn btn-sm btn-secondary"
                  title="测试连接"
                >
                  测试
                </button>
                <button
                  @click="handleEdit(platform)"
                  class="btn btn-sm btn-primary"
                  title="编辑"
                >
                  编辑
                </button>
                <button
                  @click="handleDelete(platform.id!)"
                  class="btn btn-sm btn-danger"
                  title="删除"
                >
                  删除
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      
      <!-- 分页 -->
      <div class="pagination" v-if="pagination.total > 0">
        <div class="pagination-info">
          共 {{ pagination.total }} 条记录，第 {{ pagination.page + 1 }} 页
        </div>
        <div class="pagination-controls">
          <button
            @click="handlePageChange(pagination.page - 1)"
            :disabled="!pagination.hasPrevious"
            class="btn btn-sm btn-secondary"
          >
            上一页
          </button>
          <button
            @click="handlePageChange(pagination.page + 1)"
            :disabled="!pagination.hasNext"
            class="btn btn-sm btn-secondary"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 创建/编辑模态框 -->
    <CloudPlatformModal
      v-if="showCreateModal || showEditModal"
      :visible="showCreateModal || showEditModal"
      :mode="showCreateModal ? 'create' : 'edit'"
      :platform="editingPlatform"
      :platform-types="platformTypes"
      @close="handleModalClose"
      @submit="handleModalSubmit"
    />

    <!-- 删除确认模态框 -->
    <ConfirmModal
      v-if="showDeleteModal"
      :visible="showDeleteModal"
      title="确认删除"
      :message="deleteMessage"
      @confirm="confirmDelete"
      @cancel="showDeleteModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useCloudPlatformStore } from '@/modules/cloudplatform/stores/cloudplatform'
import type { CloudPlatformDTO, CloudPlatformQueryParams } from '@/modules/cloudplatform/types/cloudplatform'
import CloudPlatformModal from '@/modules/cloudplatform/components/CloudPlatformModal.vue'
import ConfirmModal from '@/components/ConfirmDialog.vue'
import { storeToRefs } from 'pinia'

// Store
const store = useCloudPlatformStore()

// 响应式数据
const {
  platforms,
  statistics,
  platformTypes,
  pagination,
  loading
} = storeToRefs(store)

// 本地状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteModal = ref(false)
const editingPlatform = ref<CloudPlatformDTO | null>(null)
const deletingPlatformId = ref<number | null>(null)
const selectedPlatforms = ref<number[]>([])

// 查询参数
const queryParams = ref<CloudPlatformQueryParams>({
  page: 0,
  size: 10,
  sortBy: 'createdAt',
  sortDirection: 'DESC'
})

// 计算属性
const isAllSelected = computed(() => {
  return platforms.value.length > 0 && 
         selectedPlatforms.value.length === platforms.value.length
})

const deleteMessage = computed(() => {
  if (selectedPlatforms.value.length > 1) {
    return `确定要删除选中的 ${selectedPlatforms.value.length} 个云平台吗？此操作不可撤销。`
  } else {
    const platform = platforms.value.find(p => p.id === deletingPlatformId.value)
    return `确定要删除云平台 "${platform?.name}" 吗？此操作不可撤销。`
  }
})

watch(platforms, () => {
  if (platforms.value.length === 0) {
    showCreateModal.value = true
  }
})

// 方法
const refreshData = async () => {
  await Promise.all([
    store.fetchPlatforms(queryParams.value),
    store.fetchStatistics(),
    store.fetchPlatformTypes()
  ])
}

const handleFilter = () => {
  queryParams.value.page = 0
  store.fetchPlatforms(queryParams.value)
}

const resetFilter = () => {
  queryParams.value = {
    page: 0,
    size: 10,
    sortBy: 'createdAt',
    sortDirection: 'DESC'
  }
  store.fetchPlatforms(queryParams.value)
}

const handlePageChange = (page: number) => {
  queryParams.value.page = page
  store.fetchPlatforms(queryParams.value)
}

const handleSelectAll = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.checked) {
    selectedPlatforms.value = platforms.value.map(p => p.id!).filter(id => id !== undefined)
  } else {
    selectedPlatforms.value = []
  }
}

const handleSelectPlatform = (id: number) => {
  const index = selectedPlatforms.value.indexOf(id)
  if (index > -1) {
    selectedPlatforms.value.splice(index, 1)
  } else {
    selectedPlatforms.value.push(id)
  }
}

const handleTest = async (id: number) => {
  try {
    await store.testConnection(id)
    // 可以显示测试结果的通知
  } catch (error) {
    console.error('连接测试失败:', error)
  }
}

const handleBatchTest = async () => {
  for (const id of selectedPlatforms.value) {
    try {
      await store.testConnection(id)
    } catch (error) {
      console.error(`平台 ${id} 连接测试失败:`, error)
    }
  }
}

const handleEdit = (platform: CloudPlatformDTO) => {
  editingPlatform.value = platform
  showEditModal.value = true
}

const handleDelete = (id: number) => {
  deletingPlatformId.value = id
  selectedPlatforms.value = [id]
  showDeleteModal.value = true
}

const handleBatchDelete = () => {
  showDeleteModal.value = true
}

const confirmDelete = async () => {
  try {
    if (selectedPlatforms.value.length === 1) {
      await store.deletePlatform(selectedPlatforms.value[0])
    } else {
      // 批量删除
      for (const id of selectedPlatforms.value) {
        await store.deletePlatform(id)
      }
    }
    selectedPlatforms.value = []
    showDeleteModal.value = false
  } catch (error) {
    console.error('删除失败:', error)
  }
}

const handleModalClose = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingPlatform.value = null
}

const handleModalSubmit = async () => {
  handleModalClose()
  await refreshData()
}

const getPlatformTypeName = (type: string) => {
  const platformType = platformTypes.value.find(t => t.code === type)
  return platformType?.displayName || type
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'CONNECTED': '已连接',
    'DISCONNECTED': '未连接',
    'ERROR': '异常',
    'TESTING': '测试中'
  }
  return statusMap[status] || status
}

const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  refreshData()
})

// 监听查询参数变化
watch(queryParams, (newParams) => {
  store.queryParams = newParams
}, { deep: true })
</script>

<style scoped>
.cloud-platform-list {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-title h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1f2937;
}

.header-title p {
  color: #6b7280;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon svg {
  width: 24px;
  height: 24px;
}

.stat-icon.connected {
  background: #dcfce7;
  color: #16a34a;
}

.stat-icon.disconnected {
  background: #fef3c7;
  color: #d97706;
}

.stat-icon.error {
  background: #fee2e2;
  color: #dc2626;
}

.stat-icon.total {
  background: #dbeafe;
  color: #2563eb;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
}

.filter-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: end;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 150px;
}

.filter-item label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.filter-item input,
.filter-item select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.table-container {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.table-title {
  font-weight: 600;
  color: #1f2937;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.data-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.data-table tr:hover {
  background: #f9fafb;
}

.data-table tr.selected {
  background: #eff6ff;
}

.platform-name strong {
  color: #1f2937;
}

.platform-description {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.platform-type {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.platform-type.kubernetes {
  background: #dbeafe;
  color: #1d4ed8;
}

.platform-type.docker {
  background: #e0e7ff;
  color: #3730a3;
}

.platform-url {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  background: #f3f4f6;
  padding: 2px 4px;
  border-radius: 3px;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.connected {
  background: #dcfce7;
  color: #16a34a;
}

.status-badge.disconnected {
  background: #fef3c7;
  color: #d97706;
}

.status-badge.error {
  background: #fee2e2;
  color: #dc2626;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.pagination-info {
  color: #6b7280;
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  gap: 8px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.btn-primary:hover:not(:disabled) {
  background: #1d4ed8;
}

.btn-secondary {
  background: white;
  color: #374151;
  border-color: #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background: #f9fafb;
}

.btn-danger {
  background: #dc2626;
  color: white;
  border-color: #dc2626;
}

.btn-danger:hover:not(:disabled) {
  background: #b91c1c;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

.loading-row,
.empty-row {
  text-align: center;
  padding: 40px 20px;
}

.loading-spinner {
  color: #6b7280;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-icon {
  width: 48px;
  height: 48px;
  color: #9ca3af;
}

.empty-state p {
  color: #6b7280;
  margin: 0;
}
</style>
