/**
 * 云平台管理模块入口文件
 * 导出所有公共组件、类型、服务和工具
 */

// 类型定义
export * from './types/cloudplatform'

// API服务
export { cloudPlatformApi } from './services/cloudplatformApi'

// Pinia Store
export { useCloudPlatformStore } from './stores/cloudplatform'

// Vue组件
export { default as CloudPlatformList } from './views/CloudPlatformList.vue'
export { default as CloudPlatformDetail } from './views/CloudPlatformDetail.vue'
export { default as CloudPlatformDashboard } from './views/CloudPlatformDashboard.vue'
export { default as CloudPlatformModal } from './components/CloudPlatformModal.vue'

// 工具函数
export * from './utils/formatters'

// 路由配置
export const cloudPlatformRoutes = [
  {
    path: '/cloud-platforms',
    name: 'CloudPlatformList',
    component: () => import('./views/CloudPlatformList.vue'),
    meta: {
      title: '云平台管理',
      requiresAuth: true
    }
  },
  {
    path: '/cloud-platforms/dashboard',
    name: 'CloudPlatformDashboard',
    component: () => import('./views/CloudPlatformDashboard.vue'),
    meta: {
      title: '云平台仪表板',
      requiresAuth: true
    }
  },
  {
    path: '/cloud-platforms/:id',
    name: 'CloudPlatformDetail',
    component: () => import('./views/CloudPlatformDetail.vue'),
    meta: {
      title: '云平台详情',
      requiresAuth: true
    }
  }
]

// 模块配置
export const cloudPlatformModuleConfig = {
  name: 'cloudplatform',
  displayName: '云平台管理',
  description: '管理和配置多云平台连接，支持Kubernetes、Docker、云服务商等',
  version: '1.0.0',
  routes: cloudPlatformRoutes,
  permissions: [
    'cloudplatform:read',
    'cloudplatform:create',
    'cloudplatform:update',
    'cloudplatform:delete',
    'cloudplatform:test'
  ]
}
