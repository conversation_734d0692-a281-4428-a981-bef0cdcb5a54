/**
 * 云平台管理模块的状态管理
 * 使用Pinia实现状态管理和数据缓存
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  CloudPlatformDTO,
  CreateCloudPlatformRequest,
  UpdateCloudPlatformRequest,
  ConnectionTestResult,
  PlatformTypeDTO,
  CloudPlatformStatistics,
  CloudPlatformQueryParams,
  PageResponse
} from '@/modules/cloudplatform/types/cloudplatform'
import { cloudPlatformApi } from '@/modules/cloudplatform/services/cloudplatformApi'

export const useCloudPlatformStore = defineStore('cloudPlatform', () => {
  // 状态数据
  const platforms = ref<CloudPlatformDTO[]>([])
  const currentPlatform = ref<CloudPlatformDTO | null>(null)
  const statistics = ref<CloudPlatformStatistics | null>(null)
  const platformTypes = ref<PlatformTypeDTO[]>([])
  const connectionTestResults = ref<Map<number, ConnectionTestResult>>(new Map())

  // 分页状态
  const pagination = ref({
    page: 0,
    size: 10,
    total: 0,
    hasNext: false,
    hasPrevious: false
  })

  // 查询参数
  const queryParams = ref<CloudPlatformQueryParams>({
    page: 0,
    size: 10,
    sortBy: 'createdAt',
    sortDirection: 'DESC'
  })

  // 加载状态
  const loading = ref({
    list: false,
    detail: false,
    create: false,
    update: false,
    delete: false,
    test: false,
    statistics: false,
    types: false
  })

  // 错误状态
  const errors = ref({
    list: '',
    detail: '',
    create: '',
    update: '',
    delete: '',
    test: '',
    statistics: '',
    types: ''
  })

  // 计算属性
  const connectedPlatforms = computed(() => 
    platforms.value.filter(p => p.status === 'CONNECTED')
  )

  const disconnectedPlatforms = computed(() => 
    platforms.value.filter(p => p.status === 'DISCONNECTED')
  )

  const errorPlatforms = computed(() => 
    platforms.value.filter(p => p.status === 'ERROR')
  )

  const platformsByType = computed(() => {
    const result: Record<string, CloudPlatformDTO[]> = {}
    platforms.value.forEach(platform => {
      if (!result[platform.type]) {
        result[platform.type] = []
      }
      result[platform.type].push(platform)
    })
    return result
  })

  // Actions
  
  // 获取云平台列表
  const fetchPlatforms = async (params?: CloudPlatformQueryParams) => {
    loading.value.list = true
    errors.value.list = ''
    
    try {
      if (params) {
        queryParams.value = { ...queryParams.value, ...params }
      }
      
      const response = await cloudPlatformApi.getList(queryParams.value)
      
      if (response.success && response.data) {
        platforms.value = response.data.content
        pagination.value = {
          page: response.data.page,
          size: response.data.size,
          total: response.data.total,
          hasNext: response.data.hasNext,
          hasPrevious: response.data.hasPrevious
        }
      } else {
        throw new Error(response.message || '获取云平台列表失败')
      }
    } catch (error) {
      errors.value.list = error instanceof Error ? error.message : '获取云平台列表失败'
      console.error('获取云平台列表失败:', error)
    } finally {
      loading.value.list = false
    }
  }

  // 获取云平台详情
  const fetchPlatformById = async (id: number) => {
    loading.value.detail = true
    errors.value.detail = ''
    
    try {
      const response = await cloudPlatformApi.getById(id)
      
      if (response.success && response.data) {
        currentPlatform.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取云平台详情失败')
      }
    } catch (error) {
      errors.value.detail = error instanceof Error ? error.message : '获取云平台详情失败'
      console.error('获取云平台详情失败:', error)
      throw error
    } finally {
      loading.value.detail = false
    }
  }

  // 创建云平台
  const createPlatform = async (data: CreateCloudPlatformRequest) => {
    loading.value.create = true
    errors.value.create = ''
    
    try {
      const response = await cloudPlatformApi.create(data)
      
      if (response.success && response.data) {
        platforms.value.unshift(response.data)
        return response.data
      } else {
        throw new Error(response.message || '创建云平台失败')
      }
    } catch (error) {
      errors.value.create = error instanceof Error ? error.message : '创建云平台失败'
      console.error('创建云平台失败:', error)
      throw error
    } finally {
      loading.value.create = false
    }
  }

  // 更新云平台
  const updatePlatform = async (id: number, data: UpdateCloudPlatformRequest) => {
    loading.value.update = true
    errors.value.update = ''
    
    try {
      const response = await cloudPlatformApi.update(id, data)
      
      if (response.success && response.data) {
        const index = platforms.value.findIndex(p => p.id === id)
        if (index !== -1) {
          platforms.value[index] = response.data
        }
        if (currentPlatform.value?.id === id) {
          currentPlatform.value = response.data
        }
        return response.data
      } else {
        throw new Error(response.message || '更新云平台失败')
      }
    } catch (error) {
      errors.value.update = error instanceof Error ? error.message : '更新云平台失败'
      console.error('更新云平台失败:', error)
      throw error
    } finally {
      loading.value.update = false
    }
  }

  // 删除云平台
  const deletePlatform = async (id: number) => {
    loading.value.delete = true
    errors.value.delete = ''
    
    try {
      const response = await cloudPlatformApi.delete(id)
      
      if (response.success) {
        platforms.value = platforms.value.filter(p => p.id !== id)
        if (currentPlatform.value?.id === id) {
          currentPlatform.value = null
        }
        connectionTestResults.value.delete(id)
      } else {
        throw new Error(response.message || '删除云平台失败')
      }
    } catch (error) {
      errors.value.delete = error instanceof Error ? error.message : '删除云平台失败'
      console.error('删除云平台失败:', error)
      throw error
    } finally {
      loading.value.delete = false
    }
  }

  // 测试连接
  const testConnection = async (id: number) => {
    loading.value.test = true
    errors.value.test = ''
    
    try {
      const response = await cloudPlatformApi.testConnection(id)
      
      if (response.success && response.data) {
        connectionTestResults.value.set(id, response.data)
        
        // 更新平台状态
        const platform = platforms.value.find(p => p.id === id)
        if (platform) {
          platform.status = response.data.status
        }
        
        return response.data
      } else {
        throw new Error(response.message || '连接测试失败')
      }
    } catch (error) {
      errors.value.test = error instanceof Error ? error.message : '连接测试失败'
      console.error('连接测试失败:', error)
      throw error
    } finally {
      loading.value.test = false
    }
  }

  // 获取统计信息
  const fetchStatistics = async () => {
    loading.value.statistics = true
    errors.value.statistics = ''
    
    try {
      const response = await cloudPlatformApi.getStatistics()
      
      if (response.success && response.data) {
        statistics.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取统计信息失败')
      }
    } catch (error) {
      errors.value.statistics = error instanceof Error ? error.message : '获取统计信息失败'
      console.error('获取统计信息失败:', error)
      throw error
    } finally {
      loading.value.statistics = false
    }
  }

  // 获取平台类型
  const fetchPlatformTypes = async () => {
    loading.value.types = true
    errors.value.types = ''
    
    try {
      const response = await cloudPlatformApi.getPlatformTypes()
      
      if (response.success && response.data) {
        platformTypes.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取平台类型失败')
      }
    } catch (error) {
      errors.value.types = error instanceof Error ? error.message : '获取平台类型失败'
      console.error('获取平台类型失败:', error)
      throw error
    } finally {
      loading.value.types = false
    }
  }

  // 清除错误
  const clearError = (type: keyof typeof errors.value) => {
    errors.value[type] = ''
  }

  // 清除所有错误
  const clearAllErrors = () => {
    Object.keys(errors.value).forEach(key => {
      errors.value[key as keyof typeof errors.value] = ''
    })
  }

  // 重置状态
  const reset = () => {
    platforms.value = []
    currentPlatform.value = null
    statistics.value = null
    connectionTestResults.value.clear()
    clearAllErrors()
  }

  return {
    // 状态
    platforms,
    currentPlatform,
    statistics,
    platformTypes,
    connectionTestResults,
    pagination,
    queryParams,
    loading,
    errors,
    
    // 计算属性
    connectedPlatforms,
    disconnectedPlatforms,
    errorPlatforms,
    platformsByType,
    
    // Actions
    fetchPlatforms,
    fetchPlatformById,
    createPlatform,
    updatePlatform,
    deletePlatform,
    testConnection,
    fetchStatistics,
    fetchPlatformTypes,
    clearError,
    clearAllErrors,
    reset
  }
})
