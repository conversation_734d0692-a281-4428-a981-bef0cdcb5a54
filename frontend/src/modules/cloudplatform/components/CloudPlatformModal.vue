<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h2>{{ modalTitle }}</h2>
        <button @click="$emit('close')" class="close-button">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <form @submit.prevent="handleSubmit">
          <!-- 基本信息 -->
          <div class="form-section">
            <h3>基本信息</h3>
            
            <div class="form-row">
              <div class="form-field">
                <label for="name">平台名称 <span class="required">*</span></label>
                <input
                  id="name"
                  v-model="formData.name"
                  type="text"
                  placeholder="输入云平台名称"
                  :class="{ error: errors.name }"
                  required
                />
                <div v-if="errors.name" class="error-message">{{ errors.name }}</div>
              </div>

              <div class="form-field">
                <label for="type">平台类型 <span class="required">*</span></label>
                <select
                  id="type"
                  v-model="formData.type"
                  :class="{ error: errors.type }"
                  required
                >
                  <option value="">请选择平台类型</option>
                  <option
                    v-for="type in platformTypes"
                    :key="type.code"
                    :value="type.code"
                  >
                    {{ type.displayName }}
                  </option>
                </select>
                <div v-if="errors.type" class="error-message">{{ errors.type }}</div>
              </div>
            </div>

            <div class="form-field">
              <label for="url">连接地址 <span class="required">*</span></label>
              <input
                id="url"
                v-model="formData.url"
                type="url"
                placeholder="https://example.com:6443"
                :class="{ error: errors.url }"
                required
              />
              <div v-if="errors.url" class="error-message">{{ errors.url }}</div>
              <div class="field-hint">
                请输入完整的API地址，包括协议和端口
              </div>
            </div>

            <div class="form-field">
              <label for="description">描述信息</label>
              <textarea
                id="description"
                v-model="formData.description"
                placeholder="输入平台描述信息"
                rows="3"
              ></textarea>
            </div>
          </div>

          <!-- 认证配置 -->
          <div class="form-section">
            <h3>认证配置</h3>
            
            <div class="form-field">
              <label for="secret">认证密钥 <span class="required">*</span></label>
              <textarea
                id="secret"
                v-model="formData.secret"
                placeholder="输入认证密钥（如kubeconfig、API密钥等）"
                rows="6"
                :class="{ error: errors.secret }"
                required
              ></textarea>
              <div v-if="errors.secret" class="error-message">{{ errors.secret }}</div>
              <div class="field-hint">
                密钥信息将被加密存储，请确保格式正确
              </div>
            </div>
          </div>

          <!-- 扩展配置 -->
          <div class="form-section">
            <h3>扩展配置</h3>
            
            <div class="form-row">
              <div class="form-field">
                <label for="region">区域</label>
                <input
                  id="region"
                  v-model="formData.region"
                  type="text"
                  placeholder="如：us-west-1, cn-beijing"
                />
              </div>

              <div class="form-field">
                <label for="version">版本</label>
                <input
                  id="version"
                  v-model="formData.version"
                  type="text"
                  placeholder="如：v1.24.0"
                />
              </div>
            </div>

            <div class="form-field">
              <label for="tags">标签</label>
              <div class="tags-input">
                <div class="tags-list">
                  <span
                    v-for="(value, key) in formData.tags"
                    :key="key"
                    class="tag"
                  >
                    {{ key }}: {{ value }}
                    <button
                      type="button"
                      @click="removeTag(key)"
                      class="tag-remove"
                    >
                      ×
                    </button>
                  </span>
                </div>
                <div class="tag-input-row">
                  <input
                    v-model="newTagKey"
                    type="text"
                    placeholder="标签键"
                    class="tag-key-input"
                  />
                  <input
                    v-model="newTagValue"
                    type="text"
                    placeholder="标签值"
                    class="tag-value-input"
                  />
                  <button
                    type="button"
                    @click="addTag"
                    class="btn btn-secondary"
                    style="width: auto; padding: 0 12px;"
                  >
                    添加
                  </button>
                </div>
              </div>
              <div class="field-hint">
                使用标签对云平台进行分类管理，以键值对形式添加
              </div>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button
          type="button"
          @click="$emit('close')"
          class="btn btn-secondary"
        >
          取消
        </button>
        <button
          type="button"
          @click="handleTest"
          :disabled="!canTest || testing"
          class="btn btn-secondary"
        >
          {{ testing ? '测试中...' : '测试连接' }}
        </button>
        <button
          type="button"
          @click="handleSubmit"
          :disabled="!isValid || submitting"
          class="btn btn-primary"
        >
          {{ submitting ? '保存中...' : (mode === 'create' ? '创建' : '更新') }}
        </button>
      </div>

      <!-- 测试结果 -->
      <div v-if="testResult" class="test-result" :class="testResult.success ? 'success' : 'error'">
        <div class="test-result-header">
          <svg v-if="testResult.success" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          <svg v-else viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <span>{{ testResult.success ? '连接成功' : '连接失败' }}</span>
        </div>
        <div v-if="testResult.message" class="test-result-message">
          {{ testResult.message }}
        </div>
        <div v-if="testResult.version" class="test-result-details">
          版本: {{ testResult.version }}
        </div>
        <div v-if="testResult.responseTime" class="test-result-details">
          响应时间: {{ testResult.responseTime }}ms
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useCloudPlatformStore } from '@/modules/cloudplatform/stores/cloudplatform'
import type {
  CloudPlatformDTO,
  CreateCloudPlatformRequest,
  UpdateCloudPlatformRequest,
  PlatformTypeDTO,
  ConnectionTestResult
} from '@/modules/cloudplatform/types/cloudplatform'
import { message } from '@/utils/message'

// Props
interface Props {
  visible: boolean
  mode: 'create' | 'edit' | 'view'
  platform?: CloudPlatformDTO | null
  platformTypes: PlatformTypeDTO[]
}

const props = withDefaults(defineProps<Props>(), {
  platform: null
})

// Emits
const emit = defineEmits<{
  close: []
  submit: []
}>()

// Store
const store = useCloudPlatformStore()

// 响应式数据
const formData = ref<CreateCloudPlatformRequest | UpdateCloudPlatformRequest>({
  name: '',
  type: '',
  url: '',
  secret: '',
  description: '',
  region: '',
  version: '',
  tags: {}
})

const errors = ref<Record<string, string>>({})
const submitting = ref(false)
const testing = ref(false)
const testResult = ref<ConnectionTestResult | null>(null)
const newTagKey = ref('')
const newTagValue = ref('')

// 计算属性
const modalTitle = computed(() => {
  switch (props.mode) {
    case 'create':
      return '添加云平台'
    case 'edit':
      return '编辑云平台'
    case 'view':
      return '查看云平台'
    default:
      return '云平台'
  }
})

const canTest = computed(() => {
  return formData.value.name && formData.value.type && formData.value.url && formData.value.secret
})

const isValid = computed(() => {
  return formData.value.name && formData.value.type && formData.value.url && formData.value.secret
})

// 方法
const initFormData = () => {
  if (props.mode === 'edit' && props.platform) {
    formData.value = {
      name: props.platform.name,
      type: props.platform.type,
      url: props.platform.url,
      description: props.platform.description || '',
      region: props.platform.region || '',
      version: props.platform.version || '',
      tags: props.platform.tags || {}
    }
  } else {
    formData.value = {
      name: '',
      type: '',
      url: '',
      secret: '',
      description: '',
      region: '',
      version: '',
      tags: {}
    }
  }
  errors.value = {}
  testResult.value = null
}

const validateForm = () => {
  errors.value = {}
  
  if (!formData.value.name) {
    errors.value.name = '请输入平台名称'
  }
  
  if (!formData.value.type) {
    errors.value.type = '请选择平台类型'
  }
  
  if (!formData.value.url) {
    errors.value.url = '请输入连接地址'
  } else if (!/^https?:\/\/.+/.test(formData.value.url)) {
    errors.value.url = '请输入有效的URL地址'
  }
  
  if (!formData.value.secret) {
    errors.value.secret = '请输入认证密钥'
  }
  
  return Object.keys(errors.value).length === 0
}

const addTag = () => {
  const key = newTagKey.value.trim()
  const value = newTagValue.value.trim()
  if (key && value) {
    if (!formData.value.tags) {
      formData.value.tags = {}
    }
    formData.value.tags[key] = value
    newTagKey.value = ''
    newTagValue.value = ''
  }
}

const removeTag = (key: string) => {
  if (formData.value.tags) {
    delete formData.value.tags[key]
  }
}

const handleTest = async () => {
  if (!canTest.value) return
  
  testing.value = true
  testResult.value = null
  
  try {
    // 这里需要调用临时测试API，暂时模拟
    await new Promise(resolve => setTimeout(resolve, 2000))
    testResult.value = {
      success: true,
      status: 'CONNECTED',
      message: '连接测试成功',
      version: 'v1.24.0',
      responseTime: 150,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    testResult.value = {
      success: false,
      status: 'ERROR',
      message: error instanceof Error ? error.message : '连接测试失败',
      timestamp: new Date().toISOString()
    }
  } finally {
    testing.value = false
  }
}

const handleSubmit = async () => {
  if (!validateForm()) return
  
  submitting.value = true
  
  try {
    if (props.mode === 'create') {
      await store.createPlatform(formData.value as CreateCloudPlatformRequest)
    } else if (props.mode === 'edit' && props.platform?.id) {
      await store.updatePlatform(props.platform.id, formData.value as UpdateCloudPlatformRequest)
    }
    
    emit('submit')
  } catch (error) {
    message.error('保存失败: ' + (error instanceof Error ? error.message : '未知错误'))
  } finally {
    submitting.value = false
  }
}

const handleOverlayClick = () => {
  emit('close')
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible) {
    initFormData()
  }
})

watch(() => props.platform, () => {
  if (props.visible) {
    initFormData()
  }
})

// 生命周期
onMounted(() => {
  if (props.visible) {
    initFormData()
  }
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-container {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.modal-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.close-button:hover {
  background: #e5e7eb;
  color: #374151;
}

.close-button svg {
  width: 20px;
  height: 20px;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-field {
  margin-bottom: 20px;
}

.form-field label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.required {
  color: #dc2626;
}

.form-field input,
.form-field select,
.form-field textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.form-field input:focus,
.form-field select:focus,
.form-field textarea:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-field input.error,
.form-field select.error,
.form-field textarea.error {
  border-color: #dc2626;
}

.error-message {
  color: #dc2626;
  font-size: 12px;
  margin-top: 4px;
}

.field-hint {
  color: #6b7280;
  font-size: 12px;
  margin-top: 4px;
}

.tags-input {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px;
  min-height: 40px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 8px;
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background: #eff6ff;
  color: #1d4ed8;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.tag-remove {
  background: none;
  border: none;
  color: #1d4ed8;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
}

.tag-remove:hover {
  background: #dbeafe;
}

.tag-input-row {
  display: flex;
  gap: 8px;
  align-items: center;
}

.tag-key-input,
.tag-value-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 13px;
}

.tag-key-input:focus,
.tag-value-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.btn-primary:hover:not(:disabled) {
  background: #1d4ed8;
}

.btn-secondary {
  background: white;
  color: #374151;
  border-color: #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background: #f9fafb;
}

.test-result {
  margin: 16px 24px;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid;
}

.test-result.success {
  background: #f0fdf4;
  border-color: #bbf7d0;
  color: #166534;
}

.test-result.error {
  background: #fef2f2;
  border-color: #fecaca;
  color: #991b1b;
}

.test-result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  margin-bottom: 8px;
}

.test-result-header svg {
  width: 16px;
  height: 16px;
}

.test-result-message {
  font-size: 14px;
  margin-bottom: 4px;
}

.test-result-details {
  font-size: 12px;
  opacity: 0.8;
}
</style>
