/**
 * 云平台管理模块的TypeScript类型定义
 * 与后端API保持一致的数据结构
 */

// 基础实体接口
export interface BaseEntity {
  id?: number
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  updatedBy?: string
  userId?: number
}

// 平台类型枚举
export enum PlatformType {
  KUBERNETES = 'KUBERNETES',
  K3S = 'K3S',
  DOCKER = 'DOCKER',
  DOCKER_SWARM = 'DOCKER_SWARM',
  ALIYUN = 'ALIYUN',
  TENCENT_CLOUD = 'TENCENT_CLOUD',
  AWS = 'AWS',
  AZURE = 'AZURE',
  OPENSTACK = 'OPENSTACK',
  VMWARE = 'VMWARE'
}

// 连接状态枚举
export enum ConnectionStatus {
  CONNECTED = 'CONNECTED',
  DISCONNECTED = 'DISCONNECTED',
  ERROR = 'ERROR',
  TESTING = 'TESTING'
}

// 云平台实体
export interface CloudPlatform extends BaseEntity {
  name: string
  type: string
  url: string
  secret: string
  description?: string
  status: string
  region?: string
  version?: string
  tags?: string
}

// 云平台DTO（用于API传输）
export interface CloudPlatformDTO {
  id?: number
  name: string
  type: string
  url: string
  description?: string
  status: string
  region?: string
  version?: string
  tags?: Record<string, string>
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  updatedBy?: string
}

// 创建云平台请求
export interface CreateCloudPlatformRequest {
  name: string
  type: string
  url: string
  secret?: string
  description?: string
  region?: string
  version?: string
  tags?: Record<string, string>
}

// 更新云平台请求
export interface UpdateCloudPlatformRequest {
  name?: string
  type?: string
  url?: string
  secret?: string
  description?: string
  region?: string
  version?: string
  tags?: Record<string, string>
}

// 连接测试结果
export interface ConnectionTestResult {
  success: boolean
  status: string
  message?: string
  version?: string
  responseTime?: number
  timestamp: string
}

// 平台类型DTO
export interface PlatformTypeDTO {
  code: string
  displayName: string
  description: string
  icon?: string
  supportedFeatures: string[]
}

// 云平台统计信息
export interface CloudPlatformStatistics {
  totalCount: number
  connectedCount: number
  disconnectedCount: number
  errorCount: number
  typeDistribution: Record<string, number>
  regionDistribution: Record<string, number>
}

// 查询参数
export interface CloudPlatformQueryParams {
  page?: number
  size?: number
  type?: string
  status?: string
  region?: string
  keyword?: string
  sortBy?: string
  sortDirection?: 'ASC' | 'DESC'
}

// 分页响应
export interface PageResponse<T> {
  content: T[]
  page: number
  size: number
  total: number
  hasNext: boolean
  hasPrevious: boolean
}

// API响应包装
export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  code?: string
  timestamp: string
}

// 表单验证规则
export interface ValidationRule {
  required?: boolean
  message: string
  validator?: (value: any) => boolean
}

// 表单字段配置
export interface FormFieldConfig {
  key: string
  label: string
  type: 'input' | 'select' | 'textarea' | 'password' | 'tags'
  placeholder?: string
  options?: Array<{ label: string; value: string }>
  rules?: ValidationRule[]
  disabled?: boolean
  visible?: boolean
}

// 表格列配置
export interface TableColumn {
  key: string
  title: string
  width?: string
  sortable?: boolean
  filterable?: boolean
  render?: (value: any, record: any) => string
}

// 操作按钮配置
export interface ActionButton {
  key: string
  label: string
  type: 'primary' | 'default' | 'danger' | 'warning'
  icon?: string
  disabled?: (record: any) => boolean
  visible?: (record: any) => boolean
  onClick: (record: any) => void
}

// 过滤器配置
export interface FilterConfig {
  key: string
  label: string
  type: 'select' | 'input' | 'daterange'
  options?: Array<{ label: string; value: string }>
  placeholder?: string
}

// 云平台管理状态
export interface CloudPlatformState {
  // 数据状态
  platforms: CloudPlatformDTO[]
  currentPlatform: CloudPlatformDTO | null
  statistics: CloudPlatformStatistics | null
  platformTypes: PlatformTypeDTO[]
  
  // UI状态
  loading: boolean
  saving: boolean
  testing: boolean
  
  // 分页状态
  pagination: {
    page: number
    size: number
    total: number
  }
  
  // 查询状态
  queryParams: CloudPlatformQueryParams
  
  // 表单状态
  formVisible: boolean
  formMode: 'create' | 'edit' | 'view'
  formData: CreateCloudPlatformRequest | UpdateCloudPlatformRequest
  
  // 错误状态
  error: string | null
}

// 事件类型
export interface CloudPlatformEvent {
  type: 'create' | 'update' | 'delete' | 'test' | 'refresh'
  payload?: any
  timestamp: string
}
