# 云平台管理模块

云平台管理模块提供了统一的多云平台连接和管理功能，支持Kubernetes、Docker、云服务商等多种平台类型。

## 功能特性

### 核心功能
- **多云平台支持**: 支持Kubernetes、Docker、阿里云、腾讯云、AWS等多种平台
- **连接管理**: 统一管理云平台连接配置和认证信息
- **连接测试**: 实时测试云平台连接状态和健康度
- **统计分析**: 提供云平台使用统计和分布分析
- **标签管理**: 支持为云平台添加标签进行分类管理

### 界面功能
- **仪表板视图**: 展示云平台概览和统计信息
- **列表管理**: 云平台的增删改查操作
- **详情查看**: 查看云平台详细信息和连接状态
- **批量操作**: 支持批量删除、导入导出等操作

## 目录结构

```
frontend/src/modules/cloudplatform/
├── components/           # Vue组件
│   └── CloudPlatformModal.vue
├── services/            # API服务
│   └── cloudplatformApi.ts
├── stores/              # Pinia状态管理
│   └── cloudplatform.ts
├── types/               # TypeScript类型定义
│   └── cloudplatform.ts
├── utils/               # 工具函数
│   └── formatters.ts
├── views/               # 页面组件
│   ├── CloudPlatformDashboard.vue
│   ├── CloudPlatformList.vue
│   └── CloudPlatformDetail.vue
├── tests/               # 单元测试
│   ├── CloudPlatformStore.test.ts
│   └── CloudPlatformModal.test.ts
├── index.ts             # 模块入口
└── README.md            # 说明文档
```

## 使用方法

### 1. 路由配置

模块已自动配置以下路由：

- `/cloud-platforms/dashboard` - 云平台仪表板
- `/cloud-platforms` - 云平台列表
- `/cloud-platforms/:id` - 云平台详情

### 2. 组件使用

#### 导入组件
```typescript
import {
  CloudPlatformList,
  CloudPlatformDetail,
  CloudPlatformDashboard,
  CloudPlatformModal
} from '@/modules/cloudplatform'
```

#### 使用Store
```typescript
import { useCloudPlatformStore } from '@/modules/cloudplatform'

const store = useCloudPlatformStore()

// 获取云平台列表
await store.fetchPlatforms({ page: 0, size: 10 })

// 创建云平台
await store.createPlatform({
  name: 'My Kubernetes',
  type: 'KUBERNETES',
  url: 'https://k8s.example.com'
})

// 测试连接
const result = await store.testConnection('platform-id')
```

#### 使用API服务
```typescript
import { cloudPlatformApi } from '@/modules/cloudplatform'

// 直接调用API
const response = await cloudPlatformApi.getList({ page: 0, size: 10 })
const statistics = await cloudPlatformApi.getStatistics()
```

### 3. 类型定义

模块提供了完整的TypeScript类型定义：

```typescript
import type {
  CloudPlatformDTO,
  CloudPlatformCreateRequest,
  CloudPlatformUpdateRequest,
  CloudPlatformStatistics,
  PlatformTypeDTO,
  ConnectionTestResult
} from '@/modules/cloudplatform'
```

### 4. 工具函数

```typescript
import {
  formatDate,
  getPlatformTypeName,
  getStatusText,
  formatUrl
} from '@/modules/cloudplatform'

// 格式化日期
const formattedDate = formatDate('2024-01-01T00:00:00Z', 'relative')

// 获取平台类型名称
const typeName = getPlatformTypeName('KUBERNETES')

// 获取状态文本
const statusText = getStatusText('CONNECTED')
```

## API接口

### 云平台管理
- `GET /api/cloud-platforms` - 获取云平台列表
- `POST /api/cloud-platforms` - 创建云平台
- `GET /api/cloud-platforms/{id}` - 获取云平台详情
- `PUT /api/cloud-platforms/{id}` - 更新云平台
- `DELETE /api/cloud-platforms/{id}` - 删除云平台

### 连接测试
- `POST /api/cloud-platforms/{id}/test` - 测试云平台连接

### 统计信息
- `GET /api/cloud-platforms/statistics` - 获取统计信息
- `GET /api/cloud-platforms/types` - 获取支持的平台类型

### 搜索和过滤
- `GET /api/cloud-platforms/search` - 搜索云平台
- 支持按名称、类型、状态、标签等条件过滤

## 开发指南

### 添加新的平台类型

1. 在后端添加新的枚举值到 `PlatformType`
2. 在 `CloudPlatformConnectionService` 中实现连接测试逻辑
3. 更新前端的类型定义和格式化函数
4. 添加相应的图标和样式

### 扩展连接测试功能

1. 在 `CloudPlatformConnectionService` 中添加新的测试方法
2. 更新 `ConnectionTestResult` 类型定义
3. 在前端组件中处理新的测试结果

### 自定义样式

模块使用Tailwind CSS，可以通过以下方式自定义样式：

```vue
<style scoped>
.custom-platform-card {
  @apply bg-white rounded-lg shadow-md p-6;
}

.custom-status-badge {
  @apply px-2 py-1 rounded text-xs font-medium;
}
</style>
```

## 测试

### 运行单元测试
```bash
npm run test:unit
```

### 运行特定测试文件
```bash
npm run test:unit -- CloudPlatformStore.test.ts
```

### 测试覆盖率
```bash
npm run test:coverage
```

## 注意事项

1. **安全性**: 云平台的认证信息会被加密存储，请确保后端配置了正确的加密密钥
2. **权限控制**: 所有页面都需要用户登录，请确保认证中间件正常工作
3. **错误处理**: 组件已实现完整的错误处理，但请根据实际需求调整错误提示
4. **性能优化**: 大量云平台时建议启用分页和虚拟滚动
5. **国际化**: 当前仅支持中文，如需多语言请添加i18n配置

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持基本的云平台管理功能
- 实现连接测试和统计分析
- 提供完整的前端界面和API接口
