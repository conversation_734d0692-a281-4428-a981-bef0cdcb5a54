/**
 * 云平台管理模块的格式化工具函数
 * 提供统一的数据格式化方法
 */

import type { PlatformTypeDTO } from '@/modules/cloudplatform/types/cloudplatform'

/**
 * 格式化日期时间
 * @param dateString 日期字符串
 * @param format 格式类型：'full' | 'date' | 'time' | 'relative'
 * @returns 格式化后的日期字符串
 */
export function formatDate(dateString?: string, format: 'full' | 'date' | 'time' | 'relative' = 'full'): string {
  if (!dateString) return '-'
  
  const date = new Date(dateString)
  
  if (isNaN(date.getTime())) {
    return '-'
  }
  
  switch (format) {
    case 'full':
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    
    case 'date':
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    
    case 'time':
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    
    case 'relative':
      return formatRelativeTime(date)
    
    default:
      return date.toLocaleString('zh-CN')
  }
}

/**
 * 格式化相对时间
 * @param date 日期对象
 * @returns 相对时间字符串
 */
export function formatRelativeTime(date: Date): string {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  const months = Math.floor(days / 30)
  const years = Math.floor(days / 365)
  
  if (seconds < 60) {
    return '刚刚'
  } else if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 30) {
    return `${days}天前`
  } else if (months < 12) {
    return `${months}个月前`
  } else {
    return `${years}年前`
  }
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param decimals 小数位数
 * @returns 格式化后的文件大小字符串
 */
export function formatFileSize(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 格式化百分比
 * @param value 数值
 * @param total 总数
 * @param decimals 小数位数
 * @returns 格式化后的百分比字符串
 */
export function formatPercentage(value: number, total: number, decimals: number = 1): string {
  if (total === 0) return '0%'
  
  const percentage = (value / total) * 100
  return `${percentage.toFixed(decimals)}%`
}

/**
 * 格式化响应时间
 * @param milliseconds 毫秒数
 * @returns 格式化后的响应时间字符串
 */
export function formatResponseTime(milliseconds: number): string {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`
  } else if (milliseconds < 60000) {
    return `${(milliseconds / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(milliseconds / 60000)
    const seconds = Math.floor((milliseconds % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }
}

/**
 * 获取平台类型显示名称
 * @param type 平台类型
 * @param platformTypes 平台类型列表
 * @returns 显示名称
 */
export function getPlatformTypeName(type: string, platformTypes: PlatformTypeDTO[] = []): string {
  const platformType = platformTypes.find(t => t.code === type)
  if (platformType) {
    return platformType.displayName
  }
  
  // 默认映射
  const typeMap: Record<string, string> = {
    'KUBERNETES': 'Kubernetes',
    'K3S': 'K3s',
    'DOCKER': 'Docker',
    'DOCKER_SWARM': 'Docker Swarm',
    'ALIYUN': '阿里云',
    'TENCENT_CLOUD': '腾讯云',
    'AWS': 'Amazon Web Services',
    'AZURE': 'Microsoft Azure',
    'OPENSTACK': 'OpenStack',
    'VMWARE': 'VMware'
  }
  
  return typeMap[type] || type
}

/**
 * 获取连接状态显示文本
 * @param status 连接状态
 * @returns 显示文本
 */
export function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'CONNECTED': '已连接',
    'DISCONNECTED': '未连接',
    'ERROR': '异常',
    'TESTING': '测试中'
  }
  
  return statusMap[status] || status
}

/**
 * 获取状态对应的颜色类名
 * @param status 连接状态
 * @returns CSS类名
 */
export function getStatusColorClass(status: string): string {
  const colorMap: Record<string, string> = {
    'CONNECTED': 'text-green-600 bg-green-100',
    'DISCONNECTED': 'text-yellow-600 bg-yellow-100',
    'ERROR': 'text-red-600 bg-red-100',
    'TESTING': 'text-blue-600 bg-blue-100'
  }
  
  return colorMap[status] || 'text-gray-600 bg-gray-100'
}

/**
 * 获取平台类型对应的颜色类名
 * @param type 平台类型
 * @returns CSS类名
 */
export function getPlatformTypeColorClass(type: string): string {
  const colorMap: Record<string, string> = {
    'KUBERNETES': 'text-blue-600 bg-blue-100',
    'K3S': 'text-indigo-600 bg-indigo-100',
    'DOCKER': 'text-purple-600 bg-purple-100',
    'DOCKER_SWARM': 'text-violet-600 bg-violet-100',
    'ALIYUN': 'text-orange-600 bg-orange-100',
    'TENCENT_CLOUD': 'text-cyan-600 bg-cyan-100',
    'AWS': 'text-yellow-600 bg-yellow-100',
    'AZURE': 'text-blue-600 bg-blue-100',
    'OPENSTACK': 'text-red-600 bg-red-100',
    'VMWARE': 'text-green-600 bg-green-100'
  }
  
  return colorMap[type] || 'text-gray-600 bg-gray-100'
}

/**
 * 格式化URL显示
 * @param url URL字符串
 * @param maxLength 最大显示长度
 * @returns 格式化后的URL
 */
export function formatUrl(url: string, maxLength: number = 50): string {
  if (!url) return '-'
  
  if (url.length <= maxLength) {
    return url
  }
  
  const start = url.substring(0, Math.floor(maxLength / 2))
  const end = url.substring(url.length - Math.floor(maxLength / 2))
  
  return `${start}...${end}`
}

/**
 * 格式化标签列表
 * @param tags 标签数组
 * @param maxDisplay 最大显示数量
 * @returns 格式化后的标签信息
 */
export function formatTags(tags: string[] = [], maxDisplay: number = 3): { displayed: string[]; remaining: number } {
  if (tags.length <= maxDisplay) {
    return {
      displayed: tags,
      remaining: 0
    }
  }
  
  return {
    displayed: tags.slice(0, maxDisplay),
    remaining: tags.length - maxDisplay
  }
}

/**
 * 验证URL格式
 * @param url URL字符串
 * @returns 是否为有效URL
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 验证JSON格式
 * @param jsonString JSON字符串
 * @returns 是否为有效JSON
 */
export function isValidJson(jsonString: string): boolean {
  try {
    JSON.parse(jsonString)
    return true
  } catch {
    return false
  }
}

/**
 * 生成随机ID
 * @param length ID长度
 * @returns 随机ID字符串
 */
export function generateId(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  
  return result
}

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 * @returns 拷贝后的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }
  
  return obj
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(null, args), delay)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param delay 延迟时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    
    if (now - lastCall >= delay) {
      lastCall = now
      func.apply(null, args)
    }
  }
}
