/**
 * 云平台管理Store单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useCloudPlatformStore } from '../stores/cloudplatform'
import { cloudPlatformApi } from '../services/cloudplatformApi'
import type { CloudPlatformDTO, CreateCloudPlatformRequest, CloudPlatformStatistics } from '../types/cloudplatform'

// Mock API
vi.mock('../services/cloudplatformApi', () => ({
  cloudPlatformApi: {
    getList: vi.fn(),
    getById: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    testConnection: vi.fn(),
    getStatistics: vi.fn(),
    getPlatformTypes: vi.fn(),
    search: vi.fn()
  }
}))

const mockCloudPlatform: CloudPlatformDTO = {
  id: 1,
  name: 'Test Kubernetes',
  type: 'KUBERNETES',
  url: 'https://k8s.example.com',
  description: 'Test Kubernetes cluster',
  region: 'us-west-1',
  status: 'CONNECTED',
  tags: {
    "env": "test",
    "env_type": "prod"
  },
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockStatistics: CloudPlatformStatistics = {
  totalCount: 5,
  connectedCount: 3,
  disconnectedCount: 1,
  errorCount: 1,
  typeDistribution: {
    'KUBERNETES': 3,
    'DOCKER': 2
  },
  regionDistribution: {
    'us-west-1': 2,
    'us-east-1': 3
  }
}

describe('CloudPlatformStore', () => {
  let store: ReturnType<typeof useCloudPlatformStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useCloudPlatformStore()
    vi.clearAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(store.platforms).toEqual([])
      expect(store.currentPlatform).toBeNull()
      expect(store.statistics).toBeNull()
      expect(store.platformTypes).toEqual([])
      expect(store.loading.list).toBe(false)
      expect(store.loading.detail).toBe(false)
      expect(store.loading.statistics).toBe(false)
      expect(store.errors.list).toBeNull()
      expect(store.errors.detail).toBeNull()
    })
  })

  describe('fetchPlatforms', () => {
    it('应该成功获取云平台列表', async () => {
      const mockResponse = {
        success: true,
        data: {
          content: [mockCloudPlatform],
          totalElements: 1,
          totalPages: 1,
          number: 0,
          size: 10
        }
      }

      vi.mocked(cloudPlatformApi.getList).mockResolvedValue(mockResponse)

      await store.fetchPlatforms({ page: 0, size: 10 })

      expect(store.platforms).toEqual([mockCloudPlatform])
      expect(store.loading.list).toBe(false)
      expect(store.errors.list).toBeNull()
      expect(cloudPlatformApi.getList).toHaveBeenCalledWith({ page: 0, size: 10 })
    })

    it('应该处理获取云平台列表失败的情况', async () => {
      const mockError = new Error('Network error')
      vi.mocked(cloudPlatformApi.getList).mockRejectedValue(mockError)

      await store.fetchPlatforms({ page: 0, size: 10 })

      expect(store.platforms).toEqual([])
      expect(store.loading.list).toBe(false)
      expect(store.errors.list).toBe('获取云平台列表失败')
    })
  })

  describe('fetchPlatformById', () => {
    it('应该成功获取云平台详情', async () => {
      const mockResponse = {
        success: true,
        data: mockCloudPlatform
      }

      vi.mocked(cloudPlatformApi.getById).mockResolvedValue(mockResponse)

      await store.fetchPlatformById(1)

      expect(store.currentPlatform).toEqual(mockCloudPlatform)
      expect(store.loading.detail).toBe(false)
      expect(store.errors.detail).toBeNull()
      expect(cloudPlatformApi.getById).toHaveBeenCalledWith('1')
    })

    it('应该处理获取云平台详情失败的情况', async () => {
      const mockError = new Error('Not found')
      vi.mocked(cloudPlatformApi.getById).mockRejectedValue(mockError)

      await store.fetchPlatformById(1)

      expect(store.currentPlatform).toBeNull()
      expect(store.loading.detail).toBe(false)
      expect(store.errors.detail).toBe('获取云平台详情失败')
    })
  })

  describe('createPlatform', () => {
    it('应该成功创建云平台', async () => {
      const createRequest: CreateCloudPlatformRequest = {
        name: 'New Platform',
        type: 'KUBERNETES',
        url: 'https://new.example.com',
        description: 'New platform',
        region: 'us-west-2',
        tags: {
          "env": "new"
        }
      }

      const mockResponse = {
        success: true,
        data: { ...mockCloudPlatform, ...createRequest, id: '2' }
      }

      vi.mocked(cloudPlatformApi.create).mockResolvedValue(mockResponse)

      const result = await store.createPlatform(createRequest)

      expect(result).toBe(true)
      expect(cloudPlatformApi.create).toHaveBeenCalledWith(createRequest)
    })

    it('应该处理创建云平台失败的情况', async () => {
      const createRequest: CreateCloudPlatformRequest = {
        name: 'New Platform',
        type: 'KUBERNETES',
        url: 'https://new.example.com'
      }

      const mockError = new Error('Validation error')
      vi.mocked(cloudPlatformApi.create).mockRejectedValue(mockError)

      const result = await store.createPlatform(createRequest)

      expect(result).toBe(false)
    })
  })

  describe('updatePlatform', () => {
    it('应该成功更新云平台', async () => {
      const updateRequest = {
        name: 'Updated Platform',
        description: 'Updated description'
      }

      const mockResponse = {
        success: true,
        data: { ...mockCloudPlatform, ...updateRequest }
      }

      vi.mocked(cloudPlatformApi.update).mockResolvedValue(mockResponse)

      const result = await store.updatePlatform(1, updateRequest)

      expect(result).toBe(true)
      expect(cloudPlatformApi.update).toHaveBeenCalledWith('1', updateRequest)
    })
  })

  describe('deletePlatform', () => {
    it('应该成功删除云平台', async () => {
      const mockResponse = {
        success: true,
        data: null
      }

      vi.mocked(cloudPlatformApi.delete).mockResolvedValue(mockResponse)

      const result = await store.deletePlatform(1)

      expect(result).toBe(true)
      expect(cloudPlatformApi.delete).toHaveBeenCalledWith(1)
    })
  })

  describe('testConnection', () => {
    it('应该成功测试连接', async () => {
      const mockResponse = {
        success: true,
        data: {
          success: true,
          message: 'Connection successful',
          responseTime: 100,
          timestamp: '2024-01-01T00:00:00Z'
        }
      }

      vi.mocked(cloudPlatformApi.testConnection).mockResolvedValue(mockResponse)

      const result = await store.testConnection(1)

      expect(result).toEqual(mockResponse.data)
      expect(cloudPlatformApi.testConnection).toHaveBeenCalledWith(1)
    })
  })

  describe('fetchStatistics', () => {
    it('应该成功获取统计信息', async () => {
      const mockResponse = {
        success: true,
        data: mockStatistics
      }

      vi.mocked(cloudPlatformApi.getStatistics).mockResolvedValue(mockResponse)

      await store.fetchStatistics()

      expect(store.statistics).toEqual(mockStatistics)
      expect(store.loading.statistics).toBe(false)
      expect(cloudPlatformApi.getStatistics).toHaveBeenCalled()
    })
  })

  describe('计算属性', () => {
    beforeEach(() => {
      store.platforms = [
        mockCloudPlatform,
        { ...mockCloudPlatform, id: 2, status: 'DISCONNECTED', type: 'DOCKER' },
        { ...mockCloudPlatform, id: 3, status: 'ERROR', type: 'KUBERNETES' }
      ]
    })

    it('connectedPlatforms 应该返回已连接的平台', () => {
      expect(store.connectedPlatforms).toHaveLength(1)
      expect(store.connectedPlatforms[0].status).toBe('CONNECTED')
    })

    it('disconnectedPlatforms 应该返回未连接的平台', () => {
      expect(store.disconnectedPlatforms).toHaveLength(1)
      expect(store.disconnectedPlatforms[0].status).toBe('DISCONNECTED')
    })

    it('errorPlatforms 应该返回异常的平台', () => {
      expect(store.errorPlatforms).toHaveLength(1)
      expect(store.errorPlatforms[0].status).toBe('ERROR')
    })

    it('platformsByType 应该按类型分组平台', () => {
      const grouped = store.platformsByType
      expect(grouped['KUBERNETES']).toHaveLength(2)
      expect(grouped['DOCKER']).toHaveLength(1)
    })
  })

  describe('搜索功能', () => {
    it('应该成功搜索云平台', async () => {
      const mockResponse = {
        success: true,
        data: [mockCloudPlatform]
      }

      vi.mocked(cloudPlatformApi.search).mockResolvedValue(mockResponse)

      const params = {
        'keyword': 'test'
      }

      const result = await store.fetchPlatforms(params)

      expect(result).toEqual([mockCloudPlatform])
      expect(cloudPlatformApi.search).toHaveBeenCalledWith(params.keyword)
    })
  })
})
