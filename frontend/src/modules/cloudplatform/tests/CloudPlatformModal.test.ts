/**
 * 云平台模态框组件单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { setActivePinia, createPinia } from 'pinia'
import CloudPlatformModal from '../components/CloudPlatformModal.vue'
import { useCloudPlatformStore } from '../stores/cloudplatform'
import type { CloudPlatformDTO } from '../types/cloudplatform'

// Mock store
vi.mock('../stores/cloudplatform', () => ({
  useCloudPlatformStore: vi.fn()
}))

const mockPlatform: CloudPlatformDTO = {
  id: 1,
  name: 'Test Platform',
  type: 'KUBERNETES',
  url: 'https://test.example.com',
  description: 'Test platform',
  region: 'us-west-1',
  status: 'CONNECTED',
  tags: {
    "env": "test"
  },
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockStore = {
  platformTypes: [
    { type: 'KUBERNETES', name: 'Kubernetes', description: 'Kubernetes cluster' },
    { type: 'DOCKER', name: 'Docker', description: 'Docker daemon' }
  ],
  createPlatform: vi.fn(),
  updatePlatform: vi.fn(),
  testConnection: vi.fn(),
  fetchPlatformTypes: vi.fn()
}

describe('CloudPlatformModal', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.mocked(useCloudPlatformStore).mockReturnValue(mockStore as any)
    vi.clearAllMocks()
  })

  describe('创建模式', () => {
    it('应该正确渲染创建模态框', () => {
      const wrapper = mount(CloudPlatformModal, {
        props: {
          visible: true,
          mode: 'create'
        }
      })

      expect(wrapper.find('.modal-title').text()).toBe('创建云平台')
      expect(wrapper.find('button[type="submit"]').text()).toBe('创建')
    })

    it('应该显示所有必需的表单字段', () => {
      const wrapper = mount(CloudPlatformModal, {
        props: {
          visible: true,
          mode: 'create'
        }
      })

      expect(wrapper.find('input[name="name"]').exists()).toBe(true)
      expect(wrapper.find('select[name="type"]').exists()).toBe(true)
      expect(wrapper.find('input[name="url"]').exists()).toBe(true)
      expect(wrapper.find('textarea[name="description"]').exists()).toBe(true)
      expect(wrapper.find('input[name="region"]').exists()).toBe(true)
    })

    it('应该在提交时调用创建方法', async () => {
      mockStore.createPlatform.mockResolvedValue(true)

      const wrapper = mount(CloudPlatformModal, {
        props: {
          visible: true,
          mode: 'create'
        }
      })

      // 填写表单
      await wrapper.find('input[name="name"]').setValue('New Platform')
      await wrapper.find('select[name="type"]').setValue('KUBERNETES')
      await wrapper.find('input[name="url"]').setValue('https://new.example.com')

      // 提交表单
      await wrapper.find('form').trigger('submit.prevent')

      expect(mockStore.createPlatform).toHaveBeenCalledWith({
        name: 'New Platform',
        type: 'KUBERNETES',
        url: 'https://new.example.com',
        description: '',
        region: '',
        tags: []
      })
    })
  })

  describe('编辑模式', () => {
    it('应该正确渲染编辑模态框', () => {
      const wrapper = mount(CloudPlatformModal, {
        props: {
          visible: true,
          mode: 'edit',
          platform: mockPlatform
        }
      })

      expect(wrapper.find('.modal-title').text()).toBe('编辑云平台')
      expect(wrapper.find('button[type="submit"]').text()).toBe('保存')
    })

    it('应该预填充表单数据', () => {
      const wrapper = mount(CloudPlatformModal, {
        props: {
          visible: true,
          mode: 'edit',
          platform: mockPlatform
        }
      })

      expect(wrapper.find('input[name="name"]').element.value).toBe(mockPlatform.name)
      expect(wrapper.find('select[name="type"]').element.value).toBe(mockPlatform.type)
      expect(wrapper.find('input[name="url"]').element.value).toBe(mockPlatform.url)
      expect(wrapper.find('textarea[name="description"]').element.value).toBe(mockPlatform.description || '')
      expect(wrapper.find('input[name="region"]').element.value).toBe(mockPlatform.region || '')
    })

    it('应该在提交时调用更新方法', async () => {
      mockStore.updatePlatform.mockResolvedValue(true)

      const wrapper = mount(CloudPlatformModal, {
        props: {
          visible: true,
          mode: 'edit',
          platform: mockPlatform
        }
      })

      // 修改表单
      await wrapper.find('input[name="name"]').setValue('Updated Platform')

      // 提交表单
      await wrapper.find('form').trigger('submit.prevent')

      expect(mockStore.updatePlatform).toHaveBeenCalledWith(mockPlatform.id, {
        name: 'Updated Platform',
        type: mockPlatform.type,
        url: mockPlatform.url,
        description: mockPlatform.description,
        region: mockPlatform.region,
        tags: mockPlatform.tags
      })
    })
  })

  describe('表单验证', () => {
    it('应该验证必需字段', async () => {
      const wrapper = mount(CloudPlatformModal, {
        props: {
          visible: true,
          mode: 'create'
        }
      })

      // 提交空表单
      await wrapper.find('form').trigger('submit.prevent')

      // 应该显示验证错误
      expect(wrapper.find('.error-message').exists()).toBe(true)
      expect(mockStore.createPlatform).not.toHaveBeenCalled()
    })

    it('应该验证URL格式', async () => {
      const wrapper = mount(CloudPlatformModal, {
        props: {
          visible: true,
          mode: 'create'
        }
      })

      await wrapper.find('input[name="name"]').setValue('Test')
      await wrapper.find('select[name="type"]').setValue('KUBERNETES')
      await wrapper.find('input[name="url"]').setValue('invalid-url')

      await wrapper.find('form').trigger('submit.prevent')

      expect(wrapper.find('.error-message').text()).toContain('URL格式不正确')
      expect(mockStore.createPlatform).not.toHaveBeenCalled()
    })
  })

  describe('连接测试', () => {
    it('应该能够测试连接', async () => {
      mockStore.testConnection.mockResolvedValue({
        success: true,
        message: 'Connection successful',
        responseTime: 100
      })

      const wrapper = mount(CloudPlatformModal, {
        props: {
          visible: true,
          mode: 'edit',
          platform: mockPlatform
        }
      })

      const testButton = wrapper.find('.test-connection-btn')
      expect(testButton.exists()).toBe(true)

      await testButton.trigger('click')

      expect(mockStore.testConnection).toHaveBeenCalledWith(mockPlatform.id)
    })

    it('应该显示连接测试结果', async () => {
      mockStore.testConnection.mockResolvedValue({
        success: true,
        message: 'Connection successful',
        responseTime: 100
      })

      const wrapper = mount(CloudPlatformModal, {
        props: {
          visible: true,
          mode: 'edit',
          platform: mockPlatform
        }
      })

      await wrapper.find('.test-connection-btn').trigger('click')
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.test-result.success').exists()).toBe(true)
      expect(wrapper.find('.test-result').text()).toContain('Connection successful')
    })
  })

  describe('标签管理', () => {
    it('应该能够添加标签', async () => {
      const wrapper = mount(CloudPlatformModal, {
        props: {
          visible: true,
          mode: 'create'
        }
      })

      const tagInput = wrapper.find('.tag-input')
      await tagInput.setValue('new-tag')
      await tagInput.trigger('keydown.enter')

      expect(wrapper.find('.tag').text()).toContain('new-tag')
    })

    it('应该能够删除标签', async () => {
      const wrapper = mount(CloudPlatformModal, {
        props: {
          visible: true,
          mode: 'edit',
          platform: mockPlatform
        }
      })

      const deleteButton = wrapper.find('.tag .delete-tag')
      await deleteButton.trigger('click')

      expect(wrapper.findAll('.tag')).toHaveLength(0)
    })
  })

  describe('事件处理', () => {
    it('应该在取消时触发close事件', async () => {
      const wrapper = mount(CloudPlatformModal, {
        props: {
          visible: true,
          mode: 'create'
        }
      })

      await wrapper.find('.cancel-btn').trigger('click')

      expect(wrapper.emitted('close')).toBeTruthy()
    })

    it('应该在成功提交后触发success事件', async () => {
      mockStore.createPlatform.mockResolvedValue(true)

      const wrapper = mount(CloudPlatformModal, {
        props: {
          visible: true,
          mode: 'create'
        }
      })

      await wrapper.find('input[name="name"]').setValue('Test')
      await wrapper.find('select[name="type"]').setValue('KUBERNETES')
      await wrapper.find('input[name="url"]').setValue('https://test.example.com')
      await wrapper.find('form').trigger('submit.prevent')

      await wrapper.vm.$nextTick()

      expect(wrapper.emitted('success')).toBeTruthy()
    })
  })
})
