import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { ref } from 'vue'
import CloudPlatformSelector from '../CloudPlatformSelector.vue'

// Create mock stores
let mockCiStore: any
let mockCdStore: any

// Mock the store modules
vi.mock('../stores/ci', () => ({
  useCiStore: () => mockCiStore
}))

vi.mock('../stores/cd', () => ({
  useCdStore: () => mockCdStore
}))
import { useCiStore } from '../../stores/ci'
import { useCdStore } from '../../stores/cd'

// Mock the stores
vi.mock('../../stores/ci')
vi.mock('../../stores/cd')

describe('CloudPlatformSelector', () => {
  let wrapper: any

  beforeEach(() => {
    setActivePinia(createPinia())
    
    // Mock store methods and properties with reactive refs
    mockCiStore = {
      availableCloudPlatforms: ref([]),
      currentCloudPlatform: ref(null),
      cloudPlatformLoading: ref(false),
      loading: ref(false),
      error: ref(null),
      fetchAvailableCloudPlatforms: vi.fn().mockResolvedValue([]),
      selectCloudPlatform: vi.fn().mockResolvedValue({ success: true }),
      fetchCurrentCloudPlatform: vi.fn().mockResolvedValue({}),
      testCloudPlatformConnection: vi.fn().mockResolvedValue({ connected: true, kubernetesVersion: 'v1.24.0' })
    }

    mockCdStore = {
      availableCloudPlatforms: ref([]),
      currentCloudPlatform: ref(null),
      cloudPlatformLoading: ref(false),
      loading: ref(false),
      error: ref(null),
      fetchAvailableCloudPlatforms: vi.fn().mockResolvedValue([]),
      selectCloudPlatform: vi.fn().mockResolvedValue({ success: true }),
      fetchCurrentCloudPlatform: vi.fn().mockResolvedValue({}),
      testCloudPlatformConnection: vi.fn().mockResolvedValue({ connected: true, kubernetesVersion: 'v1.24.0' })
    }

    // Mock the store composables
    vi.mocked(useCiStore).mockReturnValue(mockCiStore)
    vi.mocked(useCdStore).mockReturnValue(mockCdStore)
  })

  const createWrapper = (props = {}) => {
    return mount(CloudPlatformSelector, {
      props: {
        taskType: 'ci',
        ...props
      },
      global: {
        plugins: [createPinia()]
      }
    })
  }

  it('应该正确渲染组件', () => {
    wrapper = createWrapper()
    
    expect(wrapper.find('.cloud-platform-selector').exists()).toBe(true)
    expect(wrapper.find('select').exists()).toBe(true)
    expect(wrapper.find('label').text()).toBe('选择云平台')
  })

  it('应该根据taskType选择正确的store', () => {
    // 测试CI store
    wrapper = createWrapper({ taskType: 'ci' })
    expect(useCiStore).toHaveBeenCalled()

    // 测试CD store
    wrapper = createWrapper({ taskType: 'cd' })
    expect(useCdStore).toHaveBeenCalled()
  })

  it('应该在组件挂载时获取云平台列表和当前云平台', async () => {
    wrapper = createWrapper()
    
    await wrapper.vm.$nextTick()
    
    expect(mockCiStore.fetchAvailableCloudPlatforms).toHaveBeenCalled()
    expect(mockCiStore.fetchCurrentCloudPlatform).toHaveBeenCalled()
  })

  it('应该显示可用的云平台选项', async () => {
    const platforms = [
      { id: 1, name: '测试平台1', provider: 'AWS' },
      { id: 2, name: '测试平台2', provider: 'GCP' }
    ]
    
    mockCiStore.availableCloudPlatforms.value = platforms
    
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    
    const options = wrapper.findAll('option')
    expect(options).toHaveLength(3) // 包括默认选项
    expect(options[1].text()).toBe('测试平台1 (AWS)')
    expect(options[2].text()).toBe('测试平台2 (GCP)')
  })

  it('应该显示当前云平台信息', async () => {
    const currentPlatform = {
      id: 1,
      name: '当前平台',
      provider: 'Kubernetes',
      status: 'CONNECTED'
    }
    
    mockCiStore.currentCloudPlatform.value = currentPlatform
    
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    
    const currentInfo = wrapper.find('.bg-green-50')
    expect(currentInfo.exists()).toBe(true)
    expect(currentInfo.text()).toContain('当前云平台: 当前平台')
    expect(currentInfo.text()).toContain('提供商: Kubernetes')
    expect(currentInfo.text()).toContain('状态: CONNECTED')
  })

  it('应该在选择云平台时调用selectCloudPlatform', async () => {
    const platforms = [
      { id: 1, name: '测试平台', provider: 'AWS' }
    ]
    
    mockCiStore.availableCloudPlatforms.value = platforms
    
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    
    const select = wrapper.find('select')
    await select.setValue('1')
    
    expect(mockCiStore.selectCloudPlatform).toHaveBeenCalledWith(1)
  })

  it('应该显示测试连接按钮当选择了不同的云平台', async () => {
    const platforms = [
      { id: 1, name: '测试平台', provider: 'AWS' }
    ]
    
    mockCiStore.availableCloudPlatforms.value = platforms
    mockCiStore.currentCloudPlatform.value = { id: 2, name: '当前平台' }
    
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    
    // 设置选中的平台ID
    wrapper.vm.selectedPlatformId = 1
    await wrapper.vm.$nextTick()
    
    const testButton = wrapper.find('button')
    expect(testButton.exists()).toBe(true)
    expect(testButton.text()).toBe('测试连接')
  })

  it('应该在点击测试连接时调用testCloudPlatformConnection', async () => {
    const platforms = [
      { id: 1, name: '测试平台', provider: 'AWS' }
    ]
    
    mockCiStore.availableCloudPlatforms.value = platforms
    mockCiStore.currentCloudPlatform.value = { id: 2, name: '当前平台' }
    
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    
    // 设置选中的平台ID
    wrapper.vm.selectedPlatformId = 1
    await wrapper.vm.$nextTick()
    
    const testButton = wrapper.find('button')
    await testButton.trigger('click')
    
    expect(mockCiStore.testCloudPlatformConnection).toHaveBeenCalledWith(1)
  })

  it('应该显示连接测试成功结果', async () => {
    const platforms = [
      { id: 1, name: '测试平台', provider: 'AWS' }
    ]
    
    mockCiStore.availableCloudPlatforms.value = platforms
    mockCiStore.currentCloudPlatform.value = { id: 2, name: '当前平台' }
    
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    
    // 设置选中的平台ID
    wrapper.vm.selectedPlatformId = 1
    await wrapper.vm.$nextTick()
    
    // 模拟测试连接
    const testButton = wrapper.find('button')
    await testButton.trigger('click')
    
    // 设置测试结果
    wrapper.vm.testResult = {
      connected: true,
      kubernetesVersion: 'v1.24.0'
    }
    await wrapper.vm.$nextTick()
    
    const successResults = wrapper.findAll('.bg-green-50')
    const testResult = successResults.find(el => el.text().includes('连接成功'))
    expect(testResult).toBeTruthy()
    expect(testResult!.text()).toContain('连接成功')
    expect(testResult!.text()).toContain('Kubernetes版本: v1.24.0')
  })

  it('应该显示连接测试失败结果', async () => {
    const platforms = [
      { id: 1, name: '测试平台', provider: 'AWS' }
    ]
    
    mockCiStore.availableCloudPlatforms.value = platforms
    mockCiStore.currentCloudPlatform.value = { id: 2, name: '当前平台' }
    
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    
    // 设置选中的平台ID
    wrapper.vm.selectedPlatformId = 1
    await wrapper.vm.$nextTick()

    // 设置测试失败结果
    wrapper.vm.testResult = {
      connected: false
    }
    await wrapper.vm.$nextTick()
    
    const failureResult = wrapper.find('.bg-red-50')
    expect(failureResult.text()).toContain('连接失败')
  })

  it('应该显示错误信息', async () => {
    mockCiStore.error.value = '获取云平台列表失败'
    
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    
    const errorMessage = wrapper.find('.bg-red-50')
    expect(errorMessage.text()).toContain('获取云平台列表失败')
  })

  it('应该在加载时禁用选择框', async () => {
    mockCiStore.loading.value = true
    
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    
    const select = wrapper.find('select')
    expect(select.attributes('disabled')).toBeDefined()
  })

  it('应该在云平台加载时禁用选择框', async () => {
    mockCiStore.cloudPlatformLoading.value = true
    
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    
    const select = wrapper.find('select')
    expect(select.attributes('disabled')).toBeDefined()
  })

  it('应该在测试连接时禁用测试按钮', async () => {
    const platforms = [
      { id: 1, name: '测试平台', provider: 'AWS' }
    ]
    
    mockCiStore.availableCloudPlatforms.value = platforms
    mockCiStore.currentCloudPlatform.value = { id: 2, name: '当前平台' }
    
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    
    // 设置选中的平台ID
    wrapper.vm.selectedPlatformId = 1
    await wrapper.vm.$nextTick()

    // 设置测试状态
    wrapper.vm.testing = true
    await wrapper.vm.$nextTick()
    
    const testButton = wrapper.find('button')
    expect(testButton.attributes('disabled')).toBeDefined()
    expect(testButton.text()).toBe('测试中...')
  })

  it('应该监听当前云平台变化并更新选中值', async () => {
    const newPlatform = { id: 3, name: '新平台' }
    
    wrapper = createWrapper()
    await wrapper.vm.$nextTick()
    
    // 模拟当前云平台变化
    mockCiStore.currentCloudPlatform.value = newPlatform
    await wrapper.vm.$nextTick()
    
    expect(wrapper.vm.selectedPlatformId).toBe(3)
  })
})
