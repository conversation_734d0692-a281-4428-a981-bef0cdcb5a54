import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import TaskExecutionModal from '../TaskExecutionModal.vue'
import { useCiStore } from '../../stores/ci'
import { useCdStore } from '../../stores/cd'

// Mock the stores
vi.mock('../../stores/ci')
vi.mock('../../stores/cd')

// Mock child components
vi.mock('../CloudPlatformSelector.vue', () => ({
  default: {
    name: 'CloudPlatformSelector',
    template: '<div class="cloud-platform-selector-mock">CloudPlatformSelector</div>',
    props: ['taskType']
  }
}))

vi.mock('../TaskStatusBadge.vue', () => ({
  default: {
    name: 'TaskStatusBadge',
    template: '<span class="task-status-badge-mock">{{ status }}</span>',
    props: ['status']
  }
}))

describe('TaskExecutionModal', () => {
  let wrapper: any
  let mockCiStore: any
  let mockCdStore: any

  const mockCiTask = {
    id: 1,
    name: '测试CI任务',
    taskType: 'build',
    triggerType: 'manual',
    status: 'ACTIVE',
    description: '这是一个测试CI任务',
    timeout: 30
  }

  const mockCdTask = {
    id: 2,
    name: '测试CD任务',
    taskType: 'deploy',
    triggerType: 'manual',
    status: 'ACTIVE',
    description: '这是一个测试CD任务',
    timeout: 60
  }

  beforeEach(() => {
    setActivePinia(createPinia())
    
    // Mock store methods and properties
    mockCiStore = {
      currentCloudPlatform: { id: 1, name: '测试平台' },
      executeTask: vi.fn().mockResolvedValue({ success: true })
    }

    mockCdStore = {
      currentCloudPlatform: { id: 1, name: '测试平台' },
      deployTask: vi.fn().mockResolvedValue({ success: true })
    }

    // Mock the store composables
    vi.mocked(useCiStore).mockReturnValue(mockCiStore)
    vi.mocked(useCdStore).mockReturnValue(mockCdStore)
  })

  const createWrapper = (props = {}) => {
    return mount(TaskExecutionModal, {
      props: {
        visible: true,
        task: mockCiTask,
        taskType: 'ci',
        ...props
      },
      global: {
        plugins: [createPinia()]
      }
    })
  }

  it('应该正确渲染组件', () => {
    wrapper = createWrapper()
    
    expect(wrapper.find('.modal-overlay').exists()).toBe(true)
    expect(wrapper.find('.modal-container').exists()).toBe(true)
    expect(wrapper.find('h3').text()).toBe('执行CI任务')
  })

  it('应该根据taskType显示正确的标题', () => {
    // 测试CI任务
    wrapper = createWrapper({ taskType: 'ci' })
    expect(wrapper.find('h3').text()).toBe('执行CI任务')

    // 测试CD任务
    wrapper = createWrapper({ taskType: 'cd', task: mockCdTask })
    expect(wrapper.find('h3').text()).toBe('执行CD任务')
  })

  it('应该显示任务信息', () => {
    wrapper = createWrapper()
    
    const taskInfo = wrapper.find('.task-info')
    expect(taskInfo.text()).toContain('测试CI任务')
    expect(taskInfo.text()).toContain('build')
    expect(taskInfo.text()).toContain('manual')
    expect(taskInfo.text()).toContain('这是一个测试CI任务')
  })

  it('应该包含云平台选择器组件', () => {
    wrapper = createWrapper()
    
    const cloudPlatformSelector = wrapper.find('.cloud-platform-selector-mock')
    expect(cloudPlatformSelector.exists()).toBe(true)
  })

  it('应该显示CI任务特定的执行参数', () => {
    wrapper = createWrapper({ taskType: 'ci' })
    
    const branchInput = wrapper.find('input[placeholder="main"]')
    expect(branchInput.exists()).toBe(true)
    
    const branchLabel = wrapper.find('label').filter(label => 
      label.text().includes('构建分支')
    )
    expect(branchLabel.length).toBeGreaterThan(0)
  })

  it('应该显示CD任务特定的执行参数', () => {
    wrapper = createWrapper({ taskType: 'cd', task: mockCdTask })
    
    const environmentSelect = wrapper.find('select').filter(select => 
      select.findAll('option').some(option => option.text() === '开发环境')
    )
    expect(environmentSelect.length).toBeGreaterThan(0)
  })

  it('应该在任务变化时重置执行参数', async () => {
    wrapper = createWrapper()
    
    // 修改参数
    const environmentTextarea = wrapper.find('textarea')
    await environmentTextarea.setValue('TEST_ENV=production')
    
    // 更改任务
    const newTask = { ...mockCiTask, id: 2, timeout: 45 }
    await wrapper.setProps({ task: newTask })
    
    // 验证参数被重置
    expect(wrapper.vm.executionParams.environment).toBe('')
    expect(wrapper.vm.executionParams.timeout).toBe(45)
  })

  it('应该在有云平台时启用执行按钮', () => {
    wrapper = createWrapper()
    
    const executeButton = wrapper.find('.btn-primary')
    expect(executeButton.attributes('disabled')).toBeUndefined()
    expect(executeButton.text()).toBe('开始执行')
  })

  it('应该在没有云平台时禁用执行按钮', () => {
    mockCiStore.currentCloudPlatform = null
    
    wrapper = createWrapper()
    
    const executeButton = wrapper.find('.btn-primary')
    expect(executeButton.attributes('disabled')).toBeDefined()
  })

  it('应该在任务运行中时禁用执行按钮', () => {
    const runningTask = { ...mockCiTask, status: 'RUNNING' }
    
    wrapper = createWrapper({ task: runningTask })
    
    const executeButton = wrapper.find('.btn-primary')
    expect(executeButton.attributes('disabled')).toBeDefined()
  })

  it('应该在执行中显示加载状态', async () => {
    wrapper = createWrapper()
    
    // 设置执行状态
    wrapper.vm.executing = true
    await wrapper.vm.$nextTick()
    
    const executeButton = wrapper.find('.btn-primary')
    expect(executeButton.text()).toBe('执行中...')
    expect(executeButton.find('svg.animate-spin').exists()).toBe(true)
  })

  it('应该在点击执行按钮时调用正确的store方法', async () => {
    wrapper = createWrapper()
    
    // 设置执行参数
    wrapper.vm.executionParams = {
      environment: 'NODE_ENV=production',
      branch: 'develop',
      timeout: 45
    }
    await wrapper.vm.$nextTick()
    
    const executeButton = wrapper.find('.btn-primary')
    await executeButton.trigger('click')
    
    expect(mockCiStore.executeTask).toHaveBeenCalledWith(1, {
      timeout: 45,
      environment: { NODE_ENV: 'production' },
      branch: 'develop'
    })
  })

  it('应该为CD任务调用deployTask方法', async () => {
    wrapper = createWrapper({ taskType: 'cd', task: mockCdTask })
    
    // 设置执行参数
    wrapper.vm.executionParams = {
      environment: 'production',
      timeout: 60
    }
    await wrapper.vm.$nextTick()
    
    const executeButton = wrapper.find('.btn-primary')
    await executeButton.trigger('click')
    
    expect(mockCdStore.deployTask).toHaveBeenCalledWith(2, {
      timeout: 60,
      deploymentEnvironment: 'production'
    })
  })

  it('应该正确解析环境变量', async () => {
    wrapper = createWrapper()
    
    // 设置多行环境变量
    wrapper.vm.executionParams = {
      environment: 'NODE_ENV=production\nDEBUG=true\nPORT=3000',
      timeout: 30
    }
    await wrapper.vm.$nextTick()
    
    const executeButton = wrapper.find('.btn-primary')
    await executeButton.trigger('click')
    
    expect(mockCiStore.executeTask).toHaveBeenCalledWith(1, {
      timeout: 30,
      environment: {
        NODE_ENV: 'production',
        DEBUG: 'true',
        PORT: '3000'
      }
    })
  })

  it('应该在执行成功后发出executed事件并关闭模态框', async () => {
    wrapper = createWrapper()
    
    const executeButton = wrapper.find('.btn-primary')
    await executeButton.trigger('click')
    
    // 等待异步操作完成
    await wrapper.vm.$nextTick()
    
    expect(wrapper.emitted('executed')).toBeTruthy()
    expect(wrapper.emitted('close')).toBeTruthy()
  })

  it('应该在点击取消按钮时发出close事件', async () => {
    wrapper = createWrapper()
    
    const cancelButton = wrapper.find('.btn-secondary')
    await cancelButton.trigger('click')
    
    expect(wrapper.emitted('close')).toBeTruthy()
  })

  it('应该在点击关闭按钮时发出close事件', async () => {
    wrapper = createWrapper()
    
    const closeButton = wrapper.find('.close-btn')
    await closeButton.trigger('click')
    
    expect(wrapper.emitted('close')).toBeTruthy()
  })

  it('应该在点击遮罩层时发出close事件', async () => {
    wrapper = createWrapper()
    
    const overlay = wrapper.find('.modal-overlay')
    await overlay.trigger('click')
    
    expect(wrapper.emitted('close')).toBeTruthy()
  })

  it('应该在点击模态框内容时不发出close事件', async () => {
    wrapper = createWrapper()
    
    const container = wrapper.find('.modal-container')
    await container.trigger('click')
    
    expect(wrapper.emitted('close')).toBeFalsy()
  })

  it('应该在不可见时不渲染模态框', () => {
    wrapper = createWrapper({ visible: false })
    
    expect(wrapper.find('.modal-overlay').exists()).toBe(false)
  })

  it('应该处理执行失败的情况', async () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    mockCiStore.executeTask.mockRejectedValue(new Error('执行失败'))
    
    wrapper = createWrapper()
    
    const executeButton = wrapper.find('.btn-primary')
    await executeButton.trigger('click')
    
    // 等待异步操作完成
    await wrapper.vm.$nextTick()
    
    expect(consoleErrorSpy).toHaveBeenCalledWith('执行任务失败:', expect.any(Error))
    expect(wrapper.vm.executing).toBe(false)
    
    consoleErrorSpy.mockRestore()
  })
})
