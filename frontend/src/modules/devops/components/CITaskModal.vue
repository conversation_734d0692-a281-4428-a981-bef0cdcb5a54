<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3>{{ isEdit ? '编辑CI任务' : '创建CI任务' }}</h3>
        <button @click="$emit('close')" class="close-btn">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <form @submit.prevent="handleSubmit">
          <div class="form-grid">
            <!-- 基本信息 -->
            <div class="form-section">
              <h4>基本信息</h4>
              <div class="form-group">
                <label>任务名称 *</label>
                <input
                  v-model="formData.name"
                  type="text"
                  required
                  placeholder="请输入任务名称"
                />
              </div>
              <div class="form-group">
                <label>描述</label>
                <textarea
                  v-model="formData.description"
                  placeholder="请输入任务描述"
                  rows="3"
                ></textarea>
              </div>
              <div class="form-group">
                <label>组件ID *</label>
                <input
                  v-model.number="formData.componentId"
                  type="number"
                  required
                  placeholder="请输入组件ID"
                />
              </div>
              <div class="form-group">
                <label>任务类型 *</label>
                <select v-model="formData.taskType" required>
                  <option value="">请选择任务类型</option>
                  <option value="build">构建</option>
                  <option value="test">测试</option>
                  <option value="lint">代码检查</option>
                  <option value="security">安全扫描</option>
                </select>
              </div>
            </div>

            <!-- 触发配置 -->
            <div class="form-section">
              <h4>触发配置</h4>
              <div class="form-group">
                <label>触发类型 *</label>
                <select v-model="formData.triggerType" required>
                  <option value="">请选择触发类型</option>
                  <option value="push">代码推送</option>
                  <option value="manual">手动触发</option>
                  <option value="schedule">定时触发</option>
                  <option value="webhook">Webhook</option>
                </select>
              </div>
              <div v-if="formData.triggerType === 'schedule'" class="form-group">
                <label>定时表达式</label>
                <input
                  v-model="formData.configuration.schedule"
                  type="text"
                  placeholder="0 0 2 * * ? (每天凌晨2点)"
                />
              </div>
              <div class="form-group">
                <label>超时时间 (分钟)</label>
                <input
                  v-model.number="formData.timeout"
                  type="number"
                  min="1"
                  placeholder="30"
                />
              </div>
            </div>
          </div>

          <!-- 执行配置 -->
          <div class="form-section full-width">
            <h4>执行配置</h4>
            <div class="form-grid">
              <div class="form-group">
                <label>工作目录</label>
                <input
                  v-model="formData.configuration.workingDirectory"
                  type="text"
                  placeholder="/workspace"
                />
              </div>
              <div class="form-group">
                <label>环境变量</label>
                <input
                  v-model="formData.configuration.environment"
                  type="text"
                  placeholder="NODE_ENV=production"
                />
              </div>
            </div>
            <div class="form-group">
              <label>执行脚本 *</label>
              <textarea
                v-model="formData.configuration.script"
                required
                placeholder="npm ci && npm run build"
                rows="6"
              ></textarea>
            </div>
          </div>

          <!-- 通知配置 -->
          <div class="form-section full-width">
            <h4>通知配置</h4>
            <div class="form-group">
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input
                    v-model="formData.configuration.notifyOnSuccess"
                    type="checkbox"
                  />
                  成功时通知
                </label>
                <label class="checkbox-label">
                  <input
                    v-model="formData.configuration.notifyOnFailure"
                    type="checkbox"
                  />
                  失败时通知
                </label>
              </div>
            </div>
          </div>

          <div class="modal-actions">
            <button type="button" @click="$emit('close')" class="btn btn-secondary">
              取消
            </button>
            <button type="submit" class="btn btn-primary" :disabled="loading">
              {{ loading ? '保存中...' : (isEdit ? '更新' : '创建') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { DevOpsCiTask, DevOpsCiTaskConfiguration } from '@/modules/devops/types/devops'

interface Props {
  visible: boolean
  task?: DevOpsCiTask | null
  loading?: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'submit', data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  task: null,
  loading: false
})

const emit = defineEmits<Emits>()

const isEdit = computed(() => !!props.task)

const formData = ref({
  name: '',
  description: '',
  componentId: 0,
  taskType: '',
  triggerType: '',
  timeout: 30,
  configuration: {
    workingDirectory: '/workspace',
    environment: '',
    script: '',
    notifyOnSuccess: false,
    notifyOnFailure: true,
    notificationChannels: [],
    schedule: ''
  } as DevOpsCiTaskConfiguration
})

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    componentId: 0,
    taskType: '',
    triggerType: '',
    timeout: 30,
    configuration: {
      workingDirectory: '/workspace',
      environment: '',
      script: '',
      notifyOnSuccess: false,
      notifyOnFailure: true,
      notificationChannels: [],
      schedule: ''
    } as DevOpsCiTaskConfiguration
  }
}

watch(() => props.task, (newTask) => {
  if (newTask) {
    formData.value = {
      name: newTask.name,
      description: newTask.description || '',
      componentId: newTask.componentId,
      taskType: newTask.taskType,
      triggerType: newTask.triggerType || '',
      timeout: newTask.timeout || 30,
      configuration: { ...newTask.configuration }
    }
  } else {
    resetForm()
  }
}, { immediate: true })

const handleSubmit = () => {
  emit('submit', formData.value)
}

const handleOverlayClick = () => {
  emit('close')
}
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.modal-header h3 {
  @apply text-lg font-semibold text-gray-900;
}

.close-btn {
  @apply p-2 hover:bg-gray-100 rounded-lg transition-colors;
}

.close-btn svg {
  @apply w-5 h-5 text-gray-500;
}

.modal-body {
  @apply p-6 overflow-y-auto max-h-[calc(90vh-120px)];
}

.form-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.form-section {
  @apply space-y-4;
}

.form-section.full-width {
  @apply lg:col-span-2;
}

.form-section h4 {
  @apply text-base font-medium text-gray-900 border-b border-gray-200 pb-2;
}

.form-group {
  @apply space-y-2;
}

.form-group label {
  @apply block text-sm font-medium text-gray-700;
}

.form-group input,
.form-group select,
.form-group textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.checkbox-group {
  @apply flex flex-wrap gap-4;
}

.checkbox-label {
  @apply flex items-center space-x-2 text-sm;
}

.checkbox-label input[type="checkbox"] {
  @apply w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500;
}

.modal-actions {
  @apply flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200;
}

.btn {
  @apply px-4 py-2 rounded-md font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}
</style>
