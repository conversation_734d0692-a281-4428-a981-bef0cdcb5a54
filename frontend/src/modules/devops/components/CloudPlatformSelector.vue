<template>
  <div class="cloud-platform-selector">
    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        选择云平台
      </label>
      <div class="relative">
        <select
          v-model="selectedPlatformId"
          @change="handlePlatformChange"
          :disabled="loading || cloudPlatformLoading"
          class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        >
          <option value="">请选择云平台</option>
          <option
            v-for="platform in availableCloudPlatforms"
            :key="platform.id"
            :value="platform.id"
          >
            {{ platform.name }} ({{ platform.provider }})
          </option>
        </select>
        <div v-if="loading || cloudPlatformLoading" class="absolute inset-y-0 right-0 flex items-center pr-3">
          <svg class="animate-spin h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      </div>
    </div>

    <!-- 当前云平台信息 -->
    <div v-if="currentCloudPlatform" class="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
      <div class="flex items-center">
        <svg class="h-5 w-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>
        <span class="text-sm font-medium text-green-800">
          当前云平台: {{ currentCloudPlatform.name }}
        </span>
      </div>
      <div class="mt-1 text-xs text-green-600">
        提供商: {{ currentCloudPlatform.provider }} | 
        状态: {{ currentCloudPlatform.status }}
      </div>
    </div>

    <!-- 云平台连接测试 -->
    <div v-if="selectedPlatformId && selectedPlatformId !== currentCloudPlatform?.id" class="mb-4">
      <button
        @click="testConnection"
        :disabled="testing"
        class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
      >
        <svg v-if="testing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        {{ testing ? '测试中...' : '测试连接' }}
      </button>
    </div>

    <!-- 连接测试结果 -->
    <div v-if="testResult" class="mb-4">
      <div v-if="testResult.connected" class="p-3 bg-green-50 border border-green-200 rounded-md">
        <div class="flex items-center">
          <svg class="h-5 w-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <span class="text-sm font-medium text-green-800">连接成功</span>
        </div>
        <div class="mt-1 text-xs text-green-600">
          Kubernetes版本: {{ testResult.kubernetesVersion }}
        </div>
      </div>
      <div v-else class="p-3 bg-red-50 border border-red-200 rounded-md">
        <div class="flex items-center">
          <svg class="h-5 w-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
          <span class="text-sm font-medium text-red-800">连接失败</span>
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
      <div class="flex items-center">
        <svg class="h-5 w-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
        </svg>
        <span class="text-sm font-medium text-red-800">{{ error }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useCiStore } from '../stores/ci'
import { useCdStore } from '../stores/cd'

interface Props {
  taskType: 'ci' | 'cd'
}

const props = defineProps<Props>()

// 根据任务类型选择对应的store
const store = computed(() => {
  return props.taskType === 'ci' ? useCiStore() : useCdStore()
})

// 响应式状态
const selectedPlatformId = ref<number | ''>('')
const testing = ref(false)
const testResult = ref<any>(null)

// 从store获取状态
const availableCloudPlatforms = computed(() => store.value.availableCloudPlatforms)
const currentCloudPlatform = computed(() => store.value.currentCloudPlatform)
const cloudPlatformLoading = computed(() => store.value.cloudPlatformLoading)
const loading = computed(() => store.value.loading)
const error = computed(() => store.value.error)

// 方法
const handlePlatformChange = async () => {
  if (selectedPlatformId.value) {
    testResult.value = null
    try {
      await store.value.selectCloudPlatform(Number(selectedPlatformId.value))
    } catch (err) {
      console.error('选择云平台失败:', err)
    }
  }
}

const testConnection = async () => {
  if (!selectedPlatformId.value) return
  
  testing.value = true
  testResult.value = null
  
  try {
    const result = await store.value.testCloudPlatformConnection(Number(selectedPlatformId.value))
    testResult.value = result
  } catch (err) {
    console.error('测试连接失败:', err)
    testResult.value = { connected: false }
  } finally {
    testing.value = false
  }
}

// 监听当前云平台变化
watch(currentCloudPlatform, (newPlatform) => {
  if (newPlatform?.id) {
    selectedPlatformId.value = newPlatform.id
  }
}, { immediate: true })

// 组件挂载时获取云平台列表和当前云平台
onMounted(async () => {
  try {
    await Promise.all([
      store.value.fetchAvailableCloudPlatforms(),
      store.value.fetchCurrentCloudPlatform()
    ])
  } catch (err) {
    console.error('初始化云平台信息失败:', err)
  }
})
</script>

<style scoped>
.cloud-platform-selector {
  @apply space-y-4;
}
</style>
