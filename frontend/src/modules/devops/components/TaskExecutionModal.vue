<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3>执行{{ taskType === 'ci' ? 'CI' : 'CD' }}任务</h3>
        <button @click="$emit('close')" class="close-btn">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <!-- 任务信息 -->
        <div class="task-info mb-6">
          <h4 class="text-lg font-medium text-gray-900 mb-2">任务信息</h4>
          <div class="bg-gray-50 p-4 rounded-md">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <span class="text-sm font-medium text-gray-500">任务名称:</span>
                <p class="text-sm text-gray-900">{{ task?.name }}</p>
              </div>
              <div v-if="'taskType' in task">
                <span class="text-sm font-medium text-gray-500">任务类型:</span>
                <p class="text-sm text-gray-900">{{ task?.taskType }}</p>
              </div>
              <div v-if="'triggerType' in task">
                <span class="text-sm font-medium text-gray-500">触发类型:</span>
                <p class="text-sm text-gray-900">{{ task?.triggerType }}</p>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-500">状态:</span>
                <TaskStatusBadge :status="task?.status" />
              </div>
            </div>
            <div v-if="task?.description" class="mt-3">
              <span class="text-sm font-medium text-gray-500">描述:</span>
              <p class="text-sm text-gray-900">{{ task.description }}</p>
            </div>
          </div>
        </div>

        <!-- 云平台选择 -->
        <div class="cloud-platform-section mb-6">
          <h4 class="text-lg font-medium text-gray-900 mb-3">云平台配置</h4>
          <CloudPlatformSelector :task-type="taskType" />
        </div>

        <!-- 执行参数 -->
        <div class="execution-params mb-6">
          <h4 class="text-lg font-medium text-gray-900 mb-3">执行参数</h4>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                环境变量
              </label>
              <textarea
                v-model="executionParams.environment"
                placeholder="KEY1=value1&#10;KEY2=value2"
                rows="3"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              ></textarea>
              <p class="mt-1 text-xs text-gray-500">每行一个环境变量，格式: KEY=VALUE</p>
            </div>

            <div v-if="taskType === 'ci'">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                构建分支
              </label>
              <input
                v-model="executionParams.branch"
                type="text"
                placeholder="main"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>

            <div v-if="taskType === 'cd'">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                部署环境
              </label>
              <select
                v-model="executionParams.environment"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">请选择部署环境</option>
                <option value="development">开发环境</option>
                <option value="staging">测试环境</option>
                <option value="production">生产环境</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                超时时间 (分钟)
              </label>
              <input
                v-model.number="executionParams.timeout"
                type="number"
                min="1"
                max="120"
                placeholder="30"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button
          @click="$emit('close')"
          type="button"
          class="btn btn-secondary mr-3"
        >
          取消
        </button>
        <button
          @click="handleExecute"
          :disabled="executing || !canExecute"
          type="button"
          class="btn btn-primary"
        >
          <svg v-if="executing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ executing ? '执行中...' : '开始执行' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import CloudPlatformSelector from './CloudPlatformSelector.vue'
import TaskStatusBadge from './TaskStatusBadge.vue'
import { useCiStore } from '../stores/ci'
import { useCdStore } from '../stores/cd'
import type { DevOpsCiTask, DevOpsCdTask } from '../types/devops'

interface Props {
  visible: boolean
  task: DevOpsCiTask | DevOpsCdTask | null
  taskType: 'ci' | 'cd'
}

interface Emits {
  close: []
  executed: [result: any]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 根据任务类型选择对应的store
const store = computed(() => {
  return props.taskType === 'ci' ? useCiStore() : useCdStore()
})

// 响应式状态
const executing = ref(false)
const executionParams = ref({
  environment: '',
  branch: 'main',
  timeout: 30
})

// 计算属性
const canExecute = computed(() => {
  return props.task && 
         props.task.status !== 'RUNNING' && 
         store.value.currentCloudPlatform
})

// 方法
const handleOverlayClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    emit('close')
  }
}

const handleExecute = async () => {
  if (!props.task || !canExecute.value) return

  executing.value = true
  
  try {
    // 准备执行参数
    const params: any = {
      timeout: executionParams.value.timeout
    }

    // 处理环境变量
    if (executionParams.value.environment) {
      const envLines = executionParams.value.environment.split('\n')
      const envVars: Record<string, string> = {}
      envLines.forEach(line => {
        const [key, ...valueParts] = line.split('=')
        if (key && valueParts.length > 0) {
          envVars[key.trim()] = valueParts.join('=').trim()
        }
      })
      params.environment = envVars
    }

    // CI特定参数
    if (props.taskType === 'ci' && executionParams.value.branch) {
      params.branch = executionParams.value.branch
    }

    // CD特定参数
    if (props.taskType === 'cd' && executionParams.value.environment) {
      params.deploymentEnvironment = executionParams.value.environment
    }

    // 执行任务
    let result
    if (props.taskType === 'ci') {
      result = await (store.value as any).executeTask(props.task.id!, params)
    } else {
      result = await (store.value as any).deployTask(props.task.id!, params)
    }

    emit('executed', result)
    emit('close')
  } catch (error) {
    console.error('执行任务失败:', error)
  } finally {
    executing.value = false
  }
}

// 监听任务变化，重置参数
watch(() => props.task, (newTask) => {
  if (newTask) {
    executionParams.value = {
      environment: '',
      branch: 'main',
      timeout: newTask.timeout || 30
    }
  }
})
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50;
}

.modal-container {
  @apply relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white;
}

.modal-header {
  @apply flex justify-between items-center pb-3 border-b border-gray-200;
}

.modal-header h3 {
  @apply text-lg font-medium text-gray-900;
}

.close-btn {
  @apply text-gray-400 hover:text-gray-600 transition-colors;
}

.close-btn svg {
  @apply w-6 h-6;
}

.modal-body {
  @apply py-4 max-h-96 overflow-y-auto;
}

.modal-footer {
  @apply flex justify-end pt-3 border-t border-gray-200;
}

.btn {
  @apply px-4 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:ring-blue-500;
}
</style>
