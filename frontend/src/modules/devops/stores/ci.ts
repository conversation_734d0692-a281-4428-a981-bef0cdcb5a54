/**
 * CI 任务状态管理
 * 管理持续集成任务的状态和操作
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { DevOpsCiTask, DevOpsCiTaskInstance, QueryParams } from '@/modules/devops/types/devops'
import { ciTaskApi, cloudPlatformIntegrationApi } from '@/modules/devops/services/devopsApi'

// 请求类型定义
export interface CreateCiTaskRequest {
  name: string
  description?: string
  componentId: number
  taskType: string
  triggerType: string
  schedule?: string
  timeout?: number
  configuration: {
    workingDirectory: string
    environment: string
    script: string
    notifyOnSuccess: boolean
    notifyOnFailure: boolean
    notificationChannels: string[]
  }
}

export interface UpdateCiTaskRequest extends Partial<CreateCiTaskRequest> {
  status?: string
}

export const useCiStore = defineStore('ci', () => {
  // 状态定义
  const tasks = ref<DevOpsCiTask[]>([])
  const currentTask = ref<DevOpsCiTask | null>(null)
  const taskInstances = ref<DevOpsCiTaskInstance[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 云平台相关状态
  const availableCloudPlatforms = ref<any[]>([])
  const currentCloudPlatform = ref<any | null>(null)
  const cloudPlatformLoading = ref(false)

  // 计算属性
  const activeTasks = computed(() => 
    tasks.value.filter(task => task.status === 'ACTIVE')
  )
  
  const runningTasks = computed(() =>
    tasks.value.filter(task => task.status === 'RUNNING')
  )

  const completedTasks = computed(() =>
    tasks.value.filter(task => task.status === 'COMPLETED')
  )

  const failedTasks = computed(() =>
    tasks.value.filter(task => task.status === 'FAILED')
  )

  const tasksByType = computed(() => {
    const result: Record<string, DevOpsCiTask[]> = {}
    tasks.value.forEach(task => {
      if (!result[task.taskType]) {
        result[task.taskType] = []
      }
      result[task.taskType].push(task)
    })
    return result
  })

  const tasksByComponent = computed(() => {
    const result: Record<number, DevOpsCiTask[]> = {}
    tasks.value.forEach(task => {
      if (!result[task.componentId]) {
        result[task.componentId] = []
      }
      result[task.componentId].push(task)
    })
    return result
  })

  // 操作方法
  const fetchTasks = async (params?: QueryParams) => {
    loading.value = true
    error.value = null
    try {
      const response = await ciTaskApi.getAll(params)
      tasks.value = response
    } catch (err: any) {
      error.value = err.message || '获取 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchTaskById = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      const task = await ciTaskApi.getById(id)
      currentTask.value = task
      return task
    } catch (err: any) {
      error.value = err.message || '获取 CI 任务详情失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const createTask = async (taskData: Omit<DevOpsCiTask, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null
    try {
      const newTask = await ciTaskApi.create(taskData)
      tasks.value.unshift(newTask)
      return newTask
    } catch (err: any) {
      error.value = err.message || '创建 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateTask = async (id: number, taskData: Partial<DevOpsCiTask>) => {
    loading.value = true
    error.value = null
    try {
      const updatedTask = await ciTaskApi.update(id, taskData)
      const index = tasks.value.findIndex(t => t.id === id)
      if (index !== -1) {
        tasks.value[index] = updatedTask
      }
      if (currentTask.value?.id === id) {
        currentTask.value = updatedTask
      }
      return updatedTask
    } catch (err: any) {
      error.value = err.message || '更新 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteTask = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      await ciTaskApi.delete(id)
      const index = tasks.value.findIndex(t => t.id === id)
      if (index !== -1) {
        tasks.value.splice(index, 1)
      }
      if (currentTask.value?.id === id) {
        currentTask.value = null
      }
    } catch (err: any) {
      error.value = err.message || '删除 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const executeTask = async (id: number, params?: any) => {
    loading.value = true
    error.value = null
    try {
      // 如果指定了云平台，添加到参数中
      const taskParams = { ...params }
      if (currentCloudPlatform.value?.id) {
        taskParams.cloudPlatformId = currentCloudPlatform.value.id
      }

      const result = await ciTaskApi.start(id, taskParams)

      // 更新任务状态为运行中
      const taskIndex = tasks.value.findIndex(t => t.id === id)
      if (taskIndex !== -1) {
        tasks.value[taskIndex] = {
          ...tasks.value[taskIndex],
          status: 'RUNNING',
          updatedAt: new Date().toISOString()
        }
      }

      return result
    } catch (err: any) {
      error.value = err.message || '执行 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const stopTask = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      await ciTaskApi.stop(id)

      // 更新任务状态
      const taskIndex = tasks.value.findIndex(t => t.id === id)
      if (taskIndex !== -1) {
        tasks.value[taskIndex] = {
          ...tasks.value[taskIndex],
          status: 'STOPPED',
          updatedAt: new Date().toISOString()
        }
      }
    } catch (err: any) {
      error.value = err.message || '停止 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchTaskLogs = async (instanceId: string) => {
    loading.value = true
    error.value = null
    try {
      const logs = await ciTaskApi.getInstanceLogs(instanceId)
      return logs
    } catch (err: any) {
      error.value = err.message || '获取任务日志失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchTaskInstances = async (taskId: number) => {
    loading.value = true
    error.value = null
    try {
      const instances = await ciTaskApi.getInstances(taskId)
      taskInstances.value = instances
      return instances
    } catch (err: any) {
      error.value = err.message || '获取任务实例失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchInstanceStatus = async (instanceId: string) => {
    loading.value = true
    error.value = null
    try {
      const status = await ciTaskApi.getInstanceStatus(instanceId)
      return status
    } catch (err: any) {
      error.value = err.message || '获取实例状态失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const stopInstance = async (instanceId: string) => {
    loading.value = true
    error.value = null
    try {
      await ciTaskApi.stopInstance(instanceId)
    } catch (err: any) {
      error.value = err.message || '停止实例失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const cancelInstance = async (instanceId: string) => {
    loading.value = true
    error.value = null
    try {
      await ciTaskApi.cancelInstance(instanceId)
    } catch (err: any) {
      error.value = err.message || '取消实例失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const cleanupInstances = async (taskId: number, keepCount: number = 10) => {
    loading.value = true
    error.value = null
    try {
      const cleanedCount = await ciTaskApi.cleanupInstances(taskId, keepCount)
      // 重新获取实例列表
      await fetchTaskInstances(taskId)
      return cleanedCount
    } catch (err: any) {
      error.value = err.message || '清理实例失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const resetState = () => {
    tasks.value = []
    currentTask.value = null
    taskInstances.value = []
    loading.value = false
    error.value = null
    availableCloudPlatforms.value = []
    currentCloudPlatform.value = null
    cloudPlatformLoading.value = false
  }

  // 云平台相关方法
  const fetchAvailableCloudPlatforms = async () => {
    cloudPlatformLoading.value = true
    try {
      const platforms = await cloudPlatformIntegrationApi.getAvailablePlatforms()
      availableCloudPlatforms.value = platforms
    } catch (err: any) {
      error.value = err.message || '获取云平台列表失败'
      throw err
    } finally {
      cloudPlatformLoading.value = false
    }
  }

  const selectCloudPlatform = async (platformId: number) => {
    cloudPlatformLoading.value = true
    try {
      const result = await cloudPlatformIntegrationApi.selectPlatform(platformId)
      if (result.success) {
        await fetchCurrentCloudPlatform()
      }
      return result
    } catch (err: any) {
      error.value = err.message || '选择云平台失败'
      throw err
    } finally {
      cloudPlatformLoading.value = false
    }
  }

  const fetchCurrentCloudPlatform = async () => {
    try {
      const result = await cloudPlatformIntegrationApi.getCurrentPlatform()
      currentCloudPlatform.value = result.platform || null
      return result
    } catch (err: any) {
      error.value = err.message || '获取当前云平台失败'
      throw err
    }
  }

  const testCloudPlatformConnection = async (platformId: number) => {
    cloudPlatformLoading.value = true
    try {
      const result = await cloudPlatformIntegrationApi.testConnection(platformId)
      return result
    } catch (err: any) {
      error.value = err.message || '测试云平台连接失败'
      throw err
    } finally {
      cloudPlatformLoading.value = false
    }
  }

  return {
    // 状态
    tasks,
    currentTask,
    taskInstances,
    loading,
    error,
    availableCloudPlatforms,
    currentCloudPlatform,
    cloudPlatformLoading,

    // 计算属性
    activeTasks,
    runningTasks,
    completedTasks,
    failedTasks,
    tasksByType,
    tasksByComponent,

    // 方法
    fetchTasks,
    fetchTaskById,
    createTask,
    updateTask,
    deleteTask,
    executeTask,
    stopTask,
    fetchTaskLogs,
    fetchTaskInstances,
    fetchInstanceStatus,
    stopInstance,
    cancelInstance,
    cleanupInstances,
    clearError,
    resetState,

    // 云平台相关方法
    fetchAvailableCloudPlatforms,
    selectCloudPlatform,
    fetchCurrentCloudPlatform,
    testCloudPlatformConnection
  }
})
