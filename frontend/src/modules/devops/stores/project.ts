/**
 * DevOps项目管理的状态管理
 * 使用Pinia实现状态管理和数据缓存
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { DevOpsProject } from '@/modules/devops/types/devops'
import { projectApi } from '@/modules/devops/services/devopsApi'

export const useProjectStore = defineStore('devops-project', () => {
  // 状态数据
  const projects = ref<DevOpsProject[]>([])
  
  // 加载状态
  const loading = ref(false)
  
  // 错误状态
  const error = ref<string | null>(null)
  
  // 应用关联状态
  const projectApplications = ref<Record<number, any[]>>({})
  const applicationLoading = ref(false)
  const applicationError = ref<string | null>(null)

  // 计算属性
  const isLoading = computed(() => loading.value)
  
  const hasError = computed(() => error.value !== null)
  
  const projectOptions = computed(() => 
    projects.value.map(project => ({
      label: project.name,
      value: project.id
    }))
  )
  
  // 应用相关计算属性
  const isApplicationLoading = computed(() => applicationLoading.value)
  
  const hasApplicationError = computed(() => applicationError.value !== null)
  
  const applicationOptions = computed(() => (projectId: number) => 
    projectApplications.value[projectId]?.map(app => ({
      label: app.name,
      value: app.id
    })) || []
  )

  // 获取所有项目
  const loadProjects = async (params?: any) => {
    loading.value = true
    error.value = null
    try {
      const result = await projectApi.getAll(params)
      // 检查返回结果是否为有效数组
      if (Array.isArray(result)) {
        projects.value = result
      } else {
        projects.value = []
        console.warn('Project API returned invalid data:', result)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载项目失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 根据ID获取项目
  const getProjectById = async (id: number) => {
    try {
      const project = await projectApi.getById(id)
      return project
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取项目失败'
      throw err
    }
  }

  // 创建项目
  const createProject = async (project: Omit<DevOpsProject, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const newProject = await projectApi.create(project)
      projects.value.push(newProject)
      return newProject
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建项目失败'
      throw err
    }
  }

  // 更新项目
  const updateProject = async (id: number, project: Partial<DevOpsProject>) => {
    try {
      const updatedProject = await projectApi.update(id, project)
      const index = projects.value.findIndex(p => p.id === id)
      if (index !== -1) {
        projects.value[index] = updatedProject
      }
      return updatedProject
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新项目失败'
      throw err
    }
  }

  // 删除项目
  const deleteProject = async (id: number) => {
    try {
      await projectApi.delete(id)
      projects.value = projects.value.filter(p => p.id !== id)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除项目失败'
      throw err
    }
  }

  // 搜索项目
  const searchProjects = async (query: string) => {
    try {
      const result = await projectApi.search(query)
      if (Array.isArray(result)) {
        projects.value = result
      } else {
        projects.value = []
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '搜索项目失败'
      throw err
    }
  }

  // 重置状态
  const reset = () => {
    projects.value = []
    loading.value = false
    error.value = null
    projectApplications.value = {}
    applicationLoading.value = false
    applicationError.value = null
  }

  return {
    // 状态
    projects,
    loading,
    error,
    
    // 应用关联状态
    projectApplications,
    applicationLoading,
    applicationError,
    
    // 计算属性
    isLoading,
    hasError,
    projectOptions,
    
    // 应用相关计算属性
    isApplicationLoading,
    hasApplicationError,
    applicationOptions,
    
    // 方法
    loadProjects,
    getProjectById,
    createProject,
    updateProject,
    deleteProject,
    searchProjects,
    reset
  }
})