/**
 * DevOps管理模块的状态管理
 * 使用Pinia实现状态管理和数据缓存
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  DevOpsProject,
  DevOpsApplication,
  DevOpsComponent,
  DevOpsResource,
  DevOpsCiTask,
  DevOpsCdTask
} from '@/modules/devops/types/devops'
import {
  applicationApi,
  componentApi,
  resourceApi,
  statsApi
} from '@/modules/devops/services/devopsApi'

export const useDevOpsStore = defineStore('devops', () => {
  // 状态数据
  const applications = ref<DevOpsApplication[]>([])
  const components = ref<DevOpsComponent[]>([])
  const resources = ref<DevOpsResource[]>([])
  const ciTasks = ref<DevOpsCiTask[]>([])
  const cdTasks = ref<DevOpsCdTask[]>([])

  // 加载状态
  const loading = ref({
    applications: false,
    components: false,
    resources: false,
    ciTasks: false,
    cdTasks: false,
    stats: false
  })

  // 错误状态
  const errors = ref({
    applications: '',
    components: '',
    resources: '',
    ciTasks: '',
    cdTasks: '',
    stats: ''
  })

  // 统计数据
  const stats = ref({
    overview: {
      projects: 0,
      applications: 0,
      components: 0,
      resources: 0
    },
    tasks: {
      ci: { total: 0, running: 0, completed: 0, failed: 0 },
      cd: { total: 0, running: 0, completed: 0, failed: 0 }
    }
  })

  // 计算属性
  const isLoading = computed(() => {
    return Object.values(loading.value).some(l => l)
  })

  const hasErrors = computed(() => {
    return Object.values(errors.value).some(e => e)
  })

  // 应用管理方法
  const loadApplications = async (params?: any) => {
    loading.value.applications = true
    errors.value.applications = ''
    try {
      applications.value = await applicationApi.getAll(params)
    } catch (error) {
      errors.value.applications = error instanceof Error ? error.message : '加载应用失败'
      throw error
    } finally {
      loading.value.applications = false
    }
  }

  const loadApplicationsByProject = async (projectId: number) => {
    loading.value.applications = true
    errors.value.applications = ''
    try {
      const projectApplications = await applicationApi.getByProject(projectId)
      // 更新或添加到应用列表中
      projectApplications.forEach(app => {
        const index = applications.value.findIndex(a => a.id === app.id)
        if (index !== -1) {
          applications.value[index] = app
        } else {
          applications.value.push(app)
        }
      })
      return projectApplications
    } catch (error) {
      errors.value.applications = error instanceof Error ? error.message : '加载项目应用失败'
      throw error
    } finally {
      loading.value.applications = false
    }
  }

  const createApplication = async (application: Omit<DevOpsApplication, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const newApplication = await applicationApi.create(application)
      applications.value.unshift(newApplication)
      return newApplication
    } catch (error) {
      errors.value.applications = error instanceof Error ? error.message : '创建应用失败'
      throw error
    }
  }

  const updateApplication = async (id: number, application: Partial<DevOpsApplication>) => {
    try {
      const updatedApplication = await applicationApi.update(id, application)
      const index = applications.value.findIndex(a => a.id === id)
      if (index !== -1) {
        applications.value[index] = updatedApplication
      }
      return updatedApplication
    } catch (error) {
      errors.value.applications = error instanceof Error ? error.message : '更新应用失败'
      throw error
    }
  }

  const deleteApplication = async (id: number) => {
    try {
      await applicationApi.delete(id)
      const index = applications.value.findIndex(a => a.id === id)
      if (index !== -1) {
        applications.value.splice(index, 1)
      }
    } catch (error) {
      errors.value.applications = error instanceof Error ? error.message : '删除应用失败'
      throw error
    }
  }

  // 组件管理方法
  const loadComponents = async (params?: any) => {
    loading.value.components = true
    errors.value.components = ''
    try {
      components.value = await componentApi.getAll(params)
    } catch (error) {
      errors.value.components = error instanceof Error ? error.message : '加载组件失败'
      throw error
    } finally {
      loading.value.components = false
    }
  }

  const loadComponentsByApplication = async (applicationId: number) => {
    loading.value.components = true
    errors.value.components = ''
    try {
      const applicationComponents = await componentApi.getByApplication(applicationId)
      // 更新或添加到组件列表中
      applicationComponents.forEach(comp => {
        const index = components.value.findIndex(c => c.id === comp.id)
        if (index !== -1) {
          components.value[index] = comp
        } else {
          components.value.push(comp)
        }
      })
      return applicationComponents
    } catch (error) {
      errors.value.components = error instanceof Error ? error.message : '加载应用组件失败'
      throw error
    } finally {
      loading.value.components = false
    }
  }

  const createComponent = async (component: Omit<DevOpsComponent, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const newComponent = await componentApi.create(component)
      components.value.unshift(newComponent)
      return newComponent
    } catch (error) {
      errors.value.components = error instanceof Error ? error.message : '创建组件失败'
      throw error
    }
  }

  const updateComponent = async (id: number, component: Partial<DevOpsComponent>) => {
    try {
      const updatedComponent = await componentApi.update(id, component)
      const index = components.value.findIndex(c => c.id === id)
      if (index !== -1) {
        components.value[index] = updatedComponent
      }
      return updatedComponent
    } catch (error) {
      errors.value.components = error instanceof Error ? error.message : '更新组件失败'
      throw error
    }
  }

  const deleteComponent = async (id: number) => {
    try {
      await componentApi.delete(id)
      const index = components.value.findIndex(c => c.id === id)
      if (index !== -1) {
        components.value.splice(index, 1)
      }
    } catch (error) {
      errors.value.components = error instanceof Error ? error.message : '删除组件失败'
      throw error
    }
  }

  // 资源管理方法
  const loadResources = async (params?: any) => {
    loading.value.resources = true
    errors.value.resources = ''
    try {
      resources.value = await resourceApi.getAll(params)
    } catch (error) {
      errors.value.resources = error instanceof Error ? error.message : '加载资源失败'
      throw error
    } finally {
      loading.value.resources = false
    }
  }

  const loadResourcesByComponent = async (componentId: number) => {
    loading.value.resources = true
    errors.value.resources = ''
    try {
      const componentResources = await resourceApi.getByComponent(componentId)
      // 更新或添加到资源列表中
      componentResources.forEach(res => {
        const index = resources.value.findIndex(r => r.id === res.id)
        if (index !== -1) {
          resources.value[index] = res
        } else {
          resources.value.push(res)
        }
      })
      return componentResources
    } catch (error) {
      errors.value.resources = error instanceof Error ? error.message : '加载组件资源失败'
      throw error
    } finally {
      loading.value.resources = false
    }
  }

  const createResource = async (resource: Omit<DevOpsResource, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const newResource = await resourceApi.create(resource)
      resources.value.unshift(newResource)
      return newResource
    } catch (error) {
      errors.value.resources = error instanceof Error ? error.message : '创建资源失败'
      throw error
    }
  }

  const updateResource = async (id: number, resource: Partial<DevOpsResource>) => {
    try {
      const updatedResource = await resourceApi.update(id, resource)
      const index = resources.value.findIndex(r => r.id === id)
      if (index !== -1) {
        resources.value[index] = updatedResource
      }
      return updatedResource
    } catch (error) {
      errors.value.resources = error instanceof Error ? error.message : '更新资源失败'
      throw error
    }
  }

  const deleteResource = async (id: number) => {
    try {
      await resourceApi.delete(id)
      const index = resources.value.findIndex(r => r.id === id)
      if (index !== -1) {
        resources.value.splice(index, 1)
      }
    } catch (error) {
      errors.value.resources = error instanceof Error ? error.message : '删除资源失败'
      throw error
    }
  }

  // 统计数据方法
  const loadStats = async () => {
    loading.value.stats = true
    errors.value.stats = ''
    try {
      const [overview, tasks] = await Promise.all([
        statsApi.getOverview(),
        statsApi.getTaskStats()
      ])
      stats.value.overview = overview
      stats.value.tasks = tasks
    } catch (error) {
      errors.value.stats = error instanceof Error ? error.message : '加载统计数据失败'
      throw error
    } finally {
      loading.value.stats = false
    }
  }

  // 清除错误
  const clearError = (type?: keyof typeof errors.value) => {
    if (type) {
      errors.value[type] = ''
    } else {
      Object.keys(errors.value).forEach(key => {
        errors.value[key as keyof typeof errors.value] = ''
      })
    }
  }

  // 重置状态
  const reset = () => {
    applications.value = []
    components.value = []
    resources.value = []
    ciTasks.value = []
    cdTasks.value = []
    
    Object.keys(loading.value).forEach(key => {
      loading.value[key as keyof typeof loading.value] = false
    })
    
    clearError()
  }

  return {
    // 状态
    applications,
    components,
    resources,
    ciTasks,
    cdTasks,
    loading,
    errors,
    stats,
    
    // 计算属性
    isLoading,
    hasErrors,
    
    // 应用方法
    loadApplications,
    loadApplicationsByProject,
    createApplication,
    updateApplication,
    deleteApplication,
    
    // 组件方法
    loadComponents,
    loadComponentsByApplication,
    createComponent,
    updateComponent,
    deleteComponent,
    
    // 资源方法
    loadResources,
    loadResourcesByComponent,
    createResource,
    updateResource,
    deleteResource,
    
    // 统计方法
    loadStats,
    
    // 工具方法
    clearError,
    reset
  }
})
