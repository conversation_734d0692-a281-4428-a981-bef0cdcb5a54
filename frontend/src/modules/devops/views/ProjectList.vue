<template>
  <div class="project-list">
    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1>项目管理</h1>
          <p>管理和组织您的项目，创建项目层级结构</p>
        </div>
        <div class="header-actions">
          <button
            @click="showCreateModal = true"
            class="btn btn-primary"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            创建项目
          </button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <DataTable
      :data="projectStore.projects"
      :columns="tableColumns"
      :loading="projectStore.loading"
      title="项目列表"
      searchable
      search-placeholder="搜索项目名称或描述..."
      @search="handleSearch"
      @row-click="handleRowClick"
    >
      <template #toolbar-actions>
        <button
          @click="refreshData"
          :disabled="projectStore.loading"
          class="btn btn-secondary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          刷新
        </button>
      </template>

      <template #cell-status="{ value }">
        <StatusTag :status="value" />
      </template>

      <template #cell-applicationNames="{ record }">
        <div class="application-tags" v-if="record.applications && record.applications.length > 0">
          <span 
            v-for="application in record.applications" 
            :key="application.id"
            class="application-tag"
          >
            {{ application.name }}
          </span>
        </div>
        <span v-else class="no-application">
          无关联应用
        </span>
      </template>

      <template #cell-createdAt="{ value }">
        {{ formatDate(value) }}
      </template>

      <template #actions="{ record }">
        <div class="action-buttons">
          <button
            @click.stop="viewProject(record)"
            class="action-btn action-btn-view"
            title="查看详情"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
              <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            @click.stop="editProject(record)"
            class="action-btn action-btn-edit"
            title="编辑项目"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          </button>
          <button
            @click.stop="deleteProject(record)"
            class="action-btn action-btn-delete"
            title="删除项目"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </template>
    </DataTable>

    <!-- 创建/编辑项目模态框 -->
    <div v-if="showCreateModal || showEditModal" class="modal-overlay" @click="closeModal">
      <div class="modal-container" @click.stop>
        <div class="modal-header">
          <h3>{{ showCreateModal ? '创建项目' : '编辑项目' }}</h3>
          <button @click="closeModal" class="modal-close">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="submitForm" class="modal-form">
          <FormField
            v-model="formData.name"
            label="项目名称"
            type="text"
            placeholder="请输入项目名称"
            required
            :error-message="formErrors.name"
          />

          <FormField
            v-model="formData.description"
            label="项目描述"
            type="textarea"
            placeholder="请输入项目描述"
            :rows="3"
            :error-message="formErrors.description"
          />

          <FormField
            v-model="formData.status"
            label="项目状态"
            type="select"
            :options="statusOptions"
            :error-message="formErrors.status"
          />

          <!-- 添加应用选择器 -->
          <div class="application-selection">
            <label class="form-label">关联应用</label>
            <SplitPanel 
              :initial-left-width="200"
              :min-left-width="150"
              :min-right-width="150"
              :reset-left-width="200"
            >
              <template #left>
                <div class="application-list-container">
                  <div class="application-list-header">
                    <h4>应用列表</h4>
                    <input 
                      v-model="applicationSearch"
                      type="text" 
                      placeholder="搜索应用..." 
                      class="application-search"
                    />
                  </div>
                  <div class="application-list">
                    <div
                      v-for="app in filteredApplications"
                      :key="app.id"
                      :class="['application-item', { selected: isApplicationSelected(app.id!) }]"
                      @click="toggleApplicationSelection(app.id!)"
                    >
                      <span class="application-name">{{ app.name }}</span>
                      <span v-if="isApplicationSelected(app.id!)" class="checkmark">✓</span>
                    </div>
                  </div>
                </div>
              </template>
              
              <template #right>
                <div class="selected-applications-container">
                  <div class="selected-applications-header">
                    <h4>已选应用</h4>
                    <span class="selected-count">({{ formData.applicationIds.length }})</span>
                  </div>
                  <div class="selected-applications">
                    <div
                      v-for="appId in formData.applicationIds"
                      :key="appId"
                      class="selected-application"
                    >
                      <span class="application-name">{{ getApplicationName(appId) }}</span>
                      <button 
                        @click="removeApplication(appId)"
                        class="remove-button"
                        type="button"
                      >
                        ×
                      </button>
                    </div>
                    <div v-if="formData.applicationIds.length === 0" class="no-applications">
                      未选择任何应用
                    </div>
                  </div>
                </div>
              </template>
            </SplitPanel>
          </div>

          <div class="modal-actions">
            <button type="button" @click="closeModal" class="btn btn-secondary">
              取消
            </button>
            <button type="submit" :disabled="submitting" class="btn btn-primary">
              <svg v-if="submitting" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 3a7 7 0 100 14 7 7 0 000-14zM2 10a8 8 0 1116 0 8 8 0 01-16 0z" opacity="0.3"/>
                <path d="M10 2a8 8 0 018 8" fill="currentColor">
                  <animateTransform attributeName="transform" type="rotate" from="0 10 10" to="360 10 10" dur="1s" repeatCount="indefinite"/>
                </path>
              </svg>
              {{ submitting ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 确认删除对话框 -->
    <ConfirmDialog
      :visible="showDeleteDialog"
      title="确认删除项目"
      :message="`确定要删除项目 &quot;${deleteTarget?.name}&quot; 吗？此操作不可撤销。`"
      type="danger"
      confirm-text="删除"
      :loading="deleting"
      @confirm="confirmDelete"
      @cancel="showDeleteDialog = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import DataTable from '@/components/DataTable.vue'
import StatusTag from '@/components/StatusTag.vue'
import Breadcrumb from '@/components/Breadcrumb.vue'
import FormField from '@/components/FormField.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import SplitPanel from '@/components/SplitPanel.vue'
import type { DevOpsProject, BreadcrumbItem, TableColumn } from '@/modules/devops/types/devops'
import { useProjectStore } from '@/modules/devops/stores/project'
import { useDevOpsStore } from '@/modules/devops/stores/devops'
import { message } from '@/utils/message'

const router = useRouter()
const projectStore = useProjectStore()
const devOpsStore = useDevOpsStore()

// 响应式数据
const submitting = ref(false)
const deleting = ref(false)

// 模态框状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteDialog = ref(false)
const deleteTarget = ref<DevOpsProject | null>(null)
const applicationSearch = ref('')

// 表单数据
const formData = ref({
  name: '',
  description: '',
  status: 'ACTIVE',
  applicationIds: [] as number[]  // 添加关联的应用ID数组
})

const formErrors = ref({
  name: '',
  description: '',
  status: '',
  applicationIds: ''
})

const editingProject = ref<DevOpsProject | null>(null)

// 计算属性
const breadcrumbItems = computed<BreadcrumbItem[]>(() => [
  { title: 'DevOps 管理', path: '/devops', icon: 'home' },
  { title: '项目管理', active: true, icon: 'project' }
])

const tableColumns: TableColumn[] = [
  { key: 'name', title: '项目名称', sortable: true },
  { key: 'description', title: '描述' },
  { key: 'applicationNames', title: '关联应用', width: '200px' }, // 添加关联应用列
  { key: 'status', title: '状态', width: '120px' },
  { key: 'createdAt', title: '创建时间', width: '180px', sortable: true }
]

const statusOptions = [
  { label: '活跃', value: 'ACTIVE' },
  { label: '非活跃', value: 'INACTIVE' },
  { label: '已归档', value: 'ARCHIVED' }
]

// 应用选项计算属性
const applicationOptions = computed(() => {
  return devOpsStore.applications.map(app => ({
    label: app.name,
    value: app.id
  }))
})

const filteredApplications = computed(() => {
  if (!applicationSearch.value) {
    return devOpsStore.applications
  }
  
  const search = applicationSearch.value.toLowerCase()
  return devOpsStore.applications.filter(app => 
    app.name.toLowerCase().includes(search)
  )
})

const isApplicationSelected = (appId: number) => {
  return formData.value.applicationIds.includes(appId)
}

const toggleApplicationSelection = (appId: number) => {
  const index = formData.value.applicationIds.indexOf(appId)
  if (index >= 0) {
    // Remove if already selected
    formData.value.applicationIds.splice(index, 1)
  } else {
    // Add if not selected
    formData.value.applicationIds.push(appId)
  }
}

const removeApplication = (appId: number) => {
  const index = formData.value.applicationIds.indexOf(appId)
  if (index >= 0) {
    formData.value.applicationIds.splice(index, 1)
  }
}

const getApplicationName = (appId: number) => {
  const app = devOpsStore.applications.find(a => a.id === appId)
  return app ? app.name : ''
}

// 方法
const refreshData = () => {
  loadProjects()
  loadApplications()
}

const loadProjects = async () => {
  try {
    await projectStore.loadProjects()
  } catch (error) {
    console.error('加载项目数据失败:', error)
    message.error('加载项目数据失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

const loadApplications = async () => {
  try {
    // 使用applicationStore获取所有应用
    await devOpsStore.loadApplications()
  } catch (error) {
    console.error('加载应用数据失败:', error)
    message.error('加载应用数据失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

const handleSearch = (query: string) => {
  // 实际项目中这里会调用API进行搜索
  projectStore.searchProjects(query)
}

const handleRowClick = (project: DevOpsProject) => {
  viewProject(project)
}

const viewProject = (project: DevOpsProject) => {
  // 暂时导航到编辑项目，因为目前没有项目详情页面
  editProject(project)
}

const editProject = (project: DevOpsProject) => {
  editingProject.value = project
  formData.value = {
    name: project.name,
    description: project.description || '',
    status: project.status || 'ACTIVE',
    applicationIds: project.applicationIds || []
  }
  showEditModal.value = true
}

const deleteProject = (project: DevOpsProject) => {
  deleteTarget.value = project
  showDeleteDialog.value = true
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  resetForm()
}

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    status: 'ACTIVE',
    applicationIds: []
  }
  formErrors.value = {
    name: '',
    description: '',
    status: '',
    applicationIds: ''
  }
  editingProject.value = null
}

const validateForm = () => {
  const errors = { name: '', description: '', status: '', applicationIds: '' }
  let isValid = true

  if (!formData.value.name.trim()) {
    errors.name = '项目名称不能为空'
    isValid = false
  }

  formErrors.value = errors
  return isValid
}

const submitForm = async () => {
  if (!validateForm()) return

  submitting.value = true
  try {
    const projectData = {
      ...formData.value
    }

    if (showCreateModal.value) {
      // 创建项目
      await projectStore.createProject(projectData)
    } else if (showEditModal.value && editingProject.value) {
      // 更新项目
      await projectStore.updateProject(editingProject.value.id!, projectData)
    }
    
    // 刷新数据以确保应用关联正确显示
    await refreshData()
    closeModal()
  } catch (error) {
    console.error('保存项目失败:', error)
    message.error('保存项目失败: ' + (error instanceof Error ? error.message : '未知错误'))
  } finally {
    submitting.value = false
  }
}

const confirmDelete = async () => {
  if (!deleteTarget.value) return

  deleting.value = true
  try {
    await projectStore.deleteProject(deleteTarget.value.id!)
    showDeleteDialog.value = false
    deleteTarget.value = null
  } catch (error) {
    console.error('删除项目失败:', error)
    message.error('删除项目失败: ' + (error instanceof Error ? error.message : '未知错误'))
  } finally {
    deleting.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadProjects()
  loadApplications()
})
</script>

<style scoped>
.project-list {
  @apply p-6 bg-gray-50 min-h-screen;
}

/* 页面头部 */
.page-header {
  @apply mb-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-title h1 {
  @apply text-2xl font-bold text-gray-900 m-0;
}

.header-title p {
  @apply text-gray-600 mt-1 m-0;
}

.header-actions {
  @apply flex items-center gap-3;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500;
}

.btn-icon {
  @apply w-4 h-4;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 操作按钮 */
.action-buttons {
  @apply flex items-center gap-1;
}

.action-btn {
  @apply p-2 rounded-md transition-colors;
}

.action-btn svg {
  @apply w-4 h-4;
}

.action-btn-view {
  @apply text-blue-600 hover:bg-blue-50;
}

.action-btn-edit {
  @apply text-green-600 hover:bg-green-50;
}

.action-btn-delete {
  @apply text-red-600 hover:bg-red-50;
}

/* 模态框样式 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl w-full max-w-md;
}

.modal-header {
  @apply flex items-center justify-between p-6 pb-4;
}

.modal-header h3 {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.modal-close {
  @apply text-gray-400 hover:text-gray-600 p-1 rounded;
}

.modal-close svg {
  @apply w-5 h-5;
}

.modal-form {
  @apply px-6 pb-6;
}

.modal-actions {
  @apply flex items-center justify-end gap-3 mt-6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .project-list {
    @apply p-4;
  }

  .header-content {
    @apply flex-col items-start gap-4;
  }

  .modal-container {
    @apply max-w-full mx-4;
  }

  .action-buttons {
    @apply flex-col gap-2;
  }
}

/* 动画效果 */
.modal-overlay {
  animation: fadeIn 0.2s ease-out;
}

.modal-container {
  animation: slideIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.application-tag {
  @apply inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-1 mb-1;
}

.no-application {
  @apply text-gray-400 text-sm;
}

.application-tags {
  @apply flex flex-wrap;
}

/* Application Selection Styles */
.application-selection {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.application-list-container,
.selected-applications-container {
  @apply h-full flex flex-col;
}

.application-list-header,
.selected-applications-header {
  @apply flex items-center justify-between p-3 border-b border-gray-200;
}

.application-list-header h4,
.selected-applications-header h4 {
  @apply text-sm font-medium text-gray-900 m-0;
}

.selected-count {
  @apply text-xs text-gray-500;
}

.application-search {
  @apply block w-full px-3 py-1 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.application-list {
  @apply flex-1 overflow-y-auto;
}

.application-item {
  @apply flex items-center justify-between p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50;
}

.application-item.selected {
  @apply bg-blue-50 hover:bg-blue-100;
}

.application-name {
  @apply text-sm text-gray-900;
}

.checkmark {
  @apply text-blue-600 font-bold;
}

.selected-applications {
  @apply flex-1 overflow-y-auto p-3;
}

.selected-application {
  @apply flex items-center justify-between p-2 mb-2 bg-gray-50 rounded;
}

.selected-application .application-name {
  @apply text-sm;
}

.remove-button {
  @apply w-6 h-6 flex items-center justify-center rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-200;
}

.no-applications {
  @apply text-gray-400 text-sm text-center py-8;
}

/* Responsive design */
@media (max-width: 768px) {
  .application-selection {
    height: 300px;
  }
}

@media (min-width: 769px) {
  .application-selection {
    height: 300px;
  }
}
</style>
