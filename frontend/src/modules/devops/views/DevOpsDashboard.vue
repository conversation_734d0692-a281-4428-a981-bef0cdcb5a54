<template>
  <div class="devops-dashboard">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="header-title">
          <h1>DevOps 管理平台</h1>
          <p>统一的项目、应用、组件和资源管理平台</p>
        </div>
        <div class="header-actions">
          <button @click="refreshStats" :disabled="loading" class="refresh-btn">
            <svg class="refresh-icon" :class="{ spinning: loading }" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
            </svg>
            刷新
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon project-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.projects || 0 }}</div>
          <div class="stat-label">项目</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon application-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.applications || 0 }}</div>
          <div class="stat-label">应用</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon component-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9 12a1 1 0 002 0V6.414l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 6.414V12zM3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.components || 0 }}</div>
          <div class="stat-label">组件</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon resource-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M3 7v10a2 2 0 002 2h14l-2-2H5V7H3zM14 2H8a2 2 0 00-2 2v8a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2zM8 4h6v8H8V4z" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.resources || 0 }}</div>
          <div class="stat-label">资源</div>
        </div>
      </div>
    </div>

    <!-- 主要功能模块 -->
    <div class="modules-grid">
      <!-- 项目管理 -->
      <div class="module-card" @click="navigateTo('/devops/projects')">
        <div class="module-header">
          <div class="module-icon project-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
            </svg>
          </div>
          <h3>项目管理</h3>
        </div>
        <p>管理和组织您的项目，创建项目层级结构，配置项目设置和权限。</p>
        <div class="module-actions">
          <span class="action-link">管理项目 →</span>
        </div>
      </div>

      <!-- 应用管理 -->
      <div class="module-card" @click="navigateTo('/devops/applications')">
        <div class="module-header">
          <div class="module-icon application-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
            </svg>
          </div>
          <h3>应用管理</h3>
        </div>
        <p>管理项目中的应用程序，配置应用环境，监控应用状态和性能。</p>
        <div class="module-actions">
          <span class="action-link">管理应用 →</span>
        </div>
      </div>

      <!-- 组件管理 -->
      <div class="module-card" @click="navigateTo('/devops/components')">
        <div class="module-header">
          <div class="module-icon component-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 12a1 1 0 002 0V6.414l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 6.414V12zM3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <h3>组件管理</h3>
        </div>
        <p>管理应用的各个组件，配置代码仓库，设置构建和部署流程。</p>
        <div class="module-actions">
          <span class="action-link">管理组件 →</span>
        </div>
      </div>

      <!-- 资源管理 -->
      <div class="module-card" @click="navigateTo('/devops/resources')">
        <div class="module-header">
          <div class="module-icon resource-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M3 7v10a2 2 0 002 2h14l-2-2H5V7H3zM14 2H8a2 2 0 00-2 2v8a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2zM8 4h6v8H8V4z" />
            </svg>
          </div>
          <h3>资源管理</h3>
        </div>
        <p>管理组件相关的资源，包括数据库、缓存、存储等基础设施资源。</p>
        <div class="module-actions">
          <span class="action-link">管理资源 →</span>
        </div>
      </div>

      <!-- CI管理 -->
      <div class="module-card" @click="navigateTo('/devops/ci')">
        <div class="module-header">
          <div class="module-icon ci-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm6 5a1 1 0 10-2 0v6a1 1 0 102 0V7z" />
            </svg>
          </div>
          <h3>CI 管理</h3>
        </div>
        <p>配置和管理持续集成流水线，自动化构建、测试和代码质量检查流程。</p>
        <div class="module-actions">
          <span class="action-link">管理 CI 任务 →</span>
        </div>
      </div>

      <!-- CD管理 -->
      <div class="module-card" @click="navigateTo('/devops/cd')">
        <div class="module-header">
          <div class="module-icon cd-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm1 5a1 1 0 000 2h4.586l-2.293 2.293a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 10-1.414 1.414L9.586 7H5z" />
            </svg>
          </div>
          <h3>CD 管理</h3>
        </div>
        <p>配置和管理持续部署流水线，自动化应用部署、回滚和环境管理。</p>
        <div class="module-actions">
          <span class="action-link">管理 CD 任务 →</span>
        </div>
      </div>

      <!-- 监控中心 -->
      <div class="module-card" @click="navigateTo('/devops/monitoring')">
        <div class="module-header">
          <div class="module-icon monitoring-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3>监控中心</h3>
        </div>
        <p>实时监控系统状态，查看任务执行历史，分析性能指标和日志。</p>
        <div class="module-actions">
          <span class="action-link">查看监控 →</span>
        </div>
      </div>

      <!-- 云平台管理 -->
      <div class="module-card" @click="navigateTo('/cloud-platforms/dashboard')">
        <div class="module-header">
          <div class="module-icon cloudplatform-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z" />
            </svg>
          </div>
          <h3>云平台管理</h3>
        </div>
        <p>管理和配置多云平台连接，支持Kubernetes、Docker、云服务商等平台。</p>
        <div class="module-actions">
          <span class="action-link">管理云平台 →</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const stats = ref({
  projects: 0,
  applications: 0,
  components: 0,
  resources: 0
})

// 方法
const navigateTo = (path: string) => {
  router.push(path)
}

const refreshStats = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据更新
    stats.value = {
      projects: Math.floor(Math.random() * 50) + 10,
      applications: Math.floor(Math.random() * 100) + 20,
      components: Math.floor(Math.random() * 200) + 50,
      resources: Math.floor(Math.random() * 300) + 100
    }
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  refreshStats()
})
</script>

<style scoped>
.devops-dashboard {
  @apply min-h-screen bg-gray-50 p-6;
}

/* 页面头部 */
.dashboard-header {
  @apply mb-8;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-title h1 {
  @apply text-3xl font-bold text-gray-900 m-0;
}

.header-title p {
  @apply text-gray-600 mt-1 m-0;
}

.header-actions {
  @apply flex items-center gap-3;
}

.refresh-btn {
  @apply flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50;
}

.refresh-icon {
  @apply w-4 h-4;
}

.refresh-icon.spinning {
  @apply animate-spin;
}

/* 统计卡片 */
.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8;
}

.stat-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 flex items-center gap-4;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center;
}

.stat-icon svg {
  @apply w-6 h-6;
}

.stat-icon.project-icon {
  @apply bg-blue-100 text-blue-600;
}

.stat-icon.application-icon {
  @apply bg-green-100 text-green-600;
}

.stat-icon.component-icon {
  @apply bg-purple-100 text-purple-600;
}

.stat-icon.resource-icon {
  @apply bg-orange-100 text-orange-600;
}

.stat-content {
  @apply flex-1;
}

.stat-number {
  @apply text-2xl font-bold text-gray-900;
}

.stat-label {
  @apply text-sm text-gray-600 mt-1;
}

/* 模块网格 */
.modules-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8;
}

.module-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer transition-all hover:shadow-md hover:border-gray-300;
}

.module-header {
  @apply flex items-center gap-3 mb-3;
}

.module-icon {
  @apply w-10 h-10 rounded-lg flex items-center justify-center;
}

.module-icon svg {
  @apply w-5 h-5;
}

.module-icon.ci-icon {
  @apply bg-blue-100 text-blue-600;
}

.module-icon.cd-icon {
  @apply bg-indigo-100 text-indigo-600;
}

.module-icon.monitoring-icon {
  @apply bg-emerald-100 text-emerald-600;
}

.module-icon.cloudplatform-icon {
  @apply bg-sky-100 text-sky-600;
}

.module-header h3 {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.module-card p {
  @apply text-gray-600 text-sm leading-relaxed mb-4 m-0;
}

.module-actions {
  @apply flex items-center justify-between;
}

.action-link {
  @apply text-blue-600 text-sm font-medium hover:text-blue-700 transition-colors;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .devops-dashboard {
    @apply p-4;
  }

  .header-content {
    @apply flex-col items-start gap-4;
  }

  .stats-grid {
    @apply grid-cols-2 gap-4;
  }

  .modules-grid {
    @apply grid-cols-1 gap-4;
  }

  .stat-card {
    @apply p-4;
  }

  .module-card {
    @apply p-4;
  }
}

/* 动画效果 */
.module-card:hover .action-link {
  @apply transform translate-x-1;
}

.stat-card:hover {
  @apply transform -translate-y-1;
}

/* 加载状态 */
.refresh-btn:disabled {
  @apply cursor-not-allowed;
}
</style>
