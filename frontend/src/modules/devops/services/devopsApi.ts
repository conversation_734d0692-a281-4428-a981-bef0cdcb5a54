/**
 * DevOps管理模块的API服务层
 * 封装所有与DevOps相关的API调用
 */

import type {
  DevOpsProject,
  DevOpsApplication,
  DevOpsComponent,
  DevOpsResource,
  DevOpsCiTask,
  DevOpsCiTaskInstance,
  DevOpsCdTask,
  DevOpsCdTaskInstance,
  // 以下导入暂未使用，但保留注释以便将来使用
  // DevOpsDeploymentTemplate,
  // DevOpsTektonCluster,
  // ApiResponse,
  // PagedResponse,
  QueryParams
} from '@/modules/devops/types/devops'
import apiClient from '@/utils/axios'

// API基础配置
const API_BASE_URL = '/api/devops'

// 项目管理API
export const projectApi = {
  // 获取所有项目
  async getAll(params?: QueryParams): Promise<DevOpsProject[]> {
    const response = await apiClient.get<DevOpsProject[]>(`${API_BASE_URL}/projects`, { params })
    return response.data
  },

  // 根据ID获取项目
  async getById(id: number): Promise<DevOpsProject> {
    const response = await apiClient.get<DevOpsProject>(`${API_BASE_URL}/projects/${id}`)
    return response.data
  },

  // 创建项目
  async create(project: Omit<DevOpsProject, 'id' | 'createdAt' | 'updatedAt'>): Promise<DevOpsProject> {
    const response = await apiClient.post<DevOpsProject>(`${API_BASE_URL}/projects`, project)
    return response.data
  },

  // 更新项目
  async update(id: number, project: Partial<DevOpsProject>): Promise<DevOpsProject> {
    const response = await apiClient.put<DevOpsProject>(`${API_BASE_URL}/projects/${id}`, project)
    return response.data
  },

  // 删除项目
  async delete(id: number): Promise<void> {
    await apiClient.delete<void>(`${API_BASE_URL}/projects/${id}`)
  },

  // 搜索项目
  async search(query: string): Promise<DevOpsProject[]> {
    const response = await apiClient.get<DevOpsProject[]>(`${API_BASE_URL}/projects/search`, {
      params: { keyword: query }
    })
    return response.data
  },

  // 获取项目统计
  async getStats(): Promise<{ total: number; active: number; inactive: number }> {
    const response = await apiClient.get<{ total: number; active: number; inactive: number }>(`${API_BASE_URL}/projects/stats`)
    return response.data
  }
}

// 应用管理API
export const applicationApi = {
  // 获取所有应用
  async getAll(params?: QueryParams): Promise<DevOpsApplication[]> {
    const response = await apiClient.get<DevOpsApplication[]>(`${API_BASE_URL}/applications`, { params })
    return response.data
  },

  // 根据项目ID获取应用
  async getByProject(projectId: number): Promise<DevOpsApplication[]> {
    const response = await apiClient.get<DevOpsApplication[]>(`${API_BASE_URL}/projects/${projectId}/applications`)
    return response.data
  },

  // 根据ID获取应用
  async getById(id: number): Promise<DevOpsApplication> {
    const response = await apiClient.get<DevOpsApplication>(`${API_BASE_URL}/applications/${id}`)
    return response.data
  },

  // 创建应用
  async create(application: Omit<DevOpsApplication, 'id' | 'createdAt' | 'updatedAt'>): Promise<DevOpsApplication> {
    const response = await apiClient.post<DevOpsApplication>(`${API_BASE_URL}/applications`, application)
    return response.data
  },

  // 更新应用
  async update(id: number, application: Partial<DevOpsApplication>): Promise<DevOpsApplication> {
    const response = await apiClient.put<DevOpsApplication>(`${API_BASE_URL}/applications/${id}`, application)
    return response.data
  },

  // 删除应用
  async delete(id: number): Promise<void> {
    await apiClient.delete<void>(`${API_BASE_URL}/applications/${id}`)
  },

  // 搜索应用
  async search(query: string): Promise<DevOpsApplication[]> {
    const response = await apiClient.get<DevOpsApplication[]>(`${API_BASE_URL}/applications/search`, {
      params: { q: query }
    })
    return response.data
  }
}

// 组件管理API
export const componentApi = {
  // 获取所有组件
  async getAll(params?: QueryParams): Promise<DevOpsComponent[]> {
    const response = await apiClient.get<DevOpsComponent[]>(`${API_BASE_URL}/components`, { params })
    return response.data
  },

  // 根据应用ID获取组件
  async getByApplication(applicationId: number): Promise<DevOpsComponent[]> {
    const response = await apiClient.get<DevOpsComponent[]>(`${API_BASE_URL}/applications/${applicationId}/components`)
    return response.data
  },

  // 根据ID获取组件
  async getById(id: number): Promise<DevOpsComponent> {
    const response = await apiClient.get<DevOpsComponent>(`${API_BASE_URL}/components/${id}`)
    return response.data
  },

  // 创建组件
  async create(component: Omit<DevOpsComponent, 'id' | 'createdAt' | 'updatedAt'>): Promise<DevOpsComponent> {
    const response = await apiClient.post<DevOpsComponent>(`${API_BASE_URL}/components`, component)
    return response.data
  },

  // 更新组件
  async update(id: number, component: Partial<DevOpsComponent>): Promise<DevOpsComponent> {
    const response = await apiClient.put<DevOpsComponent>(`${API_BASE_URL}/components/${id}`, component)
    return response.data
  },

  // 删除组件
  async delete(id: number): Promise<void> {
    await apiClient.delete<void>(`${API_BASE_URL}/components/${id}`)
  },

  // 搜索组件
  async search(query: string): Promise<DevOpsComponent[]> {
    const response = await apiClient.get<DevOpsComponent[]>(`${API_BASE_URL}/components/search`, {
      params: { q: query }
    })
    return response.data
  }
}

// 资源管理API
export const resourceApi = {
  // 获取所有资源
  async getAll(params?: QueryParams): Promise<DevOpsResource[]> {
    const response = await apiClient.get<DevOpsResource[]>(`${API_BASE_URL}/resources`, { params })
    return response.data
  },

  // 根据组件ID获取资源
  async getByComponent(componentId: number): Promise<DevOpsResource[]> {
    const response = await apiClient.get<DevOpsResource[]>(`${API_BASE_URL}/components/${componentId}/resources`)
    return response.data
  },

  // 根据ID获取资源
  async getById(id: number): Promise<DevOpsResource> {
    const response = await apiClient.get<DevOpsResource>(`${API_BASE_URL}/resources/${id}`)
    return response.data
  },

  // 创建资源
  async create(resource: Omit<DevOpsResource, 'id' | 'createdAt' | 'updatedAt'>): Promise<DevOpsResource> {
    const response = await apiClient.post<DevOpsResource>(`${API_BASE_URL}/resources`, resource)
    return response.data
  },

  // 更新资源
  async update(id: number, resource: Partial<DevOpsResource>): Promise<DevOpsResource> {
    const response = await apiClient.put<DevOpsResource>(`${API_BASE_URL}/resources/${id}`, resource)
    return response.data
  },

  // 删除资源
  async delete(id: number): Promise<void> {
    await apiClient.delete<void>(`${API_BASE_URL}/resources/${id}`)
  },

  // 搜索资源
  async search(query: string): Promise<DevOpsResource[]> {
    const response = await apiClient.get<DevOpsResource[]>(`${API_BASE_URL}/resources/search`, {
      params: { q: query }
    })
    return response.data
  },

  // 获取资源类型
  async getTypes(): Promise<string[]> {
    const response = await apiClient.get<string[]>(`${API_BASE_URL}/resources/types`)
    return response.data
  }
}

// CI任务管理API
export const ciTaskApi = {
  // 获取所有CI任务
  async getAll(params?: QueryParams): Promise<DevOpsCiTask[]> {
    const response = await apiClient.get<DevOpsCiTask[]>(`${API_BASE_URL}/ci/tasks`, { params })
    return response.data
  },

  // 根据应用ID获取CI任务
  async getByApplication(applicationId: number): Promise<DevOpsCiTask[]> {
    const response = await apiClient.get<DevOpsCiTask[]>(`${API_BASE_URL}/applications/${applicationId}/ci/tasks`)
    return response.data
  },

  // 根据ID获取CI任务
  async getById(id: number): Promise<DevOpsCiTask> {
    const response = await apiClient.get<DevOpsCiTask>(`${API_BASE_URL}/ci/tasks/${id}`)
    return response.data
  },

  // 创建CI任务
  async create(task: Omit<DevOpsCiTask, 'id' | 'createdAt' | 'updatedAt'>): Promise<DevOpsCiTask> {
    const response = await apiClient.post<DevOpsCiTask>(`${API_BASE_URL}/ci/tasks`, task)
    return response.data
  },

  // 更新CI任务
  async update(id: number, task: Partial<DevOpsCiTask>): Promise<DevOpsCiTask> {
    const response = await apiClient.put<DevOpsCiTask>(`${API_BASE_URL}/ci/tasks/${id}`, task)
    return response.data
  },

  // 删除CI任务
  async delete(id: number): Promise<void> {
    await apiClient.delete<void>(`${API_BASE_URL}/ci/tasks/${id}`)
  },

  // 执行CI任务
  async execute(id: number): Promise<DevOpsCiTaskInstance> {
    const response = await apiClient.post<DevOpsCiTaskInstance>(`${API_BASE_URL}/ci/tasks/${id}/execute`)
    return response.data
  },

  // 获取CI任务日志
  async getLogs(id: number): Promise<string> {
    const response = await apiClient.get<string>(`${API_BASE_URL}/ci/tasks/${id}/logs`)
    return response.data
  },

  // 获取CI任务状态
  async getStatus(id: number): Promise<{ status: string; progress: number }> {
    const response = await apiClient.get<{ status: string; progress: number }>(`${API_BASE_URL}/ci/tasks/${id}/status`)
    return response.data
  },
  
  // 启动CI任务
  async start(id: number, params?: any): Promise<DevOpsCiTaskInstance> {
    const response = await apiClient.post<DevOpsCiTaskInstance>(`${API_BASE_URL}/ci/tasks/${id}/start`, params)
    return response.data
  },
  
  // 停止CI任务
  async stop(id: number): Promise<void> {
    await apiClient.post<void>(`${API_BASE_URL}/ci/tasks/${id}/stop`)
  },
  
  // 获取CI任务实例列表
  async getInstances(taskId: number): Promise<DevOpsCiTaskInstance[]> {
    const response = await apiClient.get<DevOpsCiTaskInstance[]>(`${API_BASE_URL}/ci/tasks/${taskId}/instances`)
    return response.data
  },
  
  // 获取CI任务实例状态
  async getInstanceStatus(instanceId: string): Promise<DevOpsCiTaskInstance> {
    const response = await apiClient.get<DevOpsCiTaskInstance>(`${API_BASE_URL}/ci/tasks/instances/${instanceId}`)
    return response.data
  },
  
  // 获取CI任务实例日志
  async getInstanceLogs(instanceId: string): Promise<string> {
    const response = await apiClient.get<string>(`${API_BASE_URL}/ci/tasks/instances/${instanceId}/logs`)
    return response.data
  },
  
  // 停止CI任务实例
  async stopInstance(instanceId: string): Promise<void> {
    await apiClient.post<void>(`${API_BASE_URL}/ci/tasks/instances/${instanceId}/stop`)
  },
  
  // 取消CI任务实例
  async cancelInstance(instanceId: string): Promise<void> {
    await apiClient.post<void>(`${API_BASE_URL}/ci/tasks/instances/${instanceId}/cancel`)
  },
  
  // 清理已完成的CI任务实例
  async cleanupInstances(taskId: number, keepCount: number): Promise<number> {
    const response = await apiClient.post<number>(`${API_BASE_URL}/ci/tasks/${taskId}/cleanup`, { keepCount })
    return response.data
  }
}

// CD任务管理API
export const cdTaskApi = {
  // 获取所有CD任务
  async getAll(params?: QueryParams): Promise<DevOpsCdTask[]> {
    const response = await apiClient.get<DevOpsCdTask[]>(`${API_BASE_URL}/cd/tasks`, { params })
    return response.data
  },

  // 根据应用ID获取CD任务
  async getByApplication(applicationId: number): Promise<DevOpsCdTask[]> {
    const response = await apiClient.get<DevOpsCdTask[]>(`${API_BASE_URL}/applications/${applicationId}/cd/tasks`)
    return response.data
  },

  // 根据ID获取CD任务
  async getById(id: number): Promise<DevOpsCdTask> {
    const response = await apiClient.get<DevOpsCdTask>(`${API_BASE_URL}/cd/tasks/${id}`)
    return response.data
  },

  // 创建CD任务
  async create(task: Omit<DevOpsCdTask, 'id' | 'createdAt' | 'updatedAt'>): Promise<DevOpsCdTask> {
    const response = await apiClient.post<DevOpsCdTask>(`${API_BASE_URL}/cd/tasks`, task)
    return response.data
  },

  // 更新CD任务
  async update(id: number, task: Partial<DevOpsCdTask>): Promise<DevOpsCdTask> {
    const response = await apiClient.put<DevOpsCdTask>(`${API_BASE_URL}/cd/tasks/${id}`, task)
    return response.data
  },

  // 删除CD任务
  async delete(id: number): Promise<void> {
    await apiClient.delete<void>(`${API_BASE_URL}/cd/tasks/${id}`)
  },

  // 执行CD任务
  async execute(id: number): Promise<DevOpsCdTask> {
    const response = await apiClient.post<DevOpsCdTask>(`${API_BASE_URL}/cd/tasks/${id}/execute`)
    return response.data
  },

  // 获取CD任务日志
  async getLogs(id: number): Promise<string> {
    const response = await apiClient.get<string>(`${API_BASE_URL}/cd/tasks/${id}/logs`)
    return response.data
  },

  // 获取CD任务状态
  async getStatus(id: number): Promise<{ status: string; progress: number }> {
    const response = await apiClient.get<{ status: string; progress: number }>(`${API_BASE_URL}/cd/tasks/${id}/status`)
    return response.data
  },
  
  // 获取CD任务实例列表
  async getInstances(taskId: number): Promise<DevOpsCdTaskInstance[]> {
    const response = await apiClient.get<DevOpsCdTaskInstance[]>(`${API_BASE_URL}/cd/tasks/${taskId}/instances`)
    return response.data
  },
  
  // 获取CD任务实例状态
  async getInstanceStatus(instanceId: string): Promise<DevOpsCdTaskInstance> {
    const response = await apiClient.get<DevOpsCdTaskInstance>(`${API_BASE_URL}/cd/tasks/instances/${instanceId}`)
    return response.data
  },
  
  // 获取CD任务实例日志
  async getInstanceLogs(instanceId: string): Promise<string> {
    const response = await apiClient.get<string>(`${API_BASE_URL}/cd/tasks/instances/${instanceId}/logs`)
    return response.data
  },
  
  // 停止CD任务实例
  async stopInstance(instanceId: string): Promise<void> {
    await apiClient.post<void>(`${API_BASE_URL}/cd/tasks/instances/${instanceId}/stop`)
  },
  
  // 取消CD任务实例
  async cancelInstance(instanceId: string): Promise<void> {
    await apiClient.post<void>(`${API_BASE_URL}/cd/tasks/instances/${instanceId}/cancel`)
  },
  
  // 清理已完成的CD任务实例
  async cleanupInstances(taskId: number, keepCount: number): Promise<number> {
    const response = await apiClient.post<number>(`${API_BASE_URL}/cd/tasks/${taskId}/cleanup`, { keepCount })
    return response.data
  },
  
  // 启动CD任务
  async start(id: number, params?: any): Promise<Record<string, any>> {
    const response = await apiClient.post<Record<string, any>>(`${API_BASE_URL}/cd/tasks/${id}/start`, params)
    return response.data
  },
  
  // 回滚CD任务
  async rollback(id: number, targetVersion?: string): Promise<Record<string, any>> {
    const params = targetVersion ? { targetVersion } : {}
    const response = await apiClient.post<Record<string, any>>(`${API_BASE_URL}/cd/tasks/${id}/rollback`, params)
    return response.data
  },
  
  // 停止CD任务
  async stop(id: number): Promise<void> {
    await apiClient.post<void>(`${API_BASE_URL}/cd/tasks/${id}/stop`)
  }
}

// 云平台集成API
export const cloudPlatformIntegrationApi = {
  // 获取可用的云平台列表
  async getAvailablePlatforms(): Promise<any[]> {
    const response = await apiClient.get<any[]>(`${API_BASE_URL}/cloud-platforms`)
    return response.data
  },

  // 选择云平台
  async selectPlatform(platformId: number): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.post<{ success: boolean; message: string }>(`${API_BASE_URL}/cloud-platforms/${platformId}/select`)
    return response.data
  },

  // 获取当前云平台
  async getCurrentPlatform(): Promise<{ currentCloudPlatformId?: string; platform?: any; message?: string }> {
    const response = await apiClient.get<{ currentCloudPlatformId?: string; platform?: any; message?: string }>(`${API_BASE_URL}/cloud-platforms/current`)
    return response.data
  },

  // 测试云平台连接
  async testConnection(platformId: number): Promise<{ connected: boolean; platform: string; kubernetesVersion: string; capabilities: any }> {
    const response = await apiClient.post<{ connected: boolean; platform: string; kubernetesVersion: string; capabilities: any }>(`${API_BASE_URL}/cloud-platforms/${platformId}/test`)
    return response.data
  },

  // 获取执行器信息
  async getExecutorInfo(): Promise<any> {
    const response = await apiClient.get<any>(`${API_BASE_URL}/cloud-platforms/executor/info`)
    return response.data
  }
}

// 统计API
export const statsApi = {
  // 获取总体统计
  async getOverview(): Promise<{
    projects: number
    applications: number
    components: number
    resources: number
  }> {
    const response = await apiClient.get<{
      projects: number
      applications: number
      components: number
      resources: number
    }>(`${API_BASE_URL}/stats/overview`)
    return response.data
  },

  // 获取任务状态统计
  async getTaskStats(): Promise<{
    ci: { total: number; running: number; completed: number; failed: number }
    cd: { total: number; running: number; completed: number; failed: number }
  }> {
    const response = await apiClient.get<{
      ci: { total: number; running: number; completed: number; failed: number }
      cd: { total: number; running: number; completed: number; failed: number }
    }>(`${API_BASE_URL}/stats/tasks`)
    return response.data
  }
}
