/**
 * Query状态管理Store
 * 使用Pinia管理查询计划和查询结果相关的状态
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  QueryPlan, 
  QueryRequest, 
  QueryResponse, 
  QueryableObject,
  PageResponse,
  TargetTypeInfo 
} from '../types/portal'
import { QueryApi } from '../api/queryApi'

/**
 * Query Store
 */
export const useQueryStore = defineStore('query', () => {
  // 查询计划状态
  const queryPlans = ref<QueryPlan[]>([])
  const currentQueryPlan = ref<QueryPlan | null>(null)
  const queryPlansLoading = ref(false)
  const queryPlansError = ref<string | null>(null)
  
  // 查询结果状态
  const queryResults = ref<QueryableObject[]>([])
  const queryResultsLoading = ref(false)
  const queryResultsError = ref<string | null>(null)
  const lastQueryResponse = ref<QueryResponse | null>(null)
  
  // 目标类型状态
  const targetTypes = ref<TargetTypeInfo[]>([])
  const targetTypesLoading = ref(false)
  const targetTypesError = ref<string | null>(null)
  
  // 分页状态（查询计划）
  const currentPage = ref(0)
  const pageSize = ref(10)
  const totalCount = ref(0)
  const totalPages = ref(0)
  const hasNext = ref(false)
  const hasPrevious = ref(false)
  
  // 搜索状态
  const searchKeyword = ref('')

  // 计算属性
  const isEmpty = computed(() => queryPlans.value.length === 0)
  const hasQueryResults = computed(() => queryResults.value.length > 0)
  const isFirstPage = computed(() => currentPage.value === 0)
  const isLastPage = computed(() => !hasNext.value)

  // Actions - 查询计划管理
  
  /**
   * 加载查询计划列表
   */
  const loadQueryPlans = async (portalId: number, page = 0, size = 10, search?: string) => {
    queryPlansLoading.value = true
    queryPlansError.value = null
    
    try {
      const response: PageResponse<QueryPlan> = await QueryApi.getQueryPlans(portalId, page, size, search)
      
      queryPlans.value = response.content
      currentPage.value = response.page
      pageSize.value = response.size
      totalCount.value = response.total
      totalPages.value = response.totalPages
      hasNext.value = response.hasNext
      hasPrevious.value = response.hasPrevious
      
      if (search !== undefined) {
        searchKeyword.value = search
      }
    } catch (err: any) {
      queryPlansError.value = err.message || '加载查询计划列表失败'
      console.error('加载查询计划列表失败:', err)
    } finally {
      queryPlansLoading.value = false
    }
  }

  /**
   * 创建查询计划
   */
  const createQueryPlan = async (
    portalId: number, 
    queryPlanData: Omit<QueryPlan, 'id' | 'portalId' | 'userId' | 'createdAt' | 'updatedAt'>
  ) => {
    queryPlansLoading.value = true
    queryPlansError.value = null
    
    try {
      const newQueryPlan = await QueryApi.createQueryPlan(portalId, queryPlanData)
      
      // 如果当前在第一页，直接添加到列表开头
      if (currentPage.value === 0) {
        queryPlans.value.unshift(newQueryPlan)
        
        // 如果超过页面大小，移除最后一个
        if (queryPlans.value.length > pageSize.value) {
          queryPlans.value.pop()
        }
      }
      
      // 更新总数
      totalCount.value += 1
      totalPages.value = Math.ceil(totalCount.value / pageSize.value)
      
      return newQueryPlan
    } catch (err: any) {
      queryPlansError.value = err.message || '创建查询计划失败'
      console.error('创建查询计划失败:', err)
      throw err
    } finally {
      queryPlansLoading.value = false
    }
  }

  /**
   * 更新查询计划
   */
  const updateQueryPlan = async (id: number, queryPlanData: Partial<QueryPlan>) => {
    queryPlansLoading.value = true
    queryPlansError.value = null
    
    try {
      const updatedQueryPlan = await QueryApi.updateQueryPlan(id, queryPlanData)
      
      // 更新列表中的查询计划
      const index = queryPlans.value.findIndex(qp => qp.id === id)
      if (index !== -1) {
        queryPlans.value[index] = updatedQueryPlan
      }
      
      // 更新当前查询计划
      if (currentQueryPlan.value?.id === id) {
        currentQueryPlan.value = updatedQueryPlan
      }
      
      return updatedQueryPlan
    } catch (err: any) {
      queryPlansError.value = err.message || '更新查询计划失败'
      console.error('更新查询计划失败:', err)
      throw err
    } finally {
      queryPlansLoading.value = false
    }
  }

  /**
   * 删除查询计划
   */
  const deleteQueryPlan = async (id: number) => {
    queryPlansLoading.value = true
    queryPlansError.value = null
    
    try {
      await QueryApi.deleteQueryPlan(id)
      
      // 从列表中移除查询计划
      queryPlans.value = queryPlans.value.filter(qp => qp.id !== id)
      
      // 如果删除的是当前查询计划，清空当前查询计划
      if (currentQueryPlan.value?.id === id) {
        currentQueryPlan.value = null
      }
      
      // 更新总数
      totalCount.value -= 1
      totalPages.value = Math.ceil(totalCount.value / pageSize.value)
    } catch (err: any) {
      queryPlansError.value = err.message || '删除查询计划失败'
      console.error('删除查询计划失败:', err)
      throw err
    } finally {
      queryPlansLoading.value = false
    }
  }

  /**
   * 获取查询计划详情
   */
  const getQueryPlanById = async (id: number) => {
    queryPlansLoading.value = true
    queryPlansError.value = null
    
    try {
      const queryPlan = await QueryApi.getQueryPlanById(id)
      currentQueryPlan.value = queryPlan
      return queryPlan
    } catch (err: any) {
      queryPlansError.value = err.message || '获取查询计划详情失败'
      console.error('获取查询计划详情失败:', err)
      throw err
    } finally {
      queryPlansLoading.value = false
    }
  }

  // Actions - 查询执行
  
  /**
   * 执行查询计划
   */
  const executeQueryPlan = async (id: number) => {
    queryResultsLoading.value = true
    queryResultsError.value = null
    
    try {
      const response = await QueryApi.executeQueryPlan(id)
      queryResults.value = response.items
      lastQueryResponse.value = response
      return response
    } catch (err: any) {
      queryResultsError.value = err.message || '执行查询计划失败'
      console.error('执行查询计划失败:', err)
      throw err
    } finally {
      queryResultsLoading.value = false
    }
  }

  /**
   * 执行通用查询
   */
  const executeQuery = async (request: QueryRequest) => {
    queryResultsLoading.value = true
    queryResultsError.value = null
    
    try {
      const response = await QueryApi.executeQuery(request)
      queryResults.value = response.items
      lastQueryResponse.value = response
      return response
    } catch (err: any) {
      queryResultsError.value = err.message || '执行查询失败'
      console.error('执行查询失败:', err)
      throw err
    } finally {
      queryResultsLoading.value = false
    }
  }

  // Actions - 目标类型管理
  
  /**
   * 加载支持的目标类型
   */
  const loadTargetTypes = async () => {
    targetTypesLoading.value = true
    targetTypesError.value = null
    
    try {
      const types = await QueryApi.getSupportedTargetTypes()
      targetTypes.value = types
    } catch (err: any) {
      targetTypesError.value = err.message || '加载目标类型失败'
      console.error('加载目标类型失败:', err)
    } finally {
      targetTypesLoading.value = false
    }
  }

  // Actions - 状态管理
  
  /**
   * 设置当前查询计划
   */
  const setCurrentQueryPlan = (queryPlan: QueryPlan | null) => {
    currentQueryPlan.value = queryPlan
  }

  /**
   * 清空查询结果
   */
  const clearQueryResults = () => {
    queryResults.value = []
    lastQueryResponse.value = null
    queryResultsError.value = null
  }

  /**
   * 搜索查询计划
   */
  const searchQueryPlans = async (portalId: number, keyword: string) => {
    await loadQueryPlans(portalId, 0, pageSize.value, keyword)
  }

  /**
   * 清空搜索
   */
  const clearSearch = async (portalId: number) => {
    searchKeyword.value = ''
    await loadQueryPlans(portalId, 0, pageSize.value)
  }

  /**
   * 刷新当前页
   */
  const refresh = async (portalId: number) => {
    await loadQueryPlans(portalId, currentPage.value, pageSize.value, searchKeyword.value)
  }

  /**
   * 重置状态
   */
  const reset = () => {
    queryPlans.value = []
    currentQueryPlan.value = null
    queryPlansLoading.value = false
    queryPlansError.value = null
    
    queryResults.value = []
    queryResultsLoading.value = false
    queryResultsError.value = null
    lastQueryResponse.value = null
    
    targetTypes.value = []
    targetTypesLoading.value = false
    targetTypesError.value = null
    
    currentPage.value = 0
    pageSize.value = 10
    totalCount.value = 0
    totalPages.value = 0
    hasNext.value = false
    hasPrevious.value = false
    searchKeyword.value = ''
  }

  return {
    // 状态
    queryPlans,
    currentQueryPlan,
    queryPlansLoading,
    queryPlansError,
    queryResults,
    queryResultsLoading,
    queryResultsError,
    lastQueryResponse,
    targetTypes,
    targetTypesLoading,
    targetTypesError,
    currentPage,
    pageSize,
    totalCount,
    totalPages,
    hasNext,
    hasPrevious,
    searchKeyword,
    
    // 计算属性
    isEmpty,
    hasQueryResults,
    isFirstPage,
    isLastPage,
    
    // Actions
    loadQueryPlans,
    createQueryPlan,
    updateQueryPlan,
    deleteQueryPlan,
    getQueryPlanById,
    executeQueryPlan,
    executeQuery,
    loadTargetTypes,
    setCurrentQueryPlan,
    clearQueryResults,
    searchQueryPlans,
    clearSearch,
    refresh,
    reset
  }
})
