/**
 * Portal状态管理Store
 * 使用Pinia管理门户相关的状态
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Portal, PageResponse } from '../types/portal'
import { PortalApi } from '../api/portalApi'

/**
 * Portal Store
 */
export const usePortalStore = defineStore('portal', () => {
  // 状态
  const portals = ref<Portal[]>([])
  const currentPortal = ref<Portal | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // 分页状态
  const currentPage = ref(0)
  const pageSize = ref(10)
  const totalCount = ref(0)
  const totalPages = ref(0)
  const hasNext = ref(false)
  const hasPrevious = ref(false)

  // 搜索状态
  const searchKeyword = ref('')

  // 计算属性
  const isEmpty = computed(() => portals.value.length === 0)
  const isFirstPage = computed(() => currentPage.value === 0)
  const isLastPage = computed(() => !hasNext.value)

  // Actions
  
  /**
   * 加载门户列表
   */
  const loadPortals = async (page = 0, size = 10, search?: string) => {
    loading.value = true
    error.value = null
    
    try {
      const response: PageResponse<Portal> = await PortalApi.getPortals(page, size, search)
      
      portals.value = response.content || []
      currentPage.value = response.page
      pageSize.value = response.size
      totalCount.value = response.total
      totalPages.value = response.totalPages
      hasNext.value = response.hasNext
      hasPrevious.value = response.hasPrevious
      
      if (search !== undefined) {
        searchKeyword.value = search
      }
    } catch (err: any) {
      error.value = err.message || '加载门户列表失败'
      console.error('加载门户列表失败:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建门户
   */
  const createPortal = async (portalData: Omit<Portal, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null
    
    try {
      const newPortal = await PortalApi.createPortal(portalData)
      
      // 如果当前在第一页，直接添加到列表开头
      if (currentPage.value === 0) {
        portals.value.unshift(newPortal)
        
        // 如果超过页面大小，移除最后一个
        if (portals.value.length > pageSize.value) {
          portals.value.pop()
        }
      }
      
      // 更新总数
      totalCount.value += 1
      totalPages.value = Math.ceil(totalCount.value / pageSize.value)
      
      return newPortal
    } catch (err: any) {
      error.value = err.message || '创建门户失败'
      console.error('创建门户失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新门户
   */
  const updatePortal = async (id: number, portalData: Partial<Portal>) => {
    loading.value = true
    error.value = null
    
    try {
      const updatedPortal = await PortalApi.updatePortal(id, portalData)
      
      // 更新列表中的门户
      const index = portals.value.findIndex(p => p.id === id)
      if (index !== -1) {
        portals.value[index] = updatedPortal
      }
      
      // 更新当前门户
      if (currentPortal.value?.id === id) {
        currentPortal.value = updatedPortal
      }
      
      return updatedPortal
    } catch (err: any) {
      error.value = err.message || '更新门户失败'
      console.error('更新门户失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除门户
   */
  const deletePortal = async (id: number) => {
    loading.value = true
    error.value = null
    
    try {
      await PortalApi.deletePortal(id)
      
      // 从列表中移除门户
      portals.value = portals.value.filter(p => p.id !== id)
      
      // 如果删除的是当前门户，清空当前门户
      if (currentPortal.value?.id === id) {
        currentPortal.value = null
      }
      
      // 更新总数
      totalCount.value -= 1
      totalPages.value = Math.ceil(totalCount.value / pageSize.value)
      
      // 如果当前页没有数据且不是第一页，回到上一页
      if (portals.value.length === 0 && currentPage.value > 0) {
        await loadPortals(currentPage.value - 1, pageSize.value, searchKeyword.value)
      }
    } catch (err: any) {
      error.value = err.message || '删除门户失败'
      console.error('删除门户失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取门户详情
   */
  const getPortalById = async (id: number) => {
    loading.value = true
    error.value = null

    try {
      const portal = await PortalApi.getPortalById(id)
      currentPortal.value = portal
      return portal
    } catch (err: any) {
      error.value = err.message || '获取门户详情失败'
      console.error('获取门户详情失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取所有门户（不分页）
   */
  const getAllPortals = async () => {
    loading.value = true
    error.value = null

    try {
      const allPortals = await PortalApi.getAllPortals()
      return allPortals
    } catch (err: any) {
      error.value = err.message || '获取所有门户失败'
      console.error('获取所有门户失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 设置当前门户
   */
  const setCurrentPortal = (portal: Portal | null) => {
    currentPortal.value = portal
  }

  /**
   * 搜索门户
   */
  const searchPortals = async (keyword: string) => {
    await loadPortals(0, pageSize.value, keyword)
  }

  /**
   * 清空搜索
   */
  const clearSearch = async () => {
    searchKeyword.value = ''
    await loadPortals(0, pageSize.value)
  }

  /**
   * 刷新当前页
   */
  const refresh = async () => {
    await loadPortals(currentPage.value, pageSize.value, searchKeyword.value)
  }

  /**
   * 重置状态
   */
  const reset = () => {
    portals.value = []
    currentPortal.value = null
    loading.value = false
    error.value = null
    currentPage.value = 0
    pageSize.value = 10
    totalCount.value = 0
    totalPages.value = 0
    hasNext.value = false
    hasPrevious.value = false
    searchKeyword.value = ''
  }

  return {
    // 状态
    portals,
    currentPortal,
    loading,
    error,
    currentPage,
    pageSize,
    totalCount,
    totalPages,
    hasNext,
    hasPrevious,
    searchKeyword,
    
    // 计算属性
    isEmpty,
    isFirstPage,
    isLastPage,
    
    // Actions
    loadPortals,
    createPortal,
    updatePortal,
    deletePortal,
    getPortalById,
    getAllPortals,
    setCurrentPortal,
    searchPortals,
    clearSearch,
    refresh,
    reset
  }
})
