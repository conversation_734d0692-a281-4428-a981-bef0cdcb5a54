/**
 * Portal Store 单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { usePortalStore } from '../portalStore'
import { PortalApi } from '../../api/portalApi'
import type { Portal, PageResponse } from '../../types/portal'

// Mock PortalApi
vi.mock('../../api/portalApi', () => ({
  PortalApi: {
    createPortal: vi.fn(),
    getPortals: vi.fn(),
    getPortalById: vi.fn(),
    updatePortal: vi.fn(),
    deletePortal: vi.fn(),
    getAllPortals: vi.fn(),
    getPortalCount: vi.fn()
  }
}))

describe('Portal Store', () => {
  let portalStore: ReturnType<typeof usePortalStore>

  const mockPortal: Portal = {
    id: 1,
    name: '测试门户',
    description: '测试门户描述',
    color: '#1890ff',
    icon: 'folder',
    status: 'active',
    userId: 1,
    queryPlanCount: 5,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }

  const mockPageResponse: PageResponse<Portal> = {
    content: [mockPortal],
    page: 0,
    size: 10,
    total: 1,
    totalPages: 1,
    hasNext: false,
    hasPrevious: false
  }

  beforeEach(() => {
    setActivePinia(createPinia())
    portalStore = usePortalStore()
    vi.clearAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(portalStore.portals).toEqual([])
      expect(portalStore.currentPortal).toBeNull()
      expect(portalStore.loading).toBe(false)
      expect(portalStore.error).toBeNull()
      expect(portalStore.currentPage).toBe(0)
      expect(portalStore.pageSize).toBe(10)
      expect(portalStore.totalCount).toBe(0)
      expect(portalStore.searchKeyword).toBe('')
    })
  })

  describe('loadPortals', () => {
    it('应该成功加载门户列表', async () => {
      // Given
      vi.mocked(PortalApi.getPortals).mockResolvedValue(mockPageResponse)

      // When
      await portalStore.loadPortals()

      // Then
      expect(portalStore.loading).toBe(false)
      expect(portalStore.portals).toEqual([mockPortal])
      expect(portalStore.totalCount).toBe(1)
      expect(portalStore.error).toBeNull()
      expect(PortalApi.getPortals).toHaveBeenCalledWith(0, 10, undefined)
    })

    it('应该处理加载失败的情况', async () => {
      // Given
      const errorMessage = '网络错误'
      vi.mocked(PortalApi.getPortals).mockRejectedValue(new Error(errorMessage))

      // When
      await portalStore.loadPortals()

      // Then
      expect(portalStore.loading).toBe(false)
      expect(portalStore.portals).toEqual([])
      expect(portalStore.error).toBe(errorMessage)
    })

    it('应该在加载过程中设置loading状态', async () => {
      // Given
      let resolvePromise: (value: any) => void
      const promise = new Promise(resolve => {
        resolvePromise = resolve
      })
      vi.mocked(PortalApi.getPortals).mockReturnValue(promise)

      // When
      const loadPromise = portalStore.loadPortals()
      
      // Then
      expect(portalStore.loading).toBe(true)
      
      // 完成加载
      resolvePromise!(mockPageResponse)
      await loadPromise
      
      expect(portalStore.loading).toBe(false)
    })
  })

  describe('createPortal', () => {
    it('应该成功创建门户', async () => {
      // Given
      // 先加载初始数据，确保在第一页
      await portalStore.loadPortals()

      const newPortalData = {
        name: '新门户',
        description: '新门户描述',
        color: '#52c41a',
        icon: 'appstore'
      }
      const createdPortal = { ...mockPortal, ...newPortalData, id: 2 }

      vi.mocked(PortalApi.createPortal).mockResolvedValue(createdPortal)

      // When
      const result = await portalStore.createPortal(newPortalData)

      // Then
      expect(result).toEqual(createdPortal)
      expect(PortalApi.createPortal).toHaveBeenCalledWith(newPortalData)
      expect(portalStore.portals.some(p => p.id === createdPortal.id)).toBe(true)
    })

    it('应该处理创建失败的情况', async () => {
      // Given
      const newPortalData = {
        name: '新门户',
        description: '新门户描述',
        color: '#52c41a',
        icon: 'appstore'
      }
      const errorMessage = '门户名称已存在'
      vi.mocked(PortalApi.createPortal).mockRejectedValue(new Error(errorMessage))

      // When & Then
      await expect(portalStore.createPortal(newPortalData)).rejects.toThrow(errorMessage)
      expect(portalStore.error).toBe(errorMessage)
    })
  })

  describe('updatePortal', () => {
    it('应该成功更新门户', async () => {
      // Given
      portalStore.portals = [mockPortal]
      const updateData = {
        name: '更新后的门户',
        description: '更新后的描述',
        color: '#faad14'
      }
      const updatedPortal = { ...mockPortal, ...updateData }
      
      vi.mocked(PortalApi.updatePortal).mockResolvedValue(updatedPortal)

      // When
      const result = await portalStore.updatePortal(1, updateData)

      // Then
      expect(result).toEqual(updatedPortal)
      expect(PortalApi.updatePortal).toHaveBeenCalledWith(1, updateData)
      expect(portalStore.portals[0]).toEqual(updatedPortal)
    })

    it('应该处理更新不存在的门户', async () => {
      // Given
      const updateData = { name: '更新后的门户' }
      const errorMessage = '门户不存在'
      vi.mocked(PortalApi.updatePortal).mockRejectedValue(new Error(errorMessage))

      // When & Then
      await expect(portalStore.updatePortal(999, updateData)).rejects.toThrow(errorMessage)
      expect(portalStore.error).toBe(errorMessage)
    })
  })

  describe('deletePortal', () => {
    it('应该成功删除门户', async () => {
      // Given
      portalStore.portals = [mockPortal]
      vi.mocked(PortalApi.deletePortal).mockResolvedValue(undefined)

      // When
      await portalStore.deletePortal(1)

      // Then
      expect(PortalApi.deletePortal).toHaveBeenCalledWith(1)
      expect(portalStore.portals).toEqual([])
    })

    it('应该处理删除失败的情况', async () => {
      // Given
      portalStore.portals = [mockPortal]
      const errorMessage = '删除失败'
      vi.mocked(PortalApi.deletePortal).mockRejectedValue(new Error(errorMessage))

      // When & Then
      await expect(portalStore.deletePortal(1)).rejects.toThrow(errorMessage)
      expect(portalStore.error).toBe(errorMessage)
      expect(portalStore.portals).toEqual([mockPortal]) // 门户仍然存在
    })
  })

  describe('getPortalById', () => {
    it('应该成功获取门户详情', async () => {
      // Given
      vi.mocked(PortalApi.getPortalById).mockResolvedValue(mockPortal)

      // When
      const result = await portalStore.getPortalById(1)

      // Then
      expect(result).toEqual(mockPortal)
      expect(PortalApi.getPortalById).toHaveBeenCalledWith(1)
      expect(portalStore.currentPortal).toEqual(mockPortal)
    })

    it('应该处理获取失败的情况', async () => {
      // Given
      const errorMessage = '门户不存在'
      vi.mocked(PortalApi.getPortalById).mockRejectedValue(new Error(errorMessage))

      // When & Then
      await expect(portalStore.getPortalById(999)).rejects.toThrow(errorMessage)
      expect(portalStore.error).toBe(errorMessage)
      expect(portalStore.currentPortal).toBeNull()
    })
  })

  describe('searchPortals', () => {
    it('应该成功搜索门户', async () => {
      // Given
      const keyword = '测试'
      const searchResults = {
        ...mockPageResponse,
        content: [mockPortal]
      }
      vi.mocked(PortalApi.getPortals).mockResolvedValue(searchResults)

      // When
      await portalStore.searchPortals(keyword)

      // Then
      expect(portalStore.searchKeyword).toBe(keyword)
      expect(portalStore.portals).toEqual([mockPortal])
      expect(PortalApi.getPortals).toHaveBeenCalledWith(0, 10, keyword)
    })

    it('应该处理空搜索关键词', async () => {
      // Given
      vi.mocked(PortalApi.getPortals).mockResolvedValue(mockPageResponse)

      // When
      await portalStore.searchPortals('')

      // Then
      expect(portalStore.searchKeyword).toBe('')
      expect(PortalApi.getPortals).toHaveBeenCalledWith(0, 10, '')
    })
  })

  describe('clearSearch', () => {
    it('应该清除搜索状态并重新加载', async () => {
      // Given
      portalStore.searchKeyword = '测试'
      vi.mocked(PortalApi.getPortals).mockResolvedValue(mockPageResponse)

      // When
      await portalStore.clearSearch()

      // Then
      expect(portalStore.searchKeyword).toBe('')
      expect(PortalApi.getPortals).toHaveBeenCalledWith(0, 10, undefined)
    })
  })

  describe('setCurrentPortal', () => {
    it('应该设置当前门户', () => {
      // When
      portalStore.setCurrentPortal(mockPortal)

      // Then
      expect(portalStore.currentPortal).toEqual(mockPortal)
    })

    it('应该清除当前门户', () => {
      // Given
      portalStore.currentPortal = mockPortal

      // When
      portalStore.setCurrentPortal(null)

      // Then
      expect(portalStore.currentPortal).toBeNull()
    })
  })

  describe('reset', () => {
    it('应该重置所有状态', () => {
      // Given
      portalStore.portals = [mockPortal]
      portalStore.currentPortal = mockPortal
      portalStore.loading = true
      portalStore.error = '错误信息'
      portalStore.searchKeyword = '测试'
      portalStore.currentPage = 2
      portalStore.totalCount = 100

      // When
      portalStore.reset()

      // Then
      expect(portalStore.portals).toEqual([])
      expect(portalStore.currentPortal).toBeNull()
      expect(portalStore.loading).toBe(false)
      expect(portalStore.error).toBeNull()
      expect(portalStore.searchKeyword).toBe('')
      expect(portalStore.currentPage).toBe(0)
      expect(portalStore.totalCount).toBe(0)
    })
  })

  describe('getAllPortals', () => {
    it('应该获取所有门户列表', async () => {
      // Given
      const allPortals = [mockPortal]
      vi.mocked(PortalApi.getAllPortals).mockResolvedValue(allPortals)

      // When
      const result = await portalStore.getAllPortals()

      // Then
      expect(result).toEqual(allPortals)
      expect(PortalApi.getAllPortals).toHaveBeenCalled()
    })
  })
})
