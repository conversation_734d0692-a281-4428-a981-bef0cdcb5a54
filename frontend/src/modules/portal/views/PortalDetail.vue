<template>
  <div class="portal-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <Button type="text" @click="goBack">
          <template #icon>
            <Icon name="arrow-left" />
          </template>
          返回
        </Button>

        <div class="portal-info" v-if="currentPortal">
          <div class="portal-icon" :style="{ backgroundColor: currentPortal.color }">
            <Icon :name="currentPortal.icon" :size="24" />
          </div>
          <div class="portal-meta">
            <h1 class="portal-name">{{ currentPortal.name }}</h1>
            <p class="portal-description">{{ currentPortal.description || '暂无描述' }}</p>
          </div>
        </div>
      </div>

      <div class="header-right">
        <Button @click="showEditModal">
          <template #icon>
            <Icon name="edit" />
          </template>
          编辑门户
        </Button>
        <Button type="primary" @click="showCreateQueryPlanModal">
          <template #icon>
            <Icon name="plus" />
          </template>
          新建查询计划
        </Button>
      </div>
    </div>

    <!-- 查询计划列表 -->
    <div class="query-plans-section">
      <div class="section-header">
        <h2>查询计划</h2>
        <div class="section-actions">
          <Input
            v-model="searchValue"
            placeholder="搜索查询计划"
            search
            style="width: 300px"
            @search="handleSearch"
            @pressEnter="handleSearch"
          />
        </div>
      </div>

      <!-- 查询计划网格 -->
      <div class="query-plans-grid" v-if="!isEmpty">
        <div
          v-for="queryPlan in queryPlans"
          :key="queryPlan.id"
          class="query-plan-card"
          @click="handleQueryPlanClick(queryPlan)"
        >
          <div class="card-header">
            <div class="query-plan-icon" :style="{ backgroundColor: queryPlan.color }">
              <Icon name="search" />
            </div>
            <div class="card-actions">
              <div class="dropdown-wrapper">
                <Button
                  type="text"
                  size="small"
                  @click.stop="toggleQueryPlanDropdown(queryPlan.id)"
                >
                  <Icon name="more" />
                </Button>

                <div
                  v-if="activeQueryPlanDropdown === queryPlan.id"
                  class="dropdown-menu"
                  @click.stop
                >
                  <div class="menu-item" @click="handleExecuteQueryPlan(queryPlan)">
                    <Icon name="play-circle" />
                    执行查询
                  </div>
                  <div class="menu-item" @click="handleEditQueryPlan(queryPlan)">
                    <Icon name="edit" />
                    编辑
                  </div>
                  <div class="menu-item danger" @click="handleDeleteQueryPlan(queryPlan)">
                    <Icon name="delete" />
                    删除
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="card-content">
            <h3 class="query-plan-name">{{ queryPlan.name }}</h3>
            <p class="query-plan-description">{{ queryPlan.description || '暂无描述' }}</p>
            
            <div class="query-plan-meta">
              <Tag :color="getTargetTypeColor(queryPlan.targetType)">
                {{ getTargetTypeLabel(queryPlan.targetType) }}
              </Tag>

              <div class="query-plan-stats">
                <span class="stat-item">
                  <Icon name="calendar" />
                  {{ formatDate(queryPlan.updatedAt) }}
                </span>
                <span class="stat-item" v-if="queryPlan.lastExecuted">
                  <Icon name="clock" />
                  上次执行: {{ formatDate(queryPlan.lastExecuted) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <Empty description="暂无查询计划" image="simple">
          <Button type="primary" @click="showCreateQueryPlanModal">
            创建第一个查询计划
          </Button>
        </Empty>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="!isEmpty">
        <div class="pagination">
          <div class="pagination-info">
            第 {{ (currentPageNum - 1) * pageSizeNum + 1 }}-{{ Math.min(currentPageNum * pageSizeNum, totalCount) }} 条，共 {{ totalCount }} 条
          </div>
          <div class="pagination-controls">
            <Button
              :disabled="currentPageNum <= 1"
              @click="handlePageChange(currentPageNum - 1)"
            >
              上一页
            </Button>
            <span class="page-info">{{ currentPageNum }} / {{ Math.ceil(totalCount / pageSizeNum) }}</span>
            <Button
              :disabled="currentPageNum >= Math.ceil(totalCount / pageSizeNum)"
              @click="handlePageChange(currentPageNum + 1)"
            >
              下一页
            </Button>
          </div>
        </div>
          :total="totalCount"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
          @change="handlePageChange"
          @showSizeChange="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 编辑门户模态框 -->
    <PortalFormModal
      v-model:visible="editPortalModalVisible"
      :portal="currentPortal"
      @success="handlePortalEditSuccess"
    />

    <!-- 创建/编辑查询计划模态框 -->
    <!-- <QueryPlanFormModal
      v-model:visible="queryPlanModalVisible"
      :portal-id="portalId"
      :query-plan="editingQueryPlan"
      @success="handleQueryPlanFormSuccess"
    /> -->

    <!-- 查询结果模态框 -->
    <!-- <QueryResultModal
      v-model:visible="queryResultModalVisible"
      :query-plan="executingQueryPlan"
      :results="queryResults"
      :loading="queryResultsLoading"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Button from '../../../components/ui/Button.vue'
import Input from '../../../components/ui/Input.vue'
import Icon from '../../../components/ui/Icon.vue'
import Tag from '../../../components/ui/Tag.vue'
import Empty from '../../../components/ui/Empty.vue'
import { message } from '../../../utils/message'
import { Modal } from '../../../utils/modal'
import { usePortalStore } from '../stores/portalStore'
import { useQueryStore } from '../stores/queryStore'
import type { QueryPlan } from '../types/portal'
import PortalFormModal from '../components/PortalFormModal.vue'

// 组合式API
const route = useRoute()
const router = useRouter()
const portalStore = usePortalStore()
const queryStore = useQueryStore()

// 响应式数据
const searchValue = ref('')
const editPortalModalVisible = ref(false)
const queryPlanModalVisible = ref(false)
const queryResultModalVisible = ref(false)
const editingQueryPlan = ref<QueryPlan | null>(null)
const executingQueryPlan = ref<QueryPlan | null>(null)
const activeQueryPlanDropdown = ref<number | null>(null)

// 计算属性
const portalId = computed(() => Number(route.params.id))
const currentPortal = computed(() => portalStore.currentPortal)
const queryPlans = computed(() => queryStore.queryPlans)
const queryResults = computed(() => queryStore.queryResults)
const queryResultsLoading = computed(() => queryStore.queryResultsLoading)
const isEmpty = computed(() => queryStore.isEmpty)
const totalCount = computed(() => queryStore.totalCount)
const currentPageNum = computed({
  get: () => queryStore.currentPage + 1,
  set: (value) => queryStore.currentPage = value - 1
})
const pageSizeNum = computed({
  get: () => queryStore.pageSize,
  set: (value) => queryStore.pageSize = value
})

// 方法
const loadData = async () => {
  try {
    // 加载门户详情
    await portalStore.getPortalById(portalId.value)
    // 加载查询计划列表
    await queryStore.loadQueryPlans(portalId.value)
  } catch (error) {
    message.error('加载数据失败')
    router.push('/portal')
  }
}

const goBack = () => {
  router.push('/portal')
}

const showEditModal = () => {
  editPortalModalVisible.value = true
}

const showCreateQueryPlanModal = () => {
  editingQueryPlan.value = null
  queryPlanModalVisible.value = true
}

const handleSearch = async () => {
  if (searchValue.value.trim()) {
    await queryStore.searchQueryPlans(portalId.value, searchValue.value.trim())
  } else {
    await queryStore.clearSearch(portalId.value)
  }
}

const toggleQueryPlanDropdown = (queryPlanId: number) => {
  activeQueryPlanDropdown.value = activeQueryPlanDropdown.value === queryPlanId ? null : queryPlanId
}

const handleQueryPlanClick = (queryPlan: QueryPlan) => {
  queryStore.setCurrentQueryPlan(queryPlan)
  router.push(`/portal/${portalId.value}/query-plan/${queryPlan.id}`)
}

const handleExecuteQueryPlan = async (queryPlan: QueryPlan) => {
  activeQueryPlanDropdown.value = null
  try {
    executingQueryPlan.value = queryPlan
    await queryStore.executeQueryPlan(queryPlan.id!)
    queryResultModalVisible.value = true
    message.success('查询执行成功')
  } catch (error) {
    message.error('查询执行失败')
  }
}

const handleEditQueryPlan = (queryPlan: QueryPlan) => {
  activeQueryPlanDropdown.value = null
  editingQueryPlan.value = queryPlan
  queryPlanModalVisible.value = true
}

const handleDeleteQueryPlan = (queryPlan: QueryPlan) => {
  activeQueryPlanDropdown.value = null
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除查询计划"${queryPlan.name}"吗？此操作不可恢复。`,
    okText: '删除',
    cancelText: '取消',
    onOk: async () => {
      try {
        await queryStore.deleteQueryPlan(queryPlan.id!)
        message.success('删除成功')
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

const handlePortalEditSuccess = () => {
  editPortalModalVisible.value = false
  loadData()
}

const handleQueryPlanFormSuccess = () => {
  queryPlanModalVisible.value = false
  editingQueryPlan.value = null
  queryStore.loadQueryPlans(portalId.value)
}

const handlePageChange = async (page: number) => {
  await queryStore.loadQueryPlans(portalId.value, page - 1, pageSizeNum.value, queryStore.searchKeyword)
}

const handlePageSizeChange = async (current: number, size: number) => {
  await queryStore.loadQueryPlans(portalId.value, 0, size, queryStore.searchKeyword)
}

const getTargetTypeColor = (targetType: string) => {
  const colorMap: Record<string, string> = {
    'CiTask': 'blue',
    'CdTask': 'green',
    'McpServer': 'purple',
    'CloudPlatform': 'orange',
    'DevOpsProject': 'cyan',
    'DevOpsApplication': 'magenta'
  }
  return colorMap[targetType] || 'default'
}

const getTargetTypeLabel = (targetType: string) => {
  const labelMap: Record<string, string> = {
    'CiTask': 'CI任务',
    'CdTask': 'CD任务',
    'McpServer': 'MCP服务器',
    'CloudPlatform': '云平台',
    'DevOpsProject': 'DevOps项目',
    'DevOpsApplication': 'DevOps应用'
  }
  return labelMap[targetType] || targetType
}

const formatDate = (dateStr?: string) => {
  if (!dateStr) return '未知'
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadData()
})

// 监听搜索值变化
watch(searchValue, (newValue) => {
  if (!newValue.trim()) {
    handleSearch()
  }
})
</script>

<style scoped>
.portal-detail {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.portal-info {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.portal-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
}

.portal-meta {
  flex: 1;
}

.portal-name {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #262626;
}

.portal-description {
  margin: 0;
  color: #666;
  font-size: 16px;
  line-height: 1.5;
}

.header-right {
  display: flex;
  gap: 12px;
}

.dropdown-wrapper {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  min-width: 120px;
  margin-top: 4px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.88);
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.menu-item.danger {
  color: #ff4d4f;
}

.menu-item.danger:hover {
  background-color: #fff2f0;
}

.query-plans-section {
  margin-top: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.query-plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.query-plan-card {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.query-plan-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.query-plan-icon {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.card-actions {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.query-plan-card:hover .card-actions {
  opacity: 1;
}

.card-content {
  flex: 1;
}

.query-plan-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.query-plan-description {
  margin: 0 0 16px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.query-plan-meta {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.query-plan-stats {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 12px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 16px;
}

.pagination-info {
  font-size: 14px;
  color: #666;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-info {
  font-size: 14px;
  color: #666;
  min-width: 60px;
  text-align: center;
}
</style>
