<template>
  <div class="query-plan-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <Button type="text" @click="goBack">
          <template #icon>
            <Icon name="arrow-left" />
          </template>
          返回
        </Button>

        <div class="query-plan-info" v-if="currentQueryPlan">
          <div class="query-plan-icon" :style="{ backgroundColor: currentQueryPlan.color }">
            <Icon name="search" />
          </div>
          <div class="query-plan-meta">
            <h1 class="query-plan-name">{{ currentQueryPlan.name }}</h1>
            <p class="query-plan-description">{{ currentQueryPlan.description || '暂无描述' }}</p>
            <Tag :color="getTargetTypeColor(currentQueryPlan.targetType)">
              {{ getTargetTypeLabel(currentQueryPlan.targetType) }}
            </Tag>
          </div>
        </div>
      </div>

      <div class="header-right">
        <Button @click="showEditModal">
          <template #icon>
            <Icon name="edit" />
          </template>
          编辑查询计划
        </Button>
        <Button type="primary" @click="executeQuery" :loading="queryLoading">
          <template #icon>
            <Icon name="play-circle" />
          </template>
          执行查询
        </Button>
      </div>
    </div>

    <!-- 查询配置 -->
    <div class="query-config-section">
      <div class="config-card">
        <div class="card-header">
          <h3 class="card-title">查询配置</h3>
        </div>
        <div class="card-content">
          <div class="config-content" v-if="currentQueryPlan?.queryConfig">
            <div class="config-item" v-for="(value, key) in currentQueryPlan.queryConfig" :key="key">
              <span class="config-label">{{ getConfigLabel(String(key)) }}:</span>
              <span class="config-value">{{ formatConfigValue(value) }}</span>
            </div>
          </div>
          <Empty v-else description="暂无查询配置" />
        </div>
      </div>
    </div>

    <!-- 查询结果 -->
    <div class="query-results-section">
      <div class="results-card">
        <div class="card-header">
          <h3 class="card-title">查询结果</h3>
          <div class="results-actions">
            <Button size="small" @click="executeQuery" :loading="queryLoading">
              <template #icon>
                <Icon name="reload" />
              </template>
              刷新
            </Button>
            <Button size="small" @click="exportResults" :disabled="!hasResults">
              <template #icon>
                <Icon name="download" />
              </template>
              导出
            </Button>
          </div>
        </div>

        <div class="card-content">
          <div v-if="queryLoading" class="loading-wrapper">
            <div class="loading-spinner">
              <Icon name="loading" spin />
            </div>
            <p>正在执行查询...</p>
          </div>
        
          <div v-else-if="hasResults" class="results-content">
            <div class="results-summary">
              <div class="statistic-item">
                <div class="statistic-title">查询结果数量</div>
                <div class="statistic-value">{{ queryResults.length }} 条</div>
              </div>
              <div class="statistic-item">
                <div class="statistic-title">查询耗时</div>
                <div class="statistic-value">{{ queryDuration }} ms</div>
              </div>
            </div>

            <div class="results-table-wrapper">
              <table class="results-table">
                <thead>
                  <tr>
                    <th v-for="column in resultColumns" :key="column.key">
                      {{ column.title }}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="record in paginatedResults" :key="record.id">
                    <td v-for="column in resultColumns" :key="column.key">
                      <template v-if="column.key === 'status'">
                        <Tag :color="getStatusColor(record.status)">
                          {{ record.status }}
                        </Tag>
                      </template>
                      <template v-else-if="column.key === 'tags'">
                        <Tag
                          v-for="tag in record.tags"
                          :key="tag"
                          color="blue"
                          style="margin: 2px"
                        >
                          {{ tag }}
                        </Tag>
                      </template>
                      <template v-else-if="column.key === 'actions'">
                        <Button type="link" size="small" @click="viewDetails(record)">
                          查看详情
                        </Button>
                      </template>
                      <template v-else>
                        {{ record[column.dataIndex] }}
                      </template>
                    </td>
                  </tr>
                </tbody>
              </table>

              <!-- 分页 -->
              <div class="pagination-wrapper" v-if="queryResults.length > pageSize">
                <div class="pagination">
                  <div class="pagination-info">
                    共 {{ queryResults.length }} 条记录
                  </div>
                  <div class="pagination-controls">
                    <Button
                      size="small"
                      :disabled="currentPage === 1"
                      @click="handlePageChange(currentPage - 1)"
                    >
                      上一页
                    </Button>
                    <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
                    <Button
                      size="small"
                      :disabled="currentPage === totalPages"
                      @click="handlePageChange(currentPage + 1)"
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-else-if="queryError" class="error-content">
            <div class="error-result">
              <Icon name="close-circle" class="error-icon" />
              <h3>查询执行失败</h3>
              <p>{{ queryError }}</p>
              <Button type="primary" @click="executeQuery">
                重新执行
              </Button>
            </div>
          </div>

          <div v-else class="empty-results">
            <Empty description="暂无查询结果">
              <template #action>
                <Button type="primary" @click="executeQuery">
                  执行查询
                </Button>
              </template>
            </Empty>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑查询计划模态框 -->
    <!-- <QueryPlanFormModal
      v-model:visible="editModalVisible"
      :portal-id="portalId"
      :query-plan="currentQueryPlan"
      @success="handleEditSuccess"
    /> -->

    <!-- 详情查看模态框 -->
    <Modal
      v-model:visible="detailModalVisible"
      title="对象详情"
      :width="800"
      :show-footer="false"
    >
      <div v-if="selectedObject" class="object-detail">
        <div class="detail-grid">
          <div class="detail-item">
            <span class="detail-label">ID:</span>
            <span class="detail-value">{{ selectedObject.id }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">名称:</span>
            <span class="detail-value">{{ selectedObject.name }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">类型:</span>
            <span class="detail-value">{{ selectedObject.type }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">状态:</span>
            <Tag :color="getStatusColor(selectedObject.status)">
              {{ selectedObject.status }}
            </Tag>
          </div>
          <div class="detail-item detail-item-full">
            <span class="detail-label">创建时间:</span>
            <span class="detail-value">{{ formatDate(selectedObject.createdAt) }}</span>
          </div>
          <div class="detail-item detail-item-full">
            <span class="detail-label">更新时间:</span>
            <span class="detail-value">{{ formatDate(selectedObject.updatedAt) }}</span>
          </div>
          <div class="detail-item detail-item-full">
            <span class="detail-label">描述:</span>
            <span class="detail-value">{{ selectedObject.description || '暂无描述' }}</span>
          </div>
        </div>

        <div v-if="selectedObject.metadata" class="metadata-section">
          <h4>元数据</h4>
          <pre class="metadata-content">{{ JSON.stringify(selectedObject.metadata, null, 2) }}</pre>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Button from '../../../components/ui/Button.vue'
import Icon from '../../../components/ui/Icon.vue'
import Tag from '../../../components/ui/Tag.vue'
import Empty from '../../../components/ui/Empty.vue'
import Modal from '../../../components/ui/Modal.vue'
import { message } from '../../../utils/message'
import { useQueryStore } from '../stores/queryStore'
import type { QueryableObject } from '../types/portal'

// 组合式API
const route = useRoute()
const router = useRouter()
const queryStore = useQueryStore()

// 响应式数据
const editModalVisible = ref(false)
const detailModalVisible = ref(false)
const selectedObject = ref<QueryableObject | null>(null)
const queryDuration = ref(0)

// 计算属性
const portalId = computed(() => Number(route.params.portalId))
const queryPlanId = computed(() => Number(route.params.id))
const currentQueryPlan = computed(() => queryStore.currentQueryPlan)
const queryResults = computed(() => queryStore.queryResults)
const queryLoading = computed(() => queryStore.queryResultsLoading)
const queryError = computed(() => queryStore.queryResultsError)
const hasResults = computed(() => queryResults.value.length > 0)

// 分页相关
const pageSize = ref(10)
const currentPage = ref(1)
const totalPages = computed(() => Math.ceil(queryResults.value.length / pageSize.value))
const paginatedResults = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return queryResults.value.slice(start, end)
})

// 表格配置
const resultColumns = [
  { title: 'ID', dataIndex: 'id', key: 'id', width: 120 },
  { title: '名称', dataIndex: 'name', key: 'name', width: 200 },
  { title: '类型', dataIndex: 'type', key: 'type', width: 120 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '标签', dataIndex: 'tags', key: 'tags', width: 200 },
  { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt', width: 150 },
  { title: '操作', key: 'actions', width: 100, fixed: 'right' }
]

// 方法
const loadData = async () => {
  try {
    await queryStore.getQueryPlanById(queryPlanId.value)
  } catch (error) {
    message.error('加载查询计划失败')
    router.push(`/portal/${portalId.value}`)
  }
}

const goBack = () => {
  router.push(`/portal/${portalId.value}`)
}

const showEditModal = () => {
  editModalVisible.value = true
}

const executeQuery = async () => {
  try {
    const startTime = Date.now()
    await queryStore.executeQueryPlan(queryPlanId.value)
    queryDuration.value = Date.now() - startTime
    message.success('查询执行成功')
  } catch (error) {
    message.error('查询执行失败')
  }
}

const exportResults = () => {
  if (!hasResults.value) return
  
  const data = queryResults.value.map(item => ({
    ID: item.id,
    名称: item.name,
    类型: item.type,
    状态: item.status,
    标签: item.tags?.join(', ') || '',
    描述: item.description || '',
    创建时间: formatDate(item.createdAt),
    更新时间: formatDate(item.updatedAt)
  }))
  
  const csv = [
    Object.keys(data[0]).join(','),
    ...data.map(row => Object.values(row).join(','))
  ].join('\n')
  
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `${currentQueryPlan.value?.name || 'query-results'}.csv`
  link.click()
  
  message.success('导出成功')
}

const handlePageChange = (page: number) => {
  currentPage.value = page
}

const viewDetails = (record: QueryableObject) => {
  selectedObject.value = record
  detailModalVisible.value = true
}

const getConfigLabel = (key: string) => {
  const labelMap: Record<string, string> = {
    nameFilter: '名称过滤',
    typeFilter: '类型过滤',
    statusFilter: '状态过滤',
    tagFilters: '标签过滤',
    sortBy: '排序字段',
    sortOrder: '排序方向',
    limit: '结果限制'
  }
  return labelMap[key] || key
}

const formatConfigValue = (value: any) => {
  if (Array.isArray(value)) {
    return value.join(', ')
  }
  return String(value)
}

const getTargetTypeColor = (targetType: string) => {
  const colorMap: Record<string, string> = {
    'CiTask': 'blue',
    'CdTask': 'green',
    'McpServer': 'purple',
    'CloudPlatform': 'orange',
    'DevOpsProject': 'cyan',
    'DevOpsApplication': 'magenta'
  }
  return colorMap[targetType] || 'default'
}

const getTargetTypeLabel = (targetType: string) => {
  const labelMap: Record<string, string> = {
    'CiTask': 'CI任务',
    'CdTask': 'CD任务',
    'McpServer': 'MCP服务器',
    'CloudPlatform': '云平台',
    'DevOpsProject': 'DevOps项目',
    'DevOpsApplication': 'DevOps应用'
  }
  return labelMap[targetType] || targetType
}

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'ACTIVE': 'green',
    'INACTIVE': 'orange',
    'ERROR': 'red',
    'PENDING': 'blue',
    'SUCCESS': 'green',
    'FAILED': 'red'
  }
  return colorMap[status] || 'default'
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '未知'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.query-plan-detail {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.query-plan-info {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.query-plan-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
}

.query-plan-meta {
  flex: 1;
}

.query-plan-name {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #262626;
}

.query-plan-description {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 16px;
  line-height: 1.5;
}

.header-right {
  display: flex;
  gap: 12px;
}

.query-config-section,
.query-results-section {
  margin-bottom: 24px;
}

.config-card,
.results-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 4px;
}

.config-label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.config-value {
  color: #262626;
}

.results-actions {
  display: flex;
  gap: 8px;
}

.loading-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  gap: 16px;
}

.results-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.results-summary {
  display: flex;
  gap: 48px;
}

.results-table {
  margin-top: 16px;
}

.error-content,
.empty-results {
  padding: 40px 0;
}

.object-detail {
  max-height: 600px;
  overflow-y: auto;
}

.metadata-section {
  margin-top: 24px;
}

.metadata-section h4 {
  margin-bottom: 12px;
  color: #262626;
}

.metadata-content {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

/* 自定义组件样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.card-content {
  padding: 24px;
}

.config-card, .results-card {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background: white;
  margin-bottom: 24px;
}

.statistic-item {
  text-align: center;
}

.statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.statistic-value {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
}

.results-table-wrapper {
  overflow-x: auto;
}

.results-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #f0f0f0;
}

.results-table th,
.results-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.results-table th {
  background: #fafafa;
  font-weight: 600;
}

.loading-spinner {
  display: inline-block;
  margin-right: 8px;
}

.error-result {
  text-align: center;
  padding: 40px;
}

.error-icon {
  font-size: 48px;
  color: #ff4d4f;
  margin-bottom: 16px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item-full {
  grid-column: 1 / -1;
}

.detail-label {
  font-weight: 600;
  color: #666;
  min-width: 80px;
}

.detail-value {
  color: #333;
}
</style>
