/**
 * Portal API服务
 * 提供门户管理的HTTP请求方法
 */

import axios from 'axios'
import type { Portal, PageResponse } from '../types/portal'

const API_BASE_URL = '/api/portals'

/**
 * Portal API类
 */
export class PortalApi {
  /**
   * 创建门户
   */
  static async createPortal(portal: Omit<Portal, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<Portal> {
    const response = await axios.post<Portal>(API_BASE_URL, portal, {
      headers: {
        'X-User-Id': this.getUserId()
      }
    })
    return response.data
  }

  /**
   * 获取门户列表（分页）
   */
  static async getPortals(page = 0, size = 10, search?: string): Promise<PageResponse<Portal>> {
    const params: Record<string, any> = { page, size }
    if (search) {
      params.search = search
    }

    const response = await axios.get<PageResponse<Portal>>(API_BASE_URL, {
      params,
      headers: {
        'X-User-Id': this.getUserId()
      }
    })
    return response.data
  }

  /**
   * 获取门户详情
   */
  static async getPortalById(id: number): Promise<Portal> {
    const response = await axios.get<Portal>(`${API_BASE_URL}/${id}`, {
      headers: {
        'X-User-Id': this.getUserId()
      }
    })
    return response.data
  }

  /**
   * 更新门户
   */
  static async updatePortal(id: number, portal: Partial<Portal>): Promise<Portal> {
    const response = await axios.put<Portal>(`${API_BASE_URL}/${id}`, portal, {
      headers: {
        'X-User-Id': this.getUserId()
      }
    })
    return response.data
  }

  /**
   * 删除门户
   */
  static async deletePortal(id: number): Promise<void> {
    await axios.delete(`${API_BASE_URL}/${id}`, {
      headers: {
        'X-User-Id': this.getUserId()
      }
    })
  }

  /**
   * 获取用户的所有门户（不分页）
   */
  static async getAllPortals(): Promise<Portal[]> {
    const response = await axios.get<Portal[]>(`${API_BASE_URL}/all`, {
      headers: {
        'X-User-Id': this.getUserId()
      }
    })
    return response.data
  }

  /**
   * 统计用户的门户数量
   */
  static async getPortalCount(): Promise<number> {
    const response = await axios.get<{ count: number }>(`${API_BASE_URL}/count`, {
      headers: {
        'X-User-Id': this.getUserId()
      }
    })
    return response.data.count
  }

  /**
   * 获取当前用户ID
   * 在实际应用中，这应该从认证状态或用户上下文中获取
   */
  private static getUserId(): string {
    // 临时实现，实际应该从认证状态获取
    return '1'
  }
}

/**
 * 导出默认实例
 */
export default PortalApi
