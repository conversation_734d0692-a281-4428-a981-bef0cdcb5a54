/**
 * Query API服务
 * 提供查询计划管理和查询执行的HTTP请求方法
 */

import axios from 'axios'
import type { 
  QueryPlan, 
  QueryRequest, 
  QueryResponse, 
  PageResponse, 
  TargetTypeInfo 
} from '../types/portal'

const API_BASE_URL = '/api'

/**
 * Query API类
 */
export class QueryApi {
  /**
   * 创建查询计划
   */
  static async createQueryPlan(
    portalId: number, 
    queryPlan: Omit<QueryPlan, 'id' | 'portalId' | 'userId' | 'createdAt' | 'updatedAt'>
  ): Promise<QueryPlan> {
    const response = await axios.post<QueryPlan>(
      `${API_BASE_URL}/portals/${portalId}/query-plans`, 
      queryPlan,
      {
        headers: {
          'X-User-Id': this.getUserId()
        }
      }
    )
    return response.data
  }

  /**
   * 获取门户下的查询计划列表（分页）
   */
  static async getQueryPlans(
    portalId: number, 
    page = 0, 
    size = 10, 
    search?: string
  ): Promise<PageResponse<QueryPlan>> {
    const params: Record<string, any> = { page, size }
    if (search) {
      params.search = search
    }

    const response = await axios.get<PageResponse<QueryPlan>>(
      `${API_BASE_URL}/portals/${portalId}/query-plans`,
      {
        params,
        headers: {
          'X-User-Id': this.getUserId()
        }
      }
    )
    return response.data
  }

  /**
   * 获取查询计划详情
   */
  static async getQueryPlanById(id: number): Promise<QueryPlan> {
    const response = await axios.get<QueryPlan>(`${API_BASE_URL}/query-plans/${id}`, {
      headers: {
        'X-User-Id': this.getUserId()
      }
    })
    return response.data
  }

  /**
   * 更新查询计划
   */
  static async updateQueryPlan(id: number, queryPlan: Partial<QueryPlan>): Promise<QueryPlan> {
    const response = await axios.put<QueryPlan>(`${API_BASE_URL}/query-plans/${id}`, queryPlan, {
      headers: {
        'X-User-Id': this.getUserId()
      }
    })
    return response.data
  }

  /**
   * 删除查询计划
   */
  static async deleteQueryPlan(id: number): Promise<void> {
    await axios.delete(`${API_BASE_URL}/query-plans/${id}`, {
      headers: {
        'X-User-Id': this.getUserId()
      }
    })
  }

  /**
   * 执行查询计划
   */
  static async executeQueryPlan(id: number): Promise<QueryResponse> {
    const response = await axios.post<QueryResponse>(
      `${API_BASE_URL}/query-plans/${id}/execute`,
      {},
      {
        headers: {
          'X-User-Id': this.getUserId()
        }
      }
    )
    return response.data
  }

  /**
   * 执行通用查询
   */
  static async executeQuery(request: QueryRequest): Promise<QueryResponse> {
    const response = await axios.post<QueryResponse>(`${API_BASE_URL}/query/search`, request)
    return response.data
  }

  /**
   * 获取支持的目标类型
   */
  static async getSupportedTargetTypes(): Promise<TargetTypeInfo[]> {
    const response = await axios.get<TargetTypeInfo[]>(`${API_BASE_URL}/query/target-types`)
    return response.data
  }

  /**
   * 获取当前用户ID
   * 在实际应用中，这应该从认证状态或用户上下文中获取
   */
  private static getUserId(): string {
    // 临时实现，实际应该从认证状态获取
    return '1'
  }
}

/**
 * 导出默认实例
 */
export default QueryApi
