/**
 * Portal模块的TypeScript类型定义
 * 与后端DTO保持一致
 */

/**
 * 门户接口
 */
export interface Portal {
  id?: number
  name: string
  description?: string
  color?: string
  icon?: string
  status?: string
  userId?: number
  queryPlanCount?: number
  createdAt?: string
  updatedAt?: string
}

/**
 * 查询计划接口
 */
export interface QueryPlan {
  id?: number
  name: string
  description?: string
  portalId: number
  targetType: string
  queryConfig?: QueryConfig
  color?: string
  icon?: string
  status?: string
  userId?: number
  resultCount?: number
  lastExecuted?: string
  createdAt?: string
  updatedAt?: string
}

/**
 * 查询配置接口
 */
export interface QueryConfig {
  nameFilter?: string
  typeFilter?: string[]
  tagFilters?: string[]
  statusFilter?: string[]
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  limit?: number
  [key: string]: any
}

/**
 * 查询请求接口
 */
export interface QueryRequest {
  targetType: string
  nameFilter?: string
  typeFilter?: string[]
  tagFilters?: string[]
  statusFilter?: string[]
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  page?: number
  size?: number
}

/**
 * 可查询对象接口
 */
export interface QueryableObject {
  id: string
  name: string
  type: string
  tags?: string[]
  status: string
  description?: string
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
}

/**
 * 查询响应接口
 */
export interface QueryResponse {
  items: QueryableObject[]
  total: number
  page: number
  size: number
  hasNext: boolean
  hasPrevious: boolean
}

/**
 * 分页响应接口
 */
export interface PageResponse<T> {
  content: T[]
  page: number
  size: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrevious: boolean
}

/**
 * 目标类型信息接口
 */
export interface TargetTypeInfo {
  type: string
  displayName: string
  description: string
  availableFields: string[]
  availableStatuses: string[]
}

/**
 * 门户状态枚举
 */
export enum PortalStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived'
}

/**
 * 目标类型枚举
 */
export enum TargetType {
  CI_TASK = 'CiTask',
  CD_TASK = 'CdTask',
  MCP_SERVER = 'McpServer',
  CLOUD_PLATFORM = 'CloudPlatform',
  DEVOPS_PROJECT = 'DevOpsProject',
  DEVOPS_APPLICATION = 'DevOpsApplication'
}

/**
 * 排序方向枚举
 */
export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc'
}

/**
 * 门户表单数据接口
 */
export interface PortalFormData {
  name: string
  description: string
  color: string
  icon: string
}

/**
 * 查询计划表单数据接口
 */
export interface QueryPlanFormData {
  name: string
  description: string
  targetType: string
  queryConfig: QueryConfig
  color: string
  icon: string
}

/**
 * 面包屑项接口
 */
export interface BreadcrumbItem {
  name: string
  path: string
  icon?: string
}

/**
 * 门户上下文接口
 */
export interface PortalContext {
  currentPortal: Portal | null
  currentQueryPlan: QueryPlan | null
  breadcrumbs: BreadcrumbItem[]
}
