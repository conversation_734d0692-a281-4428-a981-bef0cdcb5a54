<template>
  <Modal
    :visible="visible"
    :title="isEdit ? '编辑门户' : '创建门户'"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :width="600"
  >
    <form class="portal-form" @submit.prevent="handleSubmit">
      <div class="form-item">
        <label class="form-label">门户名称 <span class="required">*</span></label>
        <Input
          v-model="formData.name"
          placeholder="请输入门户名称"
          :maxlength="50"
          show-count
        />
        <div v-if="errors.name" class="form-error">{{ errors.name }}</div>
      </div>

      <div class="form-item">
        <label class="form-label">门户描述</label>
        <Input
          v-model="formData.description"
          placeholder="请输入门户描述（可选）"
          :rows="3"
          :maxlength="200"
          show-count
          is-textarea
        />
        <div v-if="errors.description" class="form-error">{{ errors.description }}</div>
      </div>

      <div class="form-item">
        <label class="form-label">门户颜色 <span class="required">*</span></label>
        <div class="color-picker-wrapper">
          <div
            class="color-preview"
            :style="{ backgroundColor: formData.color }"
            @click="showColorPicker = !showColorPicker"
          />
          <Input
            v-model="formData.color"
            placeholder="#1890ff"
            style="margin-left: 12px"
          />
        </div>
        <div v-if="showColorPicker" class="color-palette">
          <div
            v-for="color in colorOptions"
            :key="color"
            class="color-option"
            :class="{ active: formData.color === color }"
            :style="{ backgroundColor: color }"
            @click="selectColor(color)"
          />
        </div>
        <div v-if="errors.color" class="form-error">{{ errors.color }}</div>
      </div>

      <div class="form-item">
        <label class="form-label">门户图标 <span class="required">*</span></label>
        <div class="icon-selector">
          <div
            v-for="iconOption in iconOptions"
            :key="iconOption.value"
            class="icon-option"
            :class="{ active: formData.icon === iconOption.value }"
            @click="formData.icon = iconOption.value"
          >
            <Icon :name="iconOption.value" :size="16" />
            <span class="icon-label">{{ iconOption.label }}</span>
          </div>
        </div>
        <div v-if="errors.icon" class="form-error">{{ errors.icon }}</div>
      </div>
    </form>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue'
import Modal from '../../../components/ui/Modal.vue'
import Input from '../../../components/ui/Input.vue'
import Icon from '../../../components/ui/Icon.vue'
import { message } from '../../../utils/message'
import { usePortalStore } from '../stores/portalStore'
import type { Portal, PortalFormData } from '../types/portal'

// Props
interface Props {
  visible: boolean
  portal?: Portal | null
}

const props = withDefaults(defineProps<Props>(), {
  portal: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 组合式API
const portalStore = usePortalStore()

// 响应式数据
const loading = ref(false)
const showColorPicker = ref(false)
const errors = reactive<Record<string, string>>({})

const formData = reactive<PortalFormData>({
  name: '',
  description: '',
  color: '#1890ff',
  icon: 'folder'
})

// 计算属性
const isEdit = computed(() => !!props.portal?.id)

// 表单验证
const validateForm = () => {
  const newErrors: Record<string, string> = {}

  if (!formData.name.trim()) {
    newErrors.name = '请输入门户名称'
  } else if (formData.name.length > 50) {
    newErrors.name = '门户名称长度不能超过50个字符'
  }

  if (formData.description && formData.description.length > 200) {
    newErrors.description = '门户描述不能超过200个字符'
  }

  if (!formData.color) {
    newErrors.color = '请选择门户颜色'
  } else if (!/^#[0-9A-Fa-f]{6}$/.test(formData.color)) {
    newErrors.color = '请输入有效的颜色值（如#1890ff）'
  }

  if (!formData.icon) {
    newErrors.icon = '请选择门户图标'
  }

  Object.assign(errors, newErrors)
  return Object.keys(newErrors).length === 0
}

// 颜色选项
const colorOptions = [
  '#1890ff', '#52c41a', '#faad14', '#f5222d',
  '#722ed1', '#13c2c2', '#eb2f96', '#fa541c',
  '#2f54eb', '#a0d911', '#fadb14', '#ff4d4f',
  '#9254de', '#36cfc9', '#f759ab', '#ff7a45'
]

// 图标选项
const iconOptions = [
  { value: 'folder', label: '文件夹' },
  { value: 'appstore', label: '应用' },
  { value: 'database', label: '数据库' },
  { value: 'cloud', label: '云服务' },
  { value: 'project', label: '项目' },
  { value: 'setting', label: '设置' }
]

// 方法
const resetForm = () => {
  formData.name = ''
  formData.description = ''
  formData.color = '#1890ff'
  formData.icon = 'folder'
  Object.keys(errors).forEach(key => delete errors[key])
}

const loadPortalData = () => {
  if (props.portal) {
    formData.name = props.portal.name
    formData.description = props.portal.description || ''
    formData.color = props.portal.color || '#1890ff'
    formData.icon = props.portal.icon || 'folder'
  } else {
    resetForm()
  }
}

const selectColor = (color: string) => {
  formData.color = color
  showColorPicker.value = false
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  try {
    loading.value = true

    if (isEdit.value) {
      await portalStore.updatePortal(props.portal!.id!, formData)
      message.success('更新门户成功')
    } else {
      await portalStore.createPortal(formData)
      message.success('创建门户成功')
    }

    emit('success')
    handleCancel()
  } catch (error) {
    console.error('提交失败:', error)
    message.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
  showColorPicker.value = false
}

// 监听器
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      loadPortalData()
    } else {
      showColorPicker.value = false
    }
  }
)

watch(
  () => props.portal,
  () => {
    if (props.visible) {
      loadPortalData()
    }
  }
)
</script>

<style scoped>
.portal-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.88);
}

.required {
  color: #ff4d4f;
}

.form-error {
  font-size: 12px;
  color: #ff4d4f;
  margin-top: 4px;
}

.color-picker-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-preview {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-preview:hover {
  border-color: #1890ff;
}

.color-palette {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
  margin-top: 12px;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
}

.color-option {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.color-option:hover,
.color-option.active {
  border-color: #1890ff;
  transform: scale(1.1);
}

.icon-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.icon-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.icon-option:hover {
  border-color: #1890ff;
  background-color: #f0f8ff;
}

.icon-option.active {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.icon-option .icon {
  margin-bottom: 8px;
  color: #666;
}

.icon-option.active .icon {
  color: #1890ff;
}

.icon-label {
  font-size: 12px;
  color: #666;
}

.icon-option.active .icon-label {
  color: #1890ff;
}
</style>
