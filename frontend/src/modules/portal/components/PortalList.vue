<template>
  <div class="portal-list">
    <!-- 头部操作栏 -->
    <div class="portal-list-header">
      <div class="header-left">
        <h2 class="title">工作区域</h2>
        <span class="count">共 {{ totalCount }} 个门户</span>
      </div>
      <div class="header-right">
        <Input
          v-model="searchValue"
          placeholder="搜索门户名称"
          search
          style="width: 300px; margin-right: 16px"
          @search="handleSearch"
          @pressEnter="handleSearch"
        />
        <Button type="primary" @click="showCreateModal">
          <template #icon>
            <Icon name="plus" />
          </template>
          新建门户
        </Button>
      </div>
    </div>

    <!-- 门户卡片网格 -->
    <div class="portal-grid" v-if="!isEmpty">
      <div
        v-for="portal in portals"
        :key="portal.id"
        class="portal-card"
        @click="handlePortalClick(portal)"
      >
        <div class="card-header">
          <div class="portal-icon" :style="{ backgroundColor: portal.color }">
            <Icon :name="portal.icon" :size="20" />
          </div>
          <div class="card-actions">
            <div class="dropdown-wrapper">
              <Button
                type="text"
                size="small"
                @click.stop="toggleCardDropdown(portal.id)"
              >
                <Icon name="more" />
              </Button>

              <div
                v-if="activeDropdown === portal.id"
                class="dropdown-menu"
                @click.stop
              >
                <div class="menu-item" @click="handleEdit(portal)">
                  <Icon name="edit" />
                  编辑
                </div>
                <div class="menu-item danger" @click="handleDelete(portal)">
                  <Icon name="delete" />
                  删除
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="card-content">
          <h3 class="portal-name">{{ portal.name }}</h3>
          <p class="portal-description">{{ portal.description || '暂无描述' }}</p>
          
          <div class="portal-stats">
            <span class="stat-item">
              <Icon name="search" />
              {{ portal.queryPlanCount || 0 }} 个查询计划
            </span>
            <span class="stat-item">
              <Icon name="calendar" />
              {{ formatDate(portal.updatedAt) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <Empty description="暂无门户" image="simple">
        <Button type="primary" @click="showCreateModal">
          创建第一个门户
        </Button>
      </Empty>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="!isEmpty">
      <div class="pagination">
        <div class="pagination-info">
          第 {{ (currentPageNum - 1) * pageSizeNum + 1 }}-{{ Math.min(currentPageNum * pageSizeNum, totalCount) }} 条，共 {{ totalCount }} 条
        </div>
        <div class="pagination-controls">
          <Button
            :disabled="currentPageNum <= 1"
            @click="handlePageChange(currentPageNum - 1)"
          >
            上一页
          </Button>
          <span class="page-info">{{ currentPageNum }} / {{ Math.ceil(totalCount / pageSizeNum) }}</span>
          <Button
            :disabled="currentPageNum >= Math.ceil(totalCount / pageSizeNum)"
            @click="handlePageChange(currentPageNum + 1)"
          >
            下一页
          </Button>
        </div>
      </div>
    </div>

    <!-- 创建/编辑门户模态框 -->
    <PortalFormModal
      v-model:visible="formModalVisible"
      :portal="editingPortal"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import Button from '../../../components/ui/Button.vue'
import Input from '../../../components/ui/Input.vue'
import Icon from '../../../components/ui/Icon.vue'
import Empty from '../../../components/ui/Empty.vue'
import { message } from '../../../utils/message'
import { Modal } from '../../../utils/modal'
import { usePortalStore } from '../stores/portalStore'
import type { Portal } from '../types/portal'
import PortalFormModal from './PortalFormModal.vue'

// 组合式API
const router = useRouter()
const portalStore = usePortalStore()

// 响应式数据
const searchValue = ref('')
const formModalVisible = ref(false)
const editingPortal = ref<Portal | null>(null)
const activeDropdown = ref<number | null>(null)

// 计算属性
const portals = computed(() => portalStore.portals)
const loading = computed(() => portalStore.loading)
const isEmpty = computed(() => portalStore.isEmpty)
const totalCount = computed(() => portalStore.totalCount)
const currentPageNum = computed({
  get: () => portalStore.currentPage + 1, // 转换为1开始的页码
  set: (value) => portalStore.currentPage = value - 1
})
const pageSizeNum = computed({
  get: () => portalStore.pageSize,
  set: (value) => portalStore.pageSize = value
})

// 方法
const loadData = async () => {
  await portalStore.loadPortals()
}

const handleSearch = async () => {
  if (searchValue.value.trim()) {
    await portalStore.searchPortals(searchValue.value.trim())
  } else {
    await portalStore.clearSearch()
  }
}

const handlePortalClick = (portal: Portal) => {
  portalStore.setCurrentPortal(portal)
  router.push(`/portal/${portal.id}`)
}

const toggleCardDropdown = (portalId: number) => {
  activeDropdown.value = activeDropdown.value === portalId ? null : portalId
}

const showCreateModal = () => {
  editingPortal.value = null
  formModalVisible.value = true
}

const handleEdit = (portal: Portal) => {
  editingPortal.value = portal
  formModalVisible.value = true
}

const handleDelete = (portal: Portal) => {
  activeDropdown.value = null
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除门户"${portal.name}"吗？此操作不可恢复。`,
    okText: '删除',
    cancelText: '取消',
    onOk: async () => {
      try {
        await portalStore.deletePortal(portal.id!)
        message.success('删除成功')
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

const handleFormSuccess = () => {
  formModalVisible.value = false
  editingPortal.value = null
  loadData()
}

const handlePageChange = async (page: number) => {
  await portalStore.loadPortals(page - 1, pageSizeNum.value, portalStore.searchKeyword)
}

const handlePageSizeChange = async (current: number, size: number) => {
  await portalStore.loadPortals(0, size, portalStore.searchKeyword)
}

const formatDate = (dateStr?: string) => {
  if (!dateStr) return '未知'
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadData()
})

// 监听搜索值变化
watch(searchValue, (newValue) => {
  if (!newValue.trim()) {
    handleSearch()
  }
})
</script>

<style scoped>
.portal-list {
  padding: 24px;
}

.portal-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.count {
  color: #666;
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.dropdown-wrapper {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  min-width: 120px;
  margin-top: 4px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.88);
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.menu-item.danger {
  color: #ff4d4f;
}

.menu-item.danger:hover {
  background-color: #fff2f0;
}

.portal-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.portal-card {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.portal-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.portal-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.card-actions {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.portal-card:hover .card-actions {
  opacity: 1;
}

.card-content {
  flex: 1;
}

.portal-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.portal-description {
  margin: 0 0 16px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.portal-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 12px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 16px;
}

.pagination-info {
  font-size: 14px;
  color: #666;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-info {
  font-size: 14px;
  color: #666;
  min-width: 60px;
  text-align: center;
}
</style>
