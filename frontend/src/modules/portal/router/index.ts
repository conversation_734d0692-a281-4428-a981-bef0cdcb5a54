/**
 * Portal模块路由配置
 */

import type { RouteRecordRaw } from 'vue-router'

const portalRoutes: RouteRecordRaw[] = [
  {
    path: '/portal',
    name: 'Portal',
    component: () => import('../views/PortalList.vue'),
    meta: {
      title: '工作区域',
      requiresAuth: true
    }
  },
  {
    path: '/portal/:id',
    name: 'PortalDetail',
    component: () => import('../views/PortalDetail.vue'),
    meta: {
      title: '门户详情',
      requiresAuth: true
    }
  },
  {
    path: '/portal/:portalId/query-plan/:id',
    name: 'QueryPlanDetail',
    component: () => import('../views/QueryPlanDetail.vue'),
    meta: {
      title: '查询计划详情',
      requiresAuth: true
    }
  }
]

export default portalRoutes
