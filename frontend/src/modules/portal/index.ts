/**
 * Portal模块入口文件
 * 导出模块的主要组件、类型、API等
 */

// 组件
export { default as PortalList } from './components/PortalList.vue'
export { default as PortalFormModal } from './components/PortalFormModal.vue'

// 视图
export { default as PortalListView } from './views/PortalList.vue'
export { default as PortalDetailView } from './views/PortalDetail.vue'

// 状态管理
export { usePortalStore } from './stores/portalStore'
export { useQueryStore } from './stores/queryStore'

// API服务
export { PortalApi } from './api/portalApi'
export { QueryApi } from './api/queryApi'

// 类型定义
export type {
  Portal,
  QueryPlan,
  QueryConfig,
  QueryRequest,
  QueryableObject,
  QueryResponse,
  PageResponse,
  TargetTypeInfo,
  PortalFormData,
  QueryPlanFormData,
  BreadcrumbItem,
  PortalContext
} from './types/portal'

export {
  PortalStatus,
  TargetType,
  SortOrder
} from './types/portal'

// 路由
export { default as portalRoutes } from './router/index'

// 模块信息
export const portalModuleInfo = {
  name: 'portal',
  version: '1.0.0',
  description: '工作区域管理模块',
  author: 'Spring Vue App Team'
}
