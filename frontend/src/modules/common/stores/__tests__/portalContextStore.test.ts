/**
 * Portal Context Store 单元测试
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { usePortalContextStore } from '../portalContextStore'
import type { Portal, QueryPlan, BreadcrumbItem } from '../../../portal/types/portal'

describe('Portal Context Store', () => {
  let portalContextStore: ReturnType<typeof usePortalContextStore>

  const mockPortal: Portal = {
    id: 1,
    name: '测试门户',
    description: '测试门户描述',
    color: '#1890ff',
    icon: 'folder',
    status: 'active',
    userId: 1,
    queryPlanCount: 5,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }

  const mockQueryPlan: QueryPlan = {
    id: 1,
    name: '测试查询计划',
    description: '测试查询计划描述',
    portalId: 1,
    targetType: 'CiTask',
    queryConfig: { nameFilter: 'test' },
    color: '#52c41a',
    icon: 'search',
    status: 'active',
    userId: 1,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }

  beforeEach(() => {
    setActivePinia(createPinia())
    portalContextStore = usePortalContextStore()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(portalContextStore.currentPortal).toBeNull()
      expect(portalContextStore.currentQueryPlan).toBeNull()
      expect(portalContextStore.breadcrumbs).toEqual([])
      expect(portalContextStore.isPortalMode).toBe(false)
      expect(portalContextStore.hasCurrentPortal).toBe(false)
      expect(portalContextStore.hasCurrentQueryPlan).toBe(false)
      expect(portalContextStore.currentPortalId).toBeUndefined()
      expect(portalContextStore.currentQueryPlanId).toBeUndefined()
    })
  })

  describe('setCurrentPortal', () => {
    it('应该设置当前门户并更新面包屑', () => {
      // When
      portalContextStore.setCurrentPortal(mockPortal)

      // Then
      expect(portalContextStore.currentPortal).toEqual(mockPortal)
      expect(portalContextStore.hasCurrentPortal).toBe(true)
      expect(portalContextStore.currentPortalId).toBe(1)
      expect(portalContextStore.breadcrumbs).toEqual([
        {
          name: '工作区域',
          path: '/portal',
          icon: 'appstore'
        },
        {
          name: '测试门户',
          path: '/portal/1',
          icon: 'folder'
        }
      ])
    })

    it('应该清除当前门户', () => {
      // Given
      portalContextStore.setCurrentPortal(mockPortal)

      // When
      portalContextStore.setCurrentPortal(null)

      // Then
      expect(portalContextStore.currentPortal).toBeNull()
      expect(portalContextStore.hasCurrentPortal).toBe(false)
      expect(portalContextStore.currentPortalId).toBeUndefined()
    })

    it('应该在切换门户时清除不匹配的查询计划', () => {
      // Given
      portalContextStore.setCurrentPortal(mockPortal)
      portalContextStore.setCurrentQueryPlan(mockQueryPlan)

      const anotherPortal = { ...mockPortal, id: 2, name: '另一个门户' }

      // When
      portalContextStore.setCurrentPortal(anotherPortal)

      // Then
      expect(portalContextStore.currentPortal).toEqual(anotherPortal)
      expect(portalContextStore.currentQueryPlan).toBeNull()
    })
  })

  describe('setCurrentQueryPlan', () => {
    it('应该设置当前查询计划并更新面包屑', () => {
      // Given
      portalContextStore.setCurrentPortal(mockPortal)

      // When
      portalContextStore.setCurrentQueryPlan(mockQueryPlan)

      // Then
      expect(portalContextStore.currentQueryPlan).toEqual(mockQueryPlan)
      expect(portalContextStore.hasCurrentQueryPlan).toBe(true)
      expect(portalContextStore.currentQueryPlanId).toBe(1)
      expect(portalContextStore.breadcrumbs).toEqual([
        {
          name: '工作区域',
          path: '/portal',
          icon: 'appstore'
        },
        {
          name: '测试门户',
          path: '/portal/1',
          icon: 'folder'
        },
        {
          name: '测试查询计划',
          path: '/portal/1/query-plan/1',
          icon: 'search'
        }
      ])
    })

    it('应该清除当前查询计划', () => {
      // Given
      portalContextStore.setCurrentPortal(mockPortal)
      portalContextStore.setCurrentQueryPlan(mockQueryPlan)

      // When
      portalContextStore.setCurrentQueryPlan(null)

      // Then
      expect(portalContextStore.currentQueryPlan).toBeNull()
      expect(portalContextStore.hasCurrentQueryPlan).toBe(false)
      expect(portalContextStore.currentQueryPlanId).toBeUndefined()
    })
  })

  describe('enterPortalMode', () => {
    it('应该进入门户模式', () => {
      // When
      portalContextStore.enterPortalMode()

      // Then
      expect(portalContextStore.isPortalMode).toBe(true)
    })

    it('应该进入门户模式并设置门户', () => {
      // When
      portalContextStore.enterPortalMode(mockPortal)

      // Then
      expect(portalContextStore.isPortalMode).toBe(true)
      expect(portalContextStore.currentPortal).toEqual(mockPortal)
    })
  })

  describe('exitPortalMode', () => {
    it('应该退出门户模式并清除所有状态', () => {
      // Given
      portalContextStore.enterPortalMode(mockPortal)
      portalContextStore.setCurrentQueryPlan(mockQueryPlan)

      // When
      portalContextStore.exitPortalMode()

      // Then
      expect(portalContextStore.isPortalMode).toBe(false)
      expect(portalContextStore.currentPortal).toBeNull()
      expect(portalContextStore.currentQueryPlan).toBeNull()
      expect(portalContextStore.breadcrumbs).toEqual([])
    })
  })

  describe('面包屑管理', () => {
    it('应该设置自定义面包屑', () => {
      // Given
      const customBreadcrumbs: BreadcrumbItem[] = [
        { name: '首页', path: '/', icon: 'home' },
        { name: '设置', path: '/settings', icon: 'setting' }
      ]

      // When
      portalContextStore.setBreadcrumbs(customBreadcrumbs)

      // Then
      expect(portalContextStore.breadcrumbs).toEqual(customBreadcrumbs)
    })

    it('应该添加面包屑项', () => {
      // Given
      const initialBreadcrumb: BreadcrumbItem = { name: '首页', path: '/', icon: 'home' }
      const newBreadcrumb: BreadcrumbItem = { name: '设置', path: '/settings', icon: 'setting' }
      
      portalContextStore.setBreadcrumbs([initialBreadcrumb])

      // When
      portalContextStore.addBreadcrumb(newBreadcrumb)

      // Then
      expect(portalContextStore.breadcrumbs).toEqual([initialBreadcrumb, newBreadcrumb])
    })

    it('应该移除最后一个面包屑项', () => {
      // Given
      const breadcrumbs: BreadcrumbItem[] = [
        { name: '首页', path: '/', icon: 'home' },
        { name: '设置', path: '/settings', icon: 'setting' }
      ]
      portalContextStore.setBreadcrumbs(breadcrumbs)

      // When
      portalContextStore.popBreadcrumb()

      // Then
      expect(portalContextStore.breadcrumbs).toEqual([breadcrumbs[0]])
    })

    it('应该清空面包屑', () => {
      // Given
      const breadcrumbs: BreadcrumbItem[] = [
        { name: '首页', path: '/', icon: 'home' },
        { name: '设置', path: '/settings', icon: 'setting' }
      ]
      portalContextStore.setBreadcrumbs(breadcrumbs)

      // When
      portalContextStore.clearBreadcrumbs()

      // Then
      expect(portalContextStore.breadcrumbs).toEqual([])
    })
  })

  describe('getPortalMenuItems', () => {
    it('应该返回门户相关的菜单项', () => {
      // Given
      portalContextStore.setCurrentPortal(mockPortal)

      // When
      const menuItems = portalContextStore.getPortalMenuItems()

      // Then
      expect(menuItems).toEqual([
        {
          key: 'portal-overview',
          label: '门户概览',
          path: '/portal/1',
          icon: 'dashboard'
        },
        {
          key: 'query-plans',
          label: '查询计划',
          path: '/portal/1/query-plans',
          icon: 'search'
        },
        {
          key: 'portal-settings',
          label: '门户设置',
          path: '/portal/1/settings',
          icon: 'setting'
        }
      ])
    })

    it('应该在没有当前门户时返回空数组', () => {
      // When
      const menuItems = portalContextStore.getPortalMenuItems()

      // Then
      expect(menuItems).toEqual([])
    })
  })

  describe('isInPortal', () => {
    it('应该正确判断是否在指定门户中', () => {
      // Given
      portalContextStore.setCurrentPortal(mockPortal)

      // When & Then
      expect(portalContextStore.isInPortal(1)).toBe(true)
      expect(portalContextStore.isInPortal(2)).toBe(false)
    })

    it('应该在没有当前门户时返回false', () => {
      // When & Then
      expect(portalContextStore.isInPortal(1)).toBe(false)
    })
  })

  describe('isInQueryPlan', () => {
    it('应该正确判断是否在指定查询计划中', () => {
      // Given
      portalContextStore.setCurrentPortal(mockPortal)
      portalContextStore.setCurrentQueryPlan(mockQueryPlan)

      // When & Then
      expect(portalContextStore.isInQueryPlan(1)).toBe(true)
      expect(portalContextStore.isInQueryPlan(2)).toBe(false)
    })

    it('应该在没有当前查询计划时返回false', () => {
      // When & Then
      expect(portalContextStore.isInQueryPlan(1)).toBe(false)
    })
  })

  describe('reset', () => {
    it('应该重置所有状态', () => {
      // Given
      portalContextStore.enterPortalMode(mockPortal)
      portalContextStore.setCurrentQueryPlan(mockQueryPlan)
      portalContextStore.setBreadcrumbs([{ name: '测试', path: '/test', icon: 'test' }])

      // When
      portalContextStore.reset()

      // Then
      expect(portalContextStore.currentPortal).toBeNull()
      expect(portalContextStore.currentQueryPlan).toBeNull()
      expect(portalContextStore.breadcrumbs).toEqual([])
      expect(portalContextStore.isPortalMode).toBe(false)
    })
  })

  describe('context计算属性', () => {
    it('应该返回正确的上下文对象', () => {
      // Given
      portalContextStore.setCurrentPortal(mockPortal)
      portalContextStore.setCurrentQueryPlan(mockQueryPlan)

      // When
      const context = portalContextStore.context

      // Then
      expect(context).toEqual({
        currentPortal: mockPortal,
        currentQueryPlan: mockQueryPlan,
        breadcrumbs: portalContextStore.breadcrumbs
      })
    })
  })
})
