/**
 * 全局Portal上下文Store
 * 管理当前门户信息，供其他模块使用
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Portal, QueryPlan, BreadcrumbItem, PortalContext } from '../../portal/types/portal'

/**
 * Portal上下文Store
 * 用于在应用全局范围内管理当前门户状态
 */
export const usePortalContextStore = defineStore('portalContext', () => {
  // 状态
  const currentPortal = ref<Portal | null>(null)
  const currentQueryPlan = ref<QueryPlan | null>(null)
  const breadcrumbs = ref<BreadcrumbItem[]>([])
  const isPortalMode = ref(false) // 是否处于门户模式

  // 计算属性
  const hasCurrentPortal = computed(() => !!currentPortal.value)
  const hasCurrentQueryPlan = computed(() => !!currentQueryPlan.value)
  const currentPortalId = computed(() => currentPortal.value?.id)
  const currentQueryPlanId = computed(() => currentQueryPlan.value?.id)
  
  const context = computed<PortalContext>(() => ({
    currentPortal: currentPortal.value,
    currentQueryPlan: currentQueryPlan.value,
    breadcrumbs: breadcrumbs.value
  }))

  // Actions

  /**
   * 设置当前门户
   */
  const setCurrentPortal = (portal: Portal | null) => {
    currentPortal.value = portal
    
    // 清空当前查询计划（因为切换了门户）
    if (currentQueryPlan.value && portal?.id !== currentQueryPlan.value.portalId) {
      currentQueryPlan.value = null
    }
    
    // 更新面包屑
    updateBreadcrumbs()
  }

  /**
   * 设置当前查询计划
   */
  const setCurrentQueryPlan = (queryPlan: QueryPlan | null) => {
    currentQueryPlan.value = queryPlan
    
    // 如果查询计划属于不同的门户，需要更新当前门户
    if (queryPlan && (!currentPortal.value || currentPortal.value.id !== queryPlan.portalId)) {
      // 这里可能需要从API获取门户信息
      // 暂时只设置查询计划
    }
    
    // 更新面包屑
    updateBreadcrumbs()
  }

  /**
   * 进入门户模式
   */
  const enterPortalMode = (portal?: Portal) => {
    isPortalMode.value = true
    if (portal) {
      setCurrentPortal(portal)
    }
  }

  /**
   * 退出门户模式
   */
  const exitPortalMode = () => {
    isPortalMode.value = false
    currentPortal.value = null
    currentQueryPlan.value = null
    breadcrumbs.value = []
  }

  /**
   * 更新面包屑导航
   */
  const updateBreadcrumbs = () => {
    const newBreadcrumbs: BreadcrumbItem[] = []
    
    // 添加门户列表
    newBreadcrumbs.push({
      name: '工作区域',
      path: '/portal',
      icon: 'appstore'
    })
    
    // 添加当前门户
    if (currentPortal.value) {
      newBreadcrumbs.push({
        name: currentPortal.value.name,
        path: `/portal/${currentPortal.value.id}`,
        icon: currentPortal.value.icon
      })
    }
    
    // 添加当前查询计划
    if (currentQueryPlan.value) {
      newBreadcrumbs.push({
        name: currentQueryPlan.value.name,
        path: `/portal/${currentPortal.value?.id}/query-plan/${currentQueryPlan.value.id}`,
        icon: 'search'
      })
    }
    
    breadcrumbs.value = newBreadcrumbs
  }

  /**
   * 设置自定义面包屑
   */
  const setBreadcrumbs = (newBreadcrumbs: BreadcrumbItem[]) => {
    breadcrumbs.value = newBreadcrumbs
  }

  /**
   * 添加面包屑项
   */
  const addBreadcrumb = (breadcrumb: BreadcrumbItem) => {
    breadcrumbs.value.push(breadcrumb)
  }

  /**
   * 移除最后一个面包屑项
   */
  const popBreadcrumb = () => {
    breadcrumbs.value.pop()
  }

  /**
   * 清空面包屑
   */
  const clearBreadcrumbs = () => {
    breadcrumbs.value = []
  }

  /**
   * 获取门户相关的导航菜单项
   */
  const getPortalMenuItems = () => {
    if (!currentPortal.value) return []
    
    return [
      {
        key: 'portal-overview',
        label: '门户概览',
        path: `/portal/${currentPortal.value.id}`,
        icon: 'dashboard'
      },
      {
        key: 'query-plans',
        label: '查询计划',
        path: `/portal/${currentPortal.value.id}/query-plans`,
        icon: 'search'
      },
      {
        key: 'portal-settings',
        label: '门户设置',
        path: `/portal/${currentPortal.value.id}/settings`,
        icon: 'setting'
      }
    ]
  }

  /**
   * 检查是否在指定门户中
   */
  const isInPortal = (portalId: number) => {
    return currentPortal.value?.id === portalId
  }

  /**
   * 检查是否在指定查询计划中
   */
  const isInQueryPlan = (queryPlanId: number) => {
    return currentQueryPlan.value?.id === queryPlanId
  }

  /**
   * 重置所有状态
   */
  const reset = () => {
    currentPortal.value = null
    currentQueryPlan.value = null
    breadcrumbs.value = []
    isPortalMode.value = false
  }

  /**
   * 从路由参数初始化上下文
   */
  const initFromRoute = (routeParams: Record<string, any>) => {
    // 这个方法可以根据路由参数来初始化上下文状态
    // 在路由守卫中调用
    const { portalId, queryPlanId } = routeParams
    
    if (portalId && !currentPortal.value) {
      // 需要从API获取门户信息
      // 这里暂时只设置ID，实际应该调用API
    }
    
    if (queryPlanId && !currentQueryPlan.value) {
      // 需要从API获取查询计划信息
      // 这里暂时只设置ID，实际应该调用API
    }
  }

  return {
    // 状态
    currentPortal,
    currentQueryPlan,
    breadcrumbs,
    isPortalMode,
    
    // 计算属性
    hasCurrentPortal,
    hasCurrentQueryPlan,
    currentPortalId,
    currentQueryPlanId,
    context,
    
    // Actions
    setCurrentPortal,
    setCurrentQueryPlan,
    enterPortalMode,
    exitPortalMode,
    updateBreadcrumbs,
    setBreadcrumbs,
    addBreadcrumb,
    popBreadcrumb,
    clearBreadcrumbs,
    getPortalMenuItems,
    isInPortal,
    isInQueryPlan,
    reset,
    initFromRoute
  }
})
