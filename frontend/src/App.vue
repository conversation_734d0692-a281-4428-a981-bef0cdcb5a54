<template>
  <div class="app">
    <!-- 登录页面不使用主布局 -->
    <router-view v-if="isLoginPage" />

    <!-- 其他页面使用主布局 -->
    <MainLayout v-else />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import MainLayout from './components/layout/MainLayout.vue'

const route = useRoute()

// 计算属性：判断是否为登录页面
const isLoginPage = computed(() => route.path === '/login')
</script>

<style>
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

.app {
  width: 100%;
  min-height: 100vh;
}

/* 全局样式 */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
}

#app {
  height: 100%;
}
</style>
