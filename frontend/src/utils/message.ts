import { createApp, h } from 'vue'

interface MessageOptions {
  content: string
  type: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  onClose?: () => void
}

interface MessageInstance {
  id: string
  destroy: () => void
}

let messageContainer: HTMLElement | null = null
let messageId = 0
const messages: MessageInstance[] = []

// 创建消息容器
function createMessageContainer() {
  if (!messageContainer) {
    messageContainer = document.createElement('div')
    messageContainer.className = 'message-container'
    messageContainer.style.cssText = `
      position: fixed;
      top: 24px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1010;
      pointer-events: none;
    `
    document.body.appendChild(messageContainer)
  }
  return messageContainer
}

// 消息组件
const MessageComponent = {
  props: {
    type: String,
    content: String,
    onDestroy: Function
  },
  setup(props: any) {
    return () => h('div', {
      class: `message message-${props.type}`,
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        padding: '8px 16px',
        marginBottom: '8px',
        backgroundColor: '#fff',
        border: '1px solid #f0f0f0',
        borderRadius: '6px',
        boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
        fontSize: '14px',
        lineHeight: '1.5',
        pointerEvents: 'auto',
        animation: 'messageSlideIn 0.3s ease-out',
        maxWidth: '400px',
        wordBreak: 'break-word'
      }
    }, [
      // 图标
      h('span', {
        style: {
          flexShrink: 0,
          fontSize: '16px',
          color: getIconColor(props.type)
        }
      }, getIcon(props.type)),
      
      // 内容
      h('span', {
        style: {
          color: 'rgba(0, 0, 0, 0.88)'
        }
      }, props.content)
    ])
  }
}

function getIcon(type: string) {
  const icons = {
    success: '✓',
    error: '✕',
    warning: '⚠',
    info: 'ℹ'
  }
  return icons[type as keyof typeof icons] || icons.info
}

function getIconColor(type: string) {
  const colors = {
    success: '#52c41a',
    error: '#ff4d4f',
    warning: '#faad14',
    info: '#1890ff'
  }
  return colors[type as keyof typeof colors] || colors.info
}

// 显示消息
function showMessage(options: MessageOptions): MessageInstance {
  const container = createMessageContainer()
  const id = `message-${++messageId}`
  
  const messageElement = document.createElement('div')
  messageElement.id = id
  container.appendChild(messageElement)
  
  const app = createApp(MessageComponent, {
    type: options.type,
    content: options.content,
    onDestroy: () => destroy()
  })
  
  app.mount(messageElement)
  
  const destroy = () => {
    app.unmount()
    if (messageElement.parentNode) {
      messageElement.style.animation = 'messageSlideOut 0.3s ease-in'
      setTimeout(() => {
        messageElement.parentNode?.removeChild(messageElement)
        
        // 如果没有消息了，移除容器
        if (container.children.length === 0) {
          document.body.removeChild(container)
          messageContainer = null
        }
      }, 300)
    }
    
    // 从消息列表中移除
    const index = messages.findIndex(msg => msg.id === id)
    if (index > -1) {
      messages.splice(index, 1)
    }
    
    options.onClose?.()
  }
  
  // 自动关闭
  const duration = options.duration ?? 3000
  if (duration > 0) {
    setTimeout(destroy, duration)
  }
  
  const instance: MessageInstance = { id, destroy }
  messages.push(instance)
  
  return instance
}

// 添加CSS动画
if (typeof document !== 'undefined') {
  const style = document.createElement('style')
  style.textContent = `
    @keyframes messageSlideIn {
      from {
        opacity: 0;
        transform: translateY(-100%);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    @keyframes messageSlideOut {
      from {
        opacity: 1;
        transform: translateY(0);
      }
      to {
        opacity: 0;
        transform: translateY(-100%);
      }
    }
  `
  document.head.appendChild(style)
}

// 导出API
export const message = {
  success: (content: string, duration?: number, onClose?: () => void) => 
    showMessage({ content, type: 'success', duration, onClose }),
    
  error: (content: string, duration?: number, onClose?: () => void) => 
    showMessage({ content, type: 'error', duration, onClose }),
    
  warning: (content: string, duration?: number, onClose?: () => void) => 
    showMessage({ content, type: 'warning', duration, onClose }),
    
  info: (content: string, duration?: number, onClose?: () => void) => 
    showMessage({ content, type: 'info', duration, onClose }),
    
  destroy: () => {
    messages.forEach(msg => msg.destroy())
    messages.length = 0
  }
}

export default message
