import { createApp, h } from 'vue'
import Button from '../components/ui/Button.vue'

interface ConfirmOptions {
  title?: string
  content: string
  okText?: string
  cancelText?: string
  type?: 'info' | 'success' | 'error' | 'warning'
  onOk?: () => void | Promise<void>
  onCancel?: () => void
}

// 确认对话框组件
const ConfirmModal = {
  props: {
    title: String,
    content: String,
    okText: String,
    cancelText: String,
    type: String,
    onOk: Function,
    onCancel: Function,
    onDestroy: Function
  },
  setup(props: any) {
    let loading = false
    
    const handleOk = async () => {
      if (props.onOk) {
        loading = true
        try {
          await props.onOk()
          props.onDestroy()
        } catch (error) {
          console.error('确认操作失败:', error)
        } finally {
          loading = false
        }
      } else {
        props.onDestroy()
      }
    }
    
    const handleCancel = () => {
      props.onCancel?.()
      props.onDestroy()
    }
    
    const handleOverlayClick = (event: MouseEvent) => {
      if (event.target === event.currentTarget) {
        handleCancel()
      }
    }
    
    return () => h('div', {
      class: 'modal-overlay',
      style: {
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.45)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000
      },
      onClick: handleOverlayClick
    }, [
      h('div', {
        class: 'modal-container',
        style: {
          background: '#fff',
          borderRadius: '8px',
          boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
          width: '416px',
          maxWidth: 'calc(100vw - 32px)'
        }
      }, [
        // 头部
        h('div', {
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '20px 24px 8px',
            fontSize: '16px',
            fontWeight: 500,
            color: 'rgba(0, 0, 0, 0.88)'
          }
        }, [
          // 图标
          h('span', {
            style: {
              fontSize: '22px',
              color: getIconColor(props.type)
            }
          }, getIcon(props.type)),
          
          // 标题
          h('span', props.title || '确认')
        ]),
        
        // 内容
        h('div', {
          style: {
            padding: '8px 24px 20px 58px',
            fontSize: '14px',
            color: 'rgba(0, 0, 0, 0.88)',
            lineHeight: '1.5'
          }
        }, props.content),
        
        // 底部按钮
        h('div', {
          style: {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
            gap: '8px',
            padding: '10px 16px',
            borderTop: '1px solid #f0f0f0'
          }
        }, [
          h(Button, {
            onClick: handleCancel
          }, () => props.cancelText || '取消'),
          
          h(Button, {
            type: 'primary',
            loading,
            onClick: handleOk
          }, () => props.okText || '确定')
        ])
      ])
    ])
  }
}

function getIcon(type: string) {
  const icons = {
    info: 'ℹ',
    success: '✓',
    error: '✕',
    warning: '⚠'
  }
  return icons[type as keyof typeof icons] || icons.info
}

function getIconColor(type: string) {
  const colors = {
    info: '#1890ff',
    success: '#52c41a',
    error: '#ff4d4f',
    warning: '#faad14'
  }
  return colors[type as keyof typeof colors] || colors.info
}

// 显示确认对话框
function showConfirm(options: ConfirmOptions): Promise<void> {
  return new Promise((resolve, reject) => {
    const container = document.createElement('div')
    document.body.appendChild(container)
    
    const destroy = () => {
      app.unmount()
      document.body.removeChild(container)
      document.body.style.overflow = ''
    }
    
    const app = createApp(ConfirmModal, {
      ...options,
      onOk: async () => {
        try {
          await options.onOk?.()
          resolve()
        } catch (error) {
          reject(error)
        }
      },
      onCancel: () => {
        options.onCancel?.()
        reject(new Error('用户取消'))
      },
      onDestroy: destroy
    })
    
    app.mount(container)
    
    // 阻止body滚动
    document.body.style.overflow = 'hidden'
  })
}

// 导出API
export const Modal = {
  confirm: (options: ConfirmOptions) => showConfirm({ ...options, type: 'info' }),
  
  info: (options: Omit<ConfirmOptions, 'type'>) => 
    showConfirm({ ...options, type: 'info' }),
    
  success: (options: Omit<ConfirmOptions, 'type'>) => 
    showConfirm({ ...options, type: 'success' }),
    
  error: (options: Omit<ConfirmOptions, 'type'>) => 
    showConfirm({ ...options, type: 'error' }),
    
  warning: (options: Omit<ConfirmOptions, 'type'>) => 
    showConfirm({ ...options, type: 'warning' })
}

export default Modal
