# 服务器配置
server.port=8080

# R2DBC 数据库配置（响应式）
spring.r2dbc.url=r2dbc:h2:file:////home/<USER>/data/testdb;AUTO_SERVER=TRUE
spring.r2dbc.username=sa
spring.r2dbc.password=password

# 日志配置
logging.level.org.springframework.data.r2dbc=DEBUG
logging.level.org.springframework.jdbc.core=DEBUG

# JWT 配置
jwt.secret=yourSecretKeyHereMakeItLongAndSecureForProductionUse
jwt.expiration=86400000

# SQL initialization
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema.sql

# Tekton 配置
tekton.namespace=default
tekton.connection.timeout=30

# MCP Configuration
mcp.sandbox.type=docker
mcp.sandbox.docker.host=unix:///var/run/docker.sock
mcp.sandbox.docker.connection-timeout=30s
mcp.sandbox.docker.response-timeout=45s
mcp.sandbox.docker.max-connections=100
mcp.sandbox.default-timeout=300
mcp.sandbox.cleanup-on-shutdown=true

# Default Docker images for common runtimes
mcp.docker.images.node=node:18-alpine
mcp.docker.images.python=python:3.11-alpine
mcp.docker.images.java=openjdk:17-alpine
mcp.docker.images.go=golang:1.21-alpine

# Resource limits
mcp.sandbox.default-memory-limit=512MB
mcp.sandbox.default-cpu-limit=1.0
mcp.sandbox.max-memory-limit=2GB
mcp.sandbox.max-cpu-limit=4.0

# Additional logging
logging.level.com.example.springvueapp=DEBUG
logging.level.com.github.dockerjava=INFO
