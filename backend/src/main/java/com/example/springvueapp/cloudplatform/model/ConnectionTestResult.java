package com.example.springvueapp.cloudplatform.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 连接测试结果模型
 * 用于返回云平台连接测试的结果
 */
public class ConnectionTestResult {

    /**
     * 连接是否成功
     */
    private boolean success;

    /**
     * 连接状态
     */
    private String status;

    /**
     * 响应时间（毫秒）
     */
    private long responseTime;

    /**
     * 测试时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime testTime;

    /**
     * 错误消息（连接失败时）
     */
    private String errorMessage;

    /**
     * 平台信息（连接成功时）
     */
    private Map<String, Object> platformInfo;

    /**
     * 详细信息
     */
    private String details;

    // 构造函数
    public ConnectionTestResult() {
        this.testTime = LocalDateTime.now();
    }

    public ConnectionTestResult(boolean success, String status, long responseTime) {
        this();
        this.success = success;
        this.status = status;
        this.responseTime = responseTime;
    }

    public ConnectionTestResult(boolean success, String status, long responseTime, 
                              String errorMessage, Map<String, Object> platformInfo, String details) {
        this(success, status, responseTime);
        this.errorMessage = errorMessage;
        this.platformInfo = platformInfo;
        this.details = details;
    }

    /**
     * 创建成功的测试结果
     */
    public static ConnectionTestResult success(long responseTime, Map<String, Object> platformInfo, String details) {
        ConnectionTestResult result = new ConnectionTestResult(true, "CONNECTED", responseTime);
        result.setPlatformInfo(platformInfo);
        result.setDetails(details);
        return result;
    }

    /**
     * 创建失败的测试结果
     */
    public static ConnectionTestResult failure(long responseTime, String errorMessage, String details) {
        ConnectionTestResult result = new ConnectionTestResult(false, "ERROR", responseTime);
        result.setErrorMessage(errorMessage);
        result.setDetails(details);
        return result;
    }

    // Getter 和 Setter 方法
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public long getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(long responseTime) {
        this.responseTime = responseTime;
    }

    public LocalDateTime getTestTime() {
        return testTime;
    }

    public void setTestTime(LocalDateTime testTime) {
        this.testTime = testTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Map<String, Object> getPlatformInfo() {
        return platformInfo;
    }

    public void setPlatformInfo(Map<String, Object> platformInfo) {
        this.platformInfo = platformInfo;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    @Override
    public String toString() {
        return "ConnectionTestResult{" +
                "success=" + success +
                ", status='" + status + '\'' +
                ", responseTime=" + responseTime +
                ", testTime=" + testTime +
                ", errorMessage='" + errorMessage + '\'' +
                ", platformInfo=" + platformInfo +
                ", details='" + details + '\'' +
                '}';
    }
}
