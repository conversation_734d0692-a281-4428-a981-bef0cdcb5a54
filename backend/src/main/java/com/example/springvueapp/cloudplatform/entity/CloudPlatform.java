package com.example.springvueapp.cloudplatform.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * 云平台实体类
 * 对应数据库表 cloud_platforms
 * 用于存储云平台的配置信息和连接状态
 */
@Table("cloud_platforms")
public class CloudPlatform {

    @Id
    private Long id;

    /**
     * 平台名称
     */
    @Column("name")
    private String name;

    /**
     * 平台类型
     * 支持的类型：KUBERNETES, K3S, DOCKER, DOCKER_SWARM, ALIYUN, TENCENT_CLOUD, AWS, AZURE, OPENSTACK, VMWARE
     */
    @Column("type")
    private String type;

    /**
     * 访问地址
     */
    @Column("url")
    private String url;

    /**
     * 认证密钥（加密存储）
     */
    @Column("secret")
    private String secret;

    /**
     * 平台描述
     */
    @Column("description")
    private String description;

    /**
     * 连接状态
     * 支持的状态：CONNECTED, DISCONNECTED, CONNECTING, ERROR, UNKNOWN
     */
    @Column("status")
    private String status;

    /**
     * 所属区域
     */
    @Column("region")
    private String region;

    /**
     * 平台版本
     */
    @Column("version")
    private String version;

    /**
     * 标签（JSON格式）
     */
    @Column("tags")
    private String tags;

    /**
     * 创建人
     */
    @Column("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新人
     */
    @Column("updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @Column("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 用户ID
     */
    @Column("user_id")
    private Long userId;

    // 构造函数
    public CloudPlatform() {
    }

    public CloudPlatform(String name, String type, String url, String secret, String description, 
                        String status, String region, String version, String tags, 
                        String createdBy, Long userId) {
        this.name = name;
        this.type = type;
        this.url = url;
        this.secret = secret;
        this.description = description;
        this.status = status;
        this.region = region;
        this.version = version;
        this.tags = tags;
        this.createdBy = createdBy;
        this.userId = userId;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return "CloudPlatform{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", url='" + url + '\'' +
                ", description='" + description + '\'' +
                ", status='" + status + '\'' +
                ", region='" + region + '\'' +
                ", version='" + version + '\'' +
                ", createdBy='" + createdBy + '\'' +
                ", createdAt=" + createdAt +
                ", updatedBy='" + updatedBy + '\'' +
                ", updatedAt=" + updatedAt +
                ", userId=" + userId +
                '}';
    }
}
