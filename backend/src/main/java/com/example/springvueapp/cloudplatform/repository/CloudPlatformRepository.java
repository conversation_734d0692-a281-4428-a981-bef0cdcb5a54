package com.example.springvueapp.cloudplatform.repository;

import com.example.springvueapp.cloudplatform.entity.CloudPlatform;
import com.example.springvueapp.cloudplatform.model.PlatformStats;
import org.springframework.data.domain.Pageable;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 云平台数据访问层
 * 基于Spring Data R2DBC实现响应式数据库操作
 */
@Repository
public interface CloudPlatformRepository extends R2dbcRepository<CloudPlatform, Long> {

    /**
     * 根据用户ID查询云平台列表
     * @param userId 用户ID
     * @return 云平台列表
     */
    Flux<CloudPlatform> findByUserId(Long userId);

    /**
     * 根据用户ID和平台名称查询云平台
     * @param userId 用户ID
     * @param name 平台名称
     * @return 云平台信息
     */
    Mono<CloudPlatform> findByUserIdAndName(Long userId, String name);

    /**
     * 根据用户ID和平台类型查询云平台列表
     * @param userId 用户ID
     * @param type 平台类型
     * @return 云平台列表
     */
    Flux<CloudPlatform> findByUserIdAndType(Long userId, String type);

    /**
     * 根据用户ID和连接状态查询云平台列表
     * @param userId 用户ID
     * @param status 连接状态
     * @return 云平台列表
     */
    Flux<CloudPlatform> findByUserIdAndStatus(Long userId, String status);

    /**
     * 根据用户ID和区域查询云平台列表
     * @param userId 用户ID
     * @param region 区域
     * @return 云平台列表
     */
    Flux<CloudPlatform> findByUserIdAndRegion(Long userId, String region);

    /**
     * 根据用户ID和平台名称模糊查询云平台列表
     * @param userId 用户ID
     * @param name 平台名称（支持模糊匹配）
     * @return 云平台列表
     */
    @Query("SELECT * FROM cloud_platforms WHERE user_id = :userId AND name LIKE CONCAT('%', :name, '%') ORDER BY created_at DESC")
    Flux<CloudPlatform> findByUserIdAndNameContaining(@Param("userId") Long userId, @Param("name") String name);

    /**
     * 根据多个条件查询云平台列表（分页）
     * @param userId 用户ID
     * @param name 平台名称（可选，支持模糊匹配）
     * @param type 平台类型（可选）
     * @param status 连接状态（可选）
     * @param region 区域（可选）
     * @param pageable 分页参数
     * @return 云平台列表
     */
    @Query("SELECT * FROM cloud_platforms WHERE user_id = :userId " +
           "AND (:name IS NULL OR name LIKE CONCAT('%', :name, '%')) " +
           "AND (:type IS NULL OR type = :type) " +
           "AND (:status IS NULL OR status = :status) " +
           "AND (:region IS NULL OR region = :region) " +
           "ORDER BY created_at DESC " +
           "LIMIT :#{#pageable.pageSize} OFFSET :#{#pageable.offset}")
    Flux<CloudPlatform> findByConditions(@Param("userId") Long userId,
                                        @Param("name") String name,
                                        @Param("type") String type,
                                        @Param("status") String status,
                                        @Param("region") String region,
                                        Pageable pageable);

    /**
     * 统计符合条件的云平台数量
     * @param userId 用户ID
     * @param name 平台名称（可选，支持模糊匹配）
     * @param type 平台类型（可选）
     * @param status 连接状态（可选）
     * @param region 区域（可选）
     * @return 数量
     */
    @Query("SELECT COUNT(*) FROM cloud_platforms WHERE user_id = :userId " +
           "AND (:name IS NULL OR name LIKE CONCAT('%', :name, '%')) " +
           "AND (:type IS NULL OR type = :type) " +
           "AND (:status IS NULL OR status = :status) " +
           "AND (:region IS NULL OR region = :region)")
    Mono<Long> countByConditions(@Param("userId") Long userId,
                                @Param("name") String name,
                                @Param("type") String type,
                                @Param("status") String status,
                                @Param("region") String region);

    /**
     * 根据用户ID和ID查询云平台
     * @param userId 用户ID
     * @param id 云平台ID
     * @return 云平台信息
     */
    Mono<CloudPlatform> findByUserIdAndId(Long userId, Long id);

    /**
     * 检查用户是否已存在同名的云平台
     * @param userId 用户ID
     * @param name 平台名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM cloud_platforms WHERE user_id = :userId AND name = :name " +
           "AND (:excludeId IS NULL OR id != :excludeId)")
    Mono<Boolean> existsByUserIdAndNameAndIdNot(@Param("userId") Long userId, 
                                               @Param("name") String name, 
                                               @Param("excludeId") Long excludeId);

    /**
     * 根据用户ID统计各种状态的云平台数量
     * @param userId 用户ID
     * @return 统计结果
     */
    @Query("SELECT status, COUNT(*) as count FROM cloud_platforms WHERE user_id = :userId GROUP BY status")
    Flux<PlatformStats> countByUserIdGroupByStatus(@Param("userId") Long userId);

    /**
     * 根据用户ID统计各种类型的云平台数量
     * @param userId 用户ID
     * @return 统计结果
     */
    @Query("SELECT type, COUNT(*) as count FROM cloud_platforms WHERE user_id = :userId GROUP BY type")
    Flux<PlatformStats> countByUserIdGroupByType(@Param("userId") Long userId);
}
