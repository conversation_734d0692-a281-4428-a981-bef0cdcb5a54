package com.example.springvueapp.cloudplatform.service;

import com.example.springvueapp.cloudplatform.entity.CloudPlatform;
import com.example.springvueapp.cloudplatform.entity.PlatformType;
import com.example.springvueapp.cloudplatform.model.ConnectionTestResult;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.DefaultKubernetesClient;
import io.fabric8.kubernetes.client.Config;
import io.fabric8.kubernetes.client.ConfigBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 云平台连接测试服务
 * 负责测试各种云平台的连接状态
 */
@Service
public class CloudPlatformConnectionService {

    private static final Logger logger = LoggerFactory.getLogger(CloudPlatformConnectionService.class);
    
    private static final Duration CONNECTION_TIMEOUT = Duration.ofSeconds(10);
    private static final Duration REQUEST_TIMEOUT = Duration.ofSeconds(30);

    /**
     * 测试云平台连接
     * @param platform 云平台配置
     * @return 连接测试结果
     */
    public Mono<ConnectionTestResult> testConnection(CloudPlatform platform) {
        logger.debug("开始测试云平台连接: {}", platform.getName());
        
        long startTime = System.currentTimeMillis();
        
        return Mono.fromCallable(() -> {
            try {
                PlatformType platformType = PlatformType.fromString(platform.getType());
                
                switch (platformType) {
                    case KUBERNETES:
                    case K3S:
                        return testKubernetesConnection(platform, startTime);
                    case DOCKER:
                    case DOCKER_SWARM:
                        return testDockerConnection(platform, startTime);
                    case ALIYUN:
                    case TENCENT_CLOUD:
                    case AWS:
                    case AZURE:
                    case OPENSTACK:
                    case VMWARE:
                        return testCloudProviderConnection(platform, startTime);
                    default:
                        return ConnectionTestResult.failure(
                            System.currentTimeMillis() - startTime,
                            "不支持的平台类型: " + platform.getType(),
                            "该平台类型暂不支持连接测试"
                        );
                }
            } catch (Exception e) {
                logger.error("连接测试异常: {}", platform.getName(), e);
                return ConnectionTestResult.failure(
                    System.currentTimeMillis() - startTime,
                    e.getMessage(),
                    "连接测试过程中发生异常"
                );
            }
        })
        .timeout(REQUEST_TIMEOUT)
        .onErrorReturn(ConnectionTestResult.failure(
            System.currentTimeMillis() - startTime,
            "连接超时",
            "连接测试超时，请检查网络连接和平台配置"
        ));
    }

    /**
     * 测试Kubernetes连接
     */
    private ConnectionTestResult testKubernetesConnection(CloudPlatform platform, long startTime) {
        try {
            // 构建Kubernetes客户端配置
            Config config = new ConfigBuilder()
                    .withMasterUrl(platform.getUrl())
                    .withOauthToken(platform.getSecret()) // 假设secret是token
                    .withTrustCerts(true) // 在生产环境中应该正确配置证书
                    .withConnectionTimeout((int) CONNECTION_TIMEOUT.toMillis())
                    .withRequestTimeout((int) REQUEST_TIMEOUT.toMillis())
                    .build();

            try (KubernetesClient client = new DefaultKubernetesClient(config)) {
                // 尝试获取集群信息 - 通过列出命名空间来测试连接
                var namespaces = client.namespaces().list();

                Map<String, Object> platformInfo = new HashMap<>();
                platformInfo.put("namespacesCount", namespaces.getItems().size());
                platformInfo.put("serverUrl", config.getMasterUrl());

                long responseTime = System.currentTimeMillis() - startTime;

                return ConnectionTestResult.success(
                    responseTime,
                    platformInfo,
                    "成功连接到Kubernetes集群，发现 " + namespaces.getItems().size() + " 个命名空间"
                );
            }
        } catch (Exception e) {
            logger.warn("Kubernetes连接测试失败: {}", platform.getName(), e);
            return ConnectionTestResult.failure(
                System.currentTimeMillis() - startTime,
                e.getMessage(),
                "无法连接到Kubernetes集群，请检查URL和认证信息"
            );
        }
    }

    /**
     * 测试Docker连接
     */
    private ConnectionTestResult testDockerConnection(CloudPlatform platform, long startTime) {
        try {
            // 构建HTTP请求测试Docker API
            HttpClient httpClient = HttpClient.newBuilder()
                    .connectTimeout(CONNECTION_TIMEOUT)
                    .build();

            // Docker API的版本端点
            String versionUrl = platform.getUrl().endsWith("/") ? 
                platform.getUrl() + "version" : platform.getUrl() + "/version";

            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                    .uri(URI.create(versionUrl))
                    .timeout(REQUEST_TIMEOUT)
                    .GET();

            // 如果有认证信息，添加到请求头
            if (platform.getSecret() != null && !platform.getSecret().trim().isEmpty()) {
                requestBuilder.header("Authorization", "Bearer " + platform.getSecret());
            }

            HttpRequest request = requestBuilder.build();
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == 200) {
                Map<String, Object> platformInfo = new HashMap<>();
                platformInfo.put("dockerVersion", "检测成功");
                platformInfo.put("apiVersion", "可访问");
                
                long responseTime = System.currentTimeMillis() - startTime;
                
                return ConnectionTestResult.success(
                    responseTime,
                    platformInfo,
                    "成功连接到Docker API"
                );
            } else {
                return ConnectionTestResult.failure(
                    System.currentTimeMillis() - startTime,
                    "HTTP " + response.statusCode(),
                    "Docker API返回错误状态码: " + response.statusCode()
                );
            }
        } catch (Exception e) {
            logger.warn("Docker连接测试失败: {}", platform.getName(), e);
            return ConnectionTestResult.failure(
                System.currentTimeMillis() - startTime,
                e.getMessage(),
                "无法连接到Docker API，请检查URL和认证信息"
            );
        }
    }

    /**
     * 测试云服务商连接
     */
    private ConnectionTestResult testCloudProviderConnection(CloudPlatform platform, long startTime) {
        try {
            // 对于云服务商，我们只做简单的HTTP连通性测试
            HttpClient httpClient = HttpClient.newBuilder()
                    .connectTimeout(CONNECTION_TIMEOUT)
                    .build();

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(platform.getUrl()))
                    .timeout(REQUEST_TIMEOUT)
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            Map<String, Object> platformInfo = new HashMap<>();
            platformInfo.put("httpStatus", response.statusCode());
            platformInfo.put("provider", platform.getType());
            
            long responseTime = System.currentTimeMillis() - startTime;

            if (response.statusCode() < 500) { // 4xx和2xx都认为是可达的
                return ConnectionTestResult.success(
                    responseTime,
                    platformInfo,
                    "云平台端点可访问，HTTP状态: " + response.statusCode()
                );
            } else {
                return ConnectionTestResult.failure(
                    responseTime,
                    "HTTP " + response.statusCode(),
                    "云平台端点返回服务器错误"
                );
            }
        } catch (Exception e) {
            logger.warn("云服务商连接测试失败: {}", platform.getName(), e);
            return ConnectionTestResult.failure(
                System.currentTimeMillis() - startTime,
                e.getMessage(),
                "无法访问云平台端点，请检查URL配置"
            );
        }
    }
}
