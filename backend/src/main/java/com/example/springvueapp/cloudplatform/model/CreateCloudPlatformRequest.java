package com.example.springvueapp.cloudplatform.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.Map;

/**
 * 创建云平台请求模型
 * 用于接收创建云平台的请求参数
 */
public class CreateCloudPlatformRequest {

    /**
     * 平台名称
     */
    @NotBlank(message = "平台名称不能为空")
    @Size(max = 100, message = "平台名称长度不能超过100个字符")
    private String name;

    /**
     * 平台类型
     */
    @NotBlank(message = "平台类型不能为空")
    private String type;

    /**
     * 访问地址
     */
    @NotBlank(message = "访问地址不能为空")
    @Size(max = 500, message = "访问地址长度不能超过500个字符")
    private String url;

    /**
     * 认证密钥
     */
    @NotBlank(message = "认证密钥不能为空")
    private String secret;

    /**
     * 平台描述
     */
    @Size(max = 1000, message = "平台描述长度不能超过1000个字符")
    private String description;

    /**
     * 所属区域
     */
    @Size(max = 100, message = "所属区域长度不能超过100个字符")
    private String region;

    /**
     * 平台版本
     */
    @Size(max = 50, message = "平台版本长度不能超过50个字符")
    private String version;

    /**
     * 标签信息
     */
    private Map<String, String> tags;

    // 构造函数
    public CreateCloudPlatformRequest() {
    }

    public CreateCloudPlatformRequest(String name, String type, String url, String secret, 
                                    String description, String region, String version, 
                                    Map<String, String> tags) {
        this.name = name;
        this.type = type;
        this.url = url;
        this.secret = secret;
        this.description = description;
        this.region = region;
        this.version = version;
        this.tags = tags;
    }

    // Getter 和 Setter 方法
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Map<String, String> getTags() {
        return tags;
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
    }

    @Override
    public String toString() {
        return "CreateCloudPlatformRequest{" +
                "name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", url='" + url + '\'' +
                ", description='" + description + '\'' +
                ", region='" + region + '\'' +
                ", version='" + version + '\'' +
                ", tags=" + tags +
                '}';
    }
}
