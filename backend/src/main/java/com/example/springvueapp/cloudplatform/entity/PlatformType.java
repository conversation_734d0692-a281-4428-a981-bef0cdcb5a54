package com.example.springvueapp.cloudplatform.entity;

/**
 * 云平台类型枚举
 * 定义支持的云平台类型
 */
public enum PlatformType {
    
    /**
     * Kubernetes集群
     */
    KUBERNETES("Kubernetes", "Kubernetes集群"),
    
    /**
     * K3s轻量级集群
     */
    K3S("K3s", "K3s轻量级集群"),
    
    /**
     * Docker环境
     */
    DOCKER("Docker", "Docker环境"),
    
    /**
     * Docker Swarm集群
     */
    DOCKER_SWARM("Docker Swarm", "Docker Swarm集群"),
    
    /**
     * 阿里云
     */
    ALIYUN("阿里云", "阿里云"),
    
    /**
     * 腾讯云
     */
    TENCENT_CLOUD("腾讯云", "腾讯云"),
    
    /**
     * Amazon Web Services
     */
    AWS("AWS", "Amazon Web Services"),
    
    /**
     * Microsoft Azure
     */
    AZURE("Azure", "Microsoft Azure"),
    
    /**
     * OpenStack私有云
     */
    OPENSTACK("OpenStack", "OpenStack私有云"),
    
    /**
     * VMware vSphere
     */
    VMWARE("VMware", "VMware vSphere");

    private final String displayName;
    private final String description;

    PlatformType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据字符串获取平台类型
     * @param type 类型字符串
     * @return 平台类型枚举
     */
    public static PlatformType fromString(String type) {
        if (type == null) {
            return null;
        }
        
        for (PlatformType platformType : PlatformType.values()) {
            if (platformType.name().equalsIgnoreCase(type)) {
                return platformType;
            }
        }
        
        throw new IllegalArgumentException("不支持的平台类型: " + type);
    }

    /**
     * 检查是否为有效的平台类型
     * @param type 类型字符串
     * @return 是否有效
     */
    public static boolean isValid(String type) {
        if (type == null) {
            return false;
        }
        
        for (PlatformType platformType : PlatformType.values()) {
            if (platformType.name().equalsIgnoreCase(type)) {
                return true;
            }
        }
        
        return false;
    }
}
