package com.example.springvueapp.cloudplatform.entity;

/**
 * 云平台连接状态枚举
 * 定义云平台的连接状态
 */
public enum ConnectionStatus {
    
    /**
     * 已连接
     */
    CONNECTED("已连接", "平台连接正常"),
    
    /**
     * 连接断开
     */
    DISCONNECTED("连接断开", "平台连接断开"),
    
    /**
     * 连接中
     */
    CONNECTING("连接中", "正在尝试连接平台"),
    
    /**
     * 连接错误
     */
    ERROR("连接错误", "平台连接出现错误"),
    
    /**
     * 状态未知
     */
    UNKNOWN("状态未知", "平台连接状态未知");

    private final String displayName;
    private final String description;

    ConnectionStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据字符串获取连接状态
     * @param status 状态字符串
     * @return 连接状态枚举
     */
    public static ConnectionStatus fromString(String status) {
        if (status == null) {
            return UNKNOWN;
        }
        
        for (ConnectionStatus connectionStatus : ConnectionStatus.values()) {
            if (connectionStatus.name().equalsIgnoreCase(status)) {
                return connectionStatus;
            }
        }
        
        return UNKNOWN;
    }

    /**
     * 检查是否为有效的连接状态
     * @param status 状态字符串
     * @return 是否有效
     */
    public static boolean isValid(String status) {
        if (status == null) {
            return false;
        }
        
        for (ConnectionStatus connectionStatus : ConnectionStatus.values()) {
            if (connectionStatus.name().equalsIgnoreCase(status)) {
                return true;
            }
        }
        
        return false;
    }
}
