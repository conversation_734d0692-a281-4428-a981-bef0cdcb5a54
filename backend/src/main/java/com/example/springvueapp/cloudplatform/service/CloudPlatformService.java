package com.example.springvueapp.cloudplatform.service;

import com.example.springvueapp.cloudplatform.entity.CloudPlatform;
import com.example.springvueapp.cloudplatform.entity.PlatformType;
import com.example.springvueapp.cloudplatform.mapper.CloudPlatformMapper;
import com.example.springvueapp.cloudplatform.model.*;
import com.example.springvueapp.cloudplatform.repository.CloudPlatformRepository;
import com.example.springvueapp.common.model.PageResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 云平台管理服务层
 * 提供云平台的业务逻辑处理，包括CRUD操作、连接测试等功能
 */
@Service
@Transactional()
public class CloudPlatformService {

    private static final Logger logger = LoggerFactory.getLogger(CloudPlatformService.class);

    private final CloudPlatformRepository cloudPlatformRepository;
    private final CloudPlatformMapper cloudPlatformMapper;
    private final CloudPlatformConnectionService connectionService;

    public CloudPlatformService(CloudPlatformRepository cloudPlatformRepository,
                               CloudPlatformMapper cloudPlatformMapper,
                               CloudPlatformConnectionService connectionService) {
        this.cloudPlatformRepository = cloudPlatformRepository;
        this.cloudPlatformMapper = cloudPlatformMapper;
        this.connectionService = connectionService;
    }

    /**
     * 分页查询云平台列表
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @param name 平台名称（可选，支持模糊匹配）
     * @param type 平台类型（可选）
     * @param status 连接状态（可选）
     * @param region 区域（可选）
     * @return 分页结果
     */
    public Mono<PageResult<CloudPlatformDTO>> findCloudPlatforms(Long userId, int page, int size,
                                                                String name, String type, String status, String region) {
        logger.debug("查询云平台列表: userId={}, page={}, size={}, name={}, type={}, status={}, region={}",
                userId, page, size, name, type, status, region);

        Pageable pageable = PageRequest.of(page, size);

        // 同时查询数据和总数
        Mono<List<CloudPlatformDTO>> contentMono = cloudPlatformRepository
                .findByConditions(userId, name, type, status, region, pageable)
                .map(cloudPlatformMapper::toDTOWithoutSecret) // 列表查询不返回敏感信息
                .collectList();

        Mono<Long> totalMono = cloudPlatformRepository
                .countByConditions(userId, name, type, status, region);

        return Mono.zip(contentMono, totalMono)
                .map(tuple -> {
                    List<CloudPlatformDTO> content = tuple.getT1();
                    Long total = tuple.getT2();
                    return new PageResult<>(content, page, size, total);
                });
    }

    /**
     * 根据ID查询云平台详情
     * @param userId 用户ID
     * @param id 云平台ID
     * @return 云平台详情
     */
    public Mono<CloudPlatformDTO> findCloudPlatformById(Long userId, Long id) {
        logger.debug("查询云平台详情: userId={}, id={}", userId, id);

        return cloudPlatformRepository.findByUserIdAndId(userId, id)
                .map(cloudPlatformMapper::toDTO)
                .doOnNext(dto -> logger.debug("找到云平台: {}", dto))
                .switchIfEmpty(Mono.error(new IllegalArgumentException("云平台不存在或无权限访问")));
    }

    /**
     * 创建云平台
     * @param userId 用户ID
     * @param request 创建请求
     * @param createdBy 创建人
     * @return 创建的云平台信息
     */
    public Mono<CloudPlatformDTO> createCloudPlatform(Long userId, CreateCloudPlatformRequest request, String createdBy) {
        logger.debug("创建云平台: userId={}, request={}, createdBy={}", userId, request, createdBy);

        // 验证平台类型
        if (!PlatformType.isValid(request.getType())) {
            return Mono.error(new IllegalArgumentException("不支持的平台类型: " + request.getType()));
        }

        // 检查名称是否已存在
        return cloudPlatformRepository.existsByUserIdAndNameAndIdNot(userId, request.getName(), null)
                .flatMap(exists -> {
                    if (exists) {
                        return Mono.error(new IllegalArgumentException("平台名称已存在: " + request.getName()));
                    }

                    // 创建实体
                    CloudPlatform entity = cloudPlatformMapper.toEntity(request, userId, createdBy);
                    
                    // 保存到数据库
                    return cloudPlatformRepository.save(entity)
                            .map(cloudPlatformMapper::toDTO)
                            .doOnNext(dto -> logger.info("云平台创建成功: {}", dto));
                });
    }

    /**
     * 更新云平台
     * @param userId 用户ID
     * @param id 云平台ID
     * @param request 更新请求
     * @param updatedBy 更新人
     * @return 更新后的云平台信息
     */
    public Mono<CloudPlatformDTO> updateCloudPlatform(Long userId, Long id, UpdateCloudPlatformRequest request, String updatedBy) {
        logger.debug("更新云平台: userId={}, id={}, request={}, updatedBy={}", userId, id, request, updatedBy);

        return cloudPlatformRepository.findByUserIdAndId(userId, id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("云平台不存在或无权限访问")))
                .flatMap(entity -> {
                    // 如果更新了名称，检查是否与其他平台重名
                    if (request.getName() != null && !request.getName().equals(entity.getName())) {
                        return cloudPlatformRepository.existsByUserIdAndNameAndIdNot(userId, request.getName(), id)
                                .flatMap(exists -> {
                                    if (exists) {
                                        return Mono.error(new IllegalArgumentException("平台名称已存在: " + request.getName()));
                                    }
                                    return Mono.just(entity);
                                });
                    }
                    return Mono.just(entity);
                })
                .flatMap(entity -> {
                    // 验证平台类型（如果有更新）
                    if (request.getType() != null && !PlatformType.isValid(request.getType())) {
                        return Mono.error(new IllegalArgumentException("不支持的平台类型: " + request.getType()));
                    }

                    // 更新实体
                    CloudPlatform updatedEntity = cloudPlatformMapper.updateEntity(entity, request, updatedBy);
                    
                    // 保存到数据库
                    return cloudPlatformRepository.save(updatedEntity)
                            .map(cloudPlatformMapper::toDTO)
                            .doOnNext(dto -> logger.info("云平台更新成功: {}", dto));
                });
    }

    /**
     * 删除云平台
     * @param userId 用户ID
     * @param id 云平台ID
     * @return 删除结果
     */
    public Mono<Void> deleteCloudPlatform(Long userId, Long id) {
        logger.debug("删除云平台: userId={}, id={}", userId, id);

        return cloudPlatformRepository.findByUserIdAndId(userId, id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("云平台不存在或无权限访问")))
                .flatMap(entity -> {
                    logger.info("删除云平台: {}", entity.getName());
                    return cloudPlatformRepository.delete(entity);
                });
    }

    /**
     * 测试云平台连接
     * @param userId 用户ID
     * @param id 云平台ID
     * @return 连接测试结果
     */
    public Mono<ConnectionTestResult> testConnection(Long userId, Long id) {
        logger.debug("测试云平台连接: userId={}, id={}", userId, id);

        return cloudPlatformRepository.findByUserIdAndId(userId, id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("云平台不存在或无权限访问")))
                .flatMap(entity -> {
                    // 执行连接测试
                    return connectionService.testConnection(entity)
                            .flatMap(result -> {
                                // 更新连接状态
                                entity.setStatus(result.getStatus());
                                return cloudPlatformRepository.save(entity)
                                        .thenReturn(result);
                            });
                })
                .doOnNext(result -> logger.debug("连接测试完成: {}", result));
    }

    /**
     * 获取支持的平台类型列表
     * @return 平台类型列表
     */
    public Flux<PlatformTypeDTO> getSupportedPlatformTypes() {
        logger.debug("获取支持的平台类型列表");

        return Flux.fromIterable(Arrays.asList(PlatformType.values()))
                .map(this::convertToPlatformTypeDTO);
    }

    /**
     * 转换平台类型枚举为DTO
     */
    private PlatformTypeDTO convertToPlatformTypeDTO(PlatformType platformType) {
        PlatformTypeDTO dto = new PlatformTypeDTO(
                platformType.name(),
                platformType.getDisplayName(),
                platformType.getDescription()
        );

        // 根据平台类型设置默认端口和配置示例
        switch (platformType) {
            case KUBERNETES:
            case K3S:
                dto.setDefaultPort(6443);
                dto.setConfigExample("https://k8s.example.com:6443");
                break;
            case DOCKER:
                dto.setDefaultPort(2376);
                dto.setConfigExample("tcp://docker.example.com:2376");
                break;
            case DOCKER_SWARM:
                dto.setDefaultPort(2377);
                dto.setConfigExample("tcp://swarm.example.com:2377");
                break;
            default:
                dto.setSupportConnectionTest(false); // 云服务商暂不支持简单连接测试
                break;
        }

        return dto;
    }

    /**
     * 获取云平台统计信息
     * @param userId 用户ID
     * @return 统计信息
     */
    public Mono<Map<String, Object>> getStatistics(Long userId) {
        logger.debug("获取云平台统计信息: userId={}", userId);

        Mono<Long> totalMono = cloudPlatformRepository.countByConditions(userId, null, null, null, null);
        
        Mono<Map<String, Long>> statusStatsMono = cloudPlatformRepository.countByUserIdGroupByStatus(userId)
                .collectMap(
                        PlatformStats::getStatus,
                        PlatformStats::getCount
                );

        Mono<Map<String, Long>> typeStatsMono = cloudPlatformRepository.countByUserIdGroupByType(userId)
                .collectMap(
                        PlatformStats::getType,
                        PlatformStats::getCount
                );

        return Mono.zip(totalMono, statusStatsMono, typeStatsMono)
                .map(tuple -> {
                    Map<String, Object> stats = new HashMap<>();
                    stats.put("total", tuple.getT1());
                    stats.put("statusStats", tuple.getT2());
                    stats.put("typeStats", tuple.getT3());
                    return stats;
                });
    }
}
