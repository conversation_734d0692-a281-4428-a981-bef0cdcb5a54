package com.example.springvueapp.cloudplatform.model;

/**
 * 平台类型数据传输对象
 * 用于返回支持的平台类型列表
 */
public class PlatformTypeDTO {

    /**
     * 类型代码
     */
    private String code;

    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 类型描述
     */
    private String description;

    /**
     * 是否支持连接测试
     */
    private boolean supportConnectionTest;

    /**
     * 默认端口
     */
    private Integer defaultPort;

    /**
     * 配置示例
     */
    private String configExample;

    // 构造函数
    public PlatformTypeDTO() {
    }

    public PlatformTypeDTO(String code, String displayName, String description) {
        this.code = code;
        this.displayName = displayName;
        this.description = description;
        this.supportConnectionTest = true; // 默认支持连接测试
    }

    public PlatformTypeDTO(String code, String displayName, String description, 
                          boolean supportConnectionTest, Integer defaultPort, String configExample) {
        this.code = code;
        this.displayName = displayName;
        this.description = description;
        this.supportConnectionTest = supportConnectionTest;
        this.defaultPort = defaultPort;
        this.configExample = configExample;
    }

    // Getter 和 Setter 方法
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isSupportConnectionTest() {
        return supportConnectionTest;
    }

    public void setSupportConnectionTest(boolean supportConnectionTest) {
        this.supportConnectionTest = supportConnectionTest;
    }

    public Integer getDefaultPort() {
        return defaultPort;
    }

    public void setDefaultPort(Integer defaultPort) {
        this.defaultPort = defaultPort;
    }

    public String getConfigExample() {
        return configExample;
    }

    public void setConfigExample(String configExample) {
        this.configExample = configExample;
    }

    @Override
    public String toString() {
        return "PlatformTypeDTO{" +
                "code='" + code + '\'' +
                ", displayName='" + displayName + '\'' +
                ", description='" + description + '\'' +
                ", supportConnectionTest=" + supportConnectionTest +
                ", defaultPort=" + defaultPort +
                ", configExample='" + configExample + '\'' +
                '}';
    }
}
