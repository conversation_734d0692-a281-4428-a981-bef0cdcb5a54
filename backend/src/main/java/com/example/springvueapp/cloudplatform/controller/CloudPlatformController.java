package com.example.springvueapp.cloudplatform.controller;

import com.example.springvueapp.cloudplatform.model.*;
import com.example.springvueapp.cloudplatform.service.CloudPlatformService;
import com.example.springvueapp.common.model.ApiResponse;
import com.example.springvueapp.common.model.PageResponse;
import com.example.springvueapp.common.auth.AuthUtils;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.core.Authentication;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * 云平台管理控制器
 * 提供云平台管理的RESTful API接口
 */
@RestController
@RequestMapping("/api/cloud-platforms")
@CrossOrigin(origins = "*")
public class CloudPlatformController {

    private static final Logger logger = LoggerFactory.getLogger(CloudPlatformController.class);

    private final CloudPlatformService cloudPlatformService;
    private final AuthUtils authUtils;

    public CloudPlatformController(CloudPlatformService cloudPlatformService, AuthUtils authUtils) {
        this.cloudPlatformService = cloudPlatformService;
        this.authUtils = authUtils;
    }

    /**
     * 创建云平台
     * @param request 创建请求
     * @param authentication 认证信息
     * @return 创建结果
     */
    @PostMapping
    public Mono<ApiResponse<CloudPlatformDTO>> createCloudPlatform(
            @Valid @RequestBody CreateCloudPlatformRequest request,
            Authentication authentication) {

        logger.info("创建云平台请求: name={}, type={}", request.getName(), request.getType());

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cloudPlatformService.createCloudPlatform(userId, request, authentication.getName())
                .map(platform -> ApiResponse.success("云平台创建成功", platform))
                .onErrorResume(throwable -> {
                    logger.error("创建云平台失败", throwable);
                    return Mono.just(ApiResponse.error("创建云平台失败: " + throwable.getMessage()));
                });
    }

    /**
     * 更新云平台
     * @param id 平台ID
     * @param request 更新请求
     * @param authentication 认证信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    public Mono<ApiResponse<CloudPlatformDTO>> updateCloudPlatform(
            @PathVariable Long id,
            @Valid @RequestBody UpdateCloudPlatformRequest request,
            Authentication authentication) {

        logger.info("更新云平台请求: id={}, name={}", id, request.getName());

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cloudPlatformService.updateCloudPlatform(userId, id, request, authentication.getName())
                .map(platform -> ApiResponse.success("云平台更新成功", platform))
                .onErrorResume(throwable -> {
                    logger.error("更新云平台失败: id={}, error={}", id, throwable.getMessage());
                    logger.error("", throwable);
                    return Mono.just(ApiResponse.error("更新云平台失败: " + throwable.getMessage()));
                });
    }

    /**
     * 删除云平台
     * @param id 平台ID
     * @param authentication 认证信息
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Mono<ApiResponse<Object>> deleteCloudPlatform(
            @PathVariable Long id,
            Authentication authentication) {
        
        logger.info("删除云平台请求: id={}", id);

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cloudPlatformService.deleteCloudPlatform(userId, id)
                .then(Mono.just(ApiResponse.success("云平台删除成功")))
                .onErrorResume(throwable -> {
                    logger.error("删除云平台失败: id={}, error={}", id, throwable.getMessage());
                    logger.error("", throwable);
                    return Mono.just(ApiResponse.error("删除云平台失败: " + throwable.getMessage()));
                });
    }

    /**
     * 获取云平台详情
     * @param id 平台ID
     * @param authentication 认证信息
     * @return 云平台详情
     */
    @GetMapping("/{id}")
    public Mono<ApiResponse<CloudPlatformDTO>> getCloudPlatform(
            @PathVariable Long id,
            Authentication authentication) {

        logger.debug("获取云平台详情: id={}", id);

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cloudPlatformService.findCloudPlatformById(userId, id)
                .map(platform -> ApiResponse.success("获取云平台详情成功", platform))
                .onErrorResume(throwable -> {
                    logger.error("获取云平台详情失败: id={}, error={}", id, throwable.getMessage());
                    logger.error("", throwable);
                    return Mono.just(ApiResponse.error("获取云平台详情失败: " + throwable.getMessage()));
                });
    }

    /**
     * 分页查询云平台列表
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param type 平台类型（可选）
     * @param status 连接状态（可选）
     * @param region 区域（可选）
     * @param name 名称关键词（可选）
     * @param authentication 认证信息
     * @return 云平台列表
     */
    @GetMapping
    public Mono<ApiResponse<PageResponse<CloudPlatformDTO>>> getCloudPlatformList(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String region,
            @RequestParam(required = false) String name,
            Authentication authentication) {

        logger.debug("查询云平台列表: page={}, size={}, type={}, status={}, region={}, name={}",
                    page, size, type, status, region, name);

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cloudPlatformService.findCloudPlatforms(userId, page, size, name, type, status, region)
                .map(pageResult -> {
                    PageResponse<CloudPlatformDTO> pageResponse = new PageResponse<>(
                            pageResult.getContent(), // content
                            pageResult.getPage(),     // page
                            pageResult.getSize(),     // size
                            pageResult.getTotal()     // total
                    );
                    return ApiResponse.success("查询云平台列表成功", pageResponse);
                })
                .onErrorResume(throwable -> {
                    logger.error("查询云平台列表失败", throwable);
                    return Mono.just(ApiResponse.error("查询云平台列表失败: " + throwable.getMessage()));
                });
    }

    /**
     * 测试云平台连接
     * @param id 平台ID
     * @param authentication 认证信息
     * @return 连接测试结果
     */
    @PostMapping("/{id}/test-connection")
    public Mono<ApiResponse<ConnectionTestResult>> testConnection(
            @PathVariable Long id,
            Authentication authentication) {
        
        logger.info("测试云平台连接: id={}", id);

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cloudPlatformService.testConnection(id, userId)
                .map(result -> ApiResponse.success("连接测试完成", result))
                .onErrorResume(throwable -> {
                    logger.error("连接测试失败: id={}, error={}", id, throwable.getMessage());
                    logger.error("", throwable);
                    return Mono.just(ApiResponse.error("连接测试失败: " + throwable.getMessage()));
                });
    }

    /**
     * 获取支持的平台类型列表
     * @return 平台类型列表
     */
    @GetMapping("/types")
    public Mono<ApiResponse<java.util.List<PlatformTypeDTO>>> getSupportedPlatformTypes() {
        logger.debug("获取支持的平台类型列表");

        return cloudPlatformService.getSupportedPlatformTypes()
                .collectList()
                .map(types -> ApiResponse.success("获取平台类型列表成功", types))
                .onErrorResume(throwable -> {
                    logger.error("获取平台类型列表失败", throwable);
                    return Mono.just(ApiResponse.error("获取平台类型列表失败: " + throwable.getMessage()));
                });
    }

    /**
     * 获取云平台统计信息
     * @param authentication 认证信息
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public Mono<ApiResponse<Map<String, Object>>> getStatistics(Authentication authentication) {
        logger.debug("获取云平台统计信息");

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cloudPlatformService.getStatistics(userId)
                .map(statistics -> ApiResponse.success("获取统计信息成功", statistics))
                .onErrorResume(throwable -> {
                    logger.error("获取统计信息失败", throwable);
                    return Mono.just(ApiResponse.error("获取统计信息失败: " + throwable.getMessage()));
                });
    }

    /**
     * 批量删除云平台
     * @param ids 平台ID列表
     * @param authentication 认证信息
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    public Mono<ApiResponse<Object>> batchDeleteCloudPlatforms(
            @RequestBody java.util.List<Long> ids,
            Authentication authentication) {
        
        logger.info("批量删除云平台: ids={}", ids);

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return Flux.fromIterable(ids)
                .flatMap(id -> cloudPlatformService.deleteCloudPlatform(id, userId))
                .then(Mono.just(ApiResponse.success("批量删除云平台成功")))
                .onErrorResume(throwable -> {
                    logger.error("批量删除云平台失败:", throwable);
                    return Mono.just(ApiResponse.error("批量删除云平台失败: " + throwable.getMessage()));
                });
    }
}