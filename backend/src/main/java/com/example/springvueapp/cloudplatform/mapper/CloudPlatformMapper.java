package com.example.springvueapp.cloudplatform.mapper;

import com.example.springvueapp.cloudplatform.entity.CloudPlatform;
import com.example.springvueapp.cloudplatform.model.CloudPlatformDTO;
import com.example.springvueapp.cloudplatform.model.CreateCloudPlatformRequest;
import com.example.springvueapp.cloudplatform.model.UpdateCloudPlatformRequest;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 云平台实体映射器
 * 负责实体类与DTO之间的转换，以及JSON字符串与Map对象的转换
 */
@Component
public class CloudPlatformMapper {

    private static final Logger logger = LoggerFactory.getLogger(CloudPlatformMapper.class);
    
    private final ObjectMapper objectMapper;

    public CloudPlatformMapper(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 将实体转换为DTO
     * @param entity 云平台实体
     * @return 云平台DTO
     */
    public CloudPlatformDTO toDTO(CloudPlatform entity) {
        if (entity == null) {
            return null;
        }

        CloudPlatformDTO dto = new CloudPlatformDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setType(entity.getType());
        dto.setUrl(entity.getUrl());
        // 注意：在某些场景下可能不需要返回敏感的secret信息
        dto.setSecret(entity.getSecret());
        dto.setDescription(entity.getDescription());
        dto.setStatus(entity.getStatus());
        dto.setRegion(entity.getRegion());
        dto.setVersion(entity.getVersion());
        dto.setTags(parseTagsFromJson(entity.getTags()));
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedBy(entity.getUpdatedBy());
        dto.setUpdatedAt(entity.getUpdatedAt());

        return dto;
    }

    /**
     * 将实体转换为DTO（不包含敏感信息）
     * @param entity 云平台实体
     * @return 云平台DTO（不包含secret）
     */
    public CloudPlatformDTO toDTOWithoutSecret(CloudPlatform entity) {
        CloudPlatformDTO dto = toDTO(entity);
        if (dto != null) {
            dto.setSecret(null); // 移除敏感信息
        }
        return dto;
    }

    /**
     * 将创建请求转换为实体
     * @param request 创建请求
     * @param userId 用户ID
     * @param createdBy 创建人
     * @return 云平台实体
     */
    public CloudPlatform toEntity(CreateCloudPlatformRequest request, Long userId, String createdBy) {
        if (request == null) {
            return null;
        }

        CloudPlatform entity = new CloudPlatform();
        entity.setName(request.getName());
        entity.setType(request.getType());
        entity.setUrl(request.getUrl());
        entity.setSecret(request.getSecret());
        entity.setDescription(request.getDescription());
        entity.setStatus("UNKNOWN"); // 初始状态为未知
        entity.setRegion(request.getRegion());
        entity.setVersion(request.getVersion());
        entity.setTags(convertTagsToJson(request.getTags()));
        entity.setCreatedBy(createdBy);
        entity.setUserId(userId);
        entity.setCreatedAt(LocalDateTime.now());
        entity.setUpdatedAt(LocalDateTime.now());

        return entity;
    }

    /**
     * 将更新请求应用到现有实体
     * @param entity 现有实体
     * @param request 更新请求
     * @param updatedBy 更新人
     * @return 更新后的实体
     */
    public CloudPlatform updateEntity(CloudPlatform entity, UpdateCloudPlatformRequest request, String updatedBy) {
        if (entity == null || request == null) {
            return entity;
        }

        // 只更新非空字段
        if (request.getName() != null) {
            entity.setName(request.getName());
        }
        if (request.getType() != null) {
            entity.setType(request.getType());
        }
        if (request.getUrl() != null) {
            entity.setUrl(request.getUrl());
        }
        if (request.getSecret() != null) {
            entity.setSecret(request.getSecret());
        }
        if (request.getDescription() != null) {
            entity.setDescription(request.getDescription());
        }
        if (request.getRegion() != null) {
            entity.setRegion(request.getRegion());
        }
        if (request.getVersion() != null) {
            entity.setVersion(request.getVersion());
        }
        if (request.getTags() != null) {
            entity.setTags(convertTagsToJson(request.getTags()));
        }

        entity.setUpdatedBy(updatedBy);
        entity.setUpdatedAt(LocalDateTime.now());

        return entity;
    }

    /**
     * 将JSON字符串转换为Map对象
     * @param json JSON字符串
     * @return Map对象
     */
    private Map<String, String> parseTagsFromJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return new HashMap<>();
        }

        try {
            return objectMapper.readValue(json, new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            logger.warn("解析标签JSON失败: {}", json, e);
            return new HashMap<>();
        }
    }

    /**
     * 将Map对象转换为JSON字符串
     * @param tags Map对象
     * @return JSON字符串
     */
    private String convertTagsToJson(Map<String, String> tags) {
        if (tags == null || tags.isEmpty()) {
            return null;
        }

        try {
            return objectMapper.writeValueAsString(tags);
        } catch (JsonProcessingException e) {
            logger.warn("转换标签为JSON失败: {}", tags, e);
            return null;
        }
    }
}
