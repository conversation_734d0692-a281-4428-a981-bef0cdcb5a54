package com.example.springvueapp.devops.mapper;

import com.example.springvueapp.devops.entity.DevOpsApplicationEntity;
import com.example.springvueapp.devops.model.DevOpsApplication;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DevOps应用实体类和DTO之间的映射器
 */
@Component
public class DevOpsApplicationMapper {

    /**
     * 将实体转换为DTO
     * @param entity 应用实体
     * @return 应用DTO
     */
    public DevOpsApplication toDto(DevOpsApplicationEntity entity) {
        if (entity == null) {
            return null;
        }

        return DevOpsApplication.builder()
                .id(entity.getId())
                .name(entity.getName())
                .description(entity.getDescription())
                .status(entity.getStatus())
                .userId(entity.getUserId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }

    /**
     * 将实体转换为DTO，并填充关联的组件ID列表
     * @param entity 应用实体
     * @param componentIds 关联的组件ID列表
     * @return 应用DTO
     */
    public DevOpsApplication toDto(DevOpsApplicationEntity entity, List<Long> componentIds) {
        if (entity == null) {
            return null;
        }

        return DevOpsApplication.builder()
                .id(entity.getId())
                .name(entity.getName())
                .description(entity.getDescription())
                .status(entity.getStatus())
                .userId(entity.getUserId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .components(componentIds)
                .build();
    }

    /**
     * 将DTO转换为实体
     * @param dto 应用DTO
     * @return 应用实体
     */
    public DevOpsApplicationEntity toEntity(DevOpsApplication dto) {
        if (dto == null) {
            return null;
        }

        return DevOpsApplicationEntity.builder()
                .id(dto.getId())
                .name(dto.getName())
                .description(dto.getDescription())
                .status(dto.getStatus())
                .userId(dto.getUserId())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为新的实体对象
     * @param application 应用DTO
     * @param userId 用户ID
     * @return 应用实体
     */
    public DevOpsApplicationEntity toNewEntity(DevOpsApplication application, Long userId) {
        if (application == null) {
            return null;
        }

        return DevOpsApplicationEntity.builder()
                .name(application.getName())
                .description(application.getDescription())
                .status(application.getStatus())
                .userId(userId)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    /**
     * 将DTO转换为更新的实体对象
     * @param application 应用DTO
     * @param existingEntity 现有的实体对象
     * @return 更新后的应用实体
     */
    public DevOpsApplicationEntity toUpdateEntity(DevOpsApplication application, DevOpsApplicationEntity existingEntity) {
        if (application == null || existingEntity == null) {
            return null;
        }

        return DevOpsApplicationEntity.builder()
                .id(existingEntity.getId())
                .name(application.getName())
                .description(application.getDescription())
                .status(application.getStatus())
                .userId(existingEntity.getUserId())
                .createdAt(existingEntity.getCreatedAt())
                .updatedAt(LocalDateTime.now())
                .build();
    }

}