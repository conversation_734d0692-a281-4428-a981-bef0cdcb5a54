package com.example.springvueapp.devops.mapper;

import com.example.springvueapp.devops.entity.DevOpsComponentEntity;
import com.example.springvueapp.devops.model.DevOpsComponent;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * DevOps组件实体类和DTO之间的映射器
 */
@Component
public class DevOpsComponentMapper {

    /**
     * 将DTO转换为新的实体对象
     * @param component 组件DTO
     * @param userId 用户ID
     * @return 组件实体
     */
    public DevOpsComponentEntity toNewEntity(DevOpsComponent component, Long userId) {
        return DevOpsComponentEntity.builder()
                .name(component.getName())
                .description(component.getDescription())
                .repositoryUrl(component.getRepositoryUrl())
                .repositoryType(component.getRepositoryType())
                .status(component.getStatus())
                .userId(userId)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    /**
     * 将DTO转换为更新的实体对象
     * @param component 组件DTO
     * @param existingEntity 现有的实体对象
     * @return 更新后的组件实体
     */
    public DevOpsComponentEntity toUpdateEntity(DevOpsComponent component, DevOpsComponentEntity existingEntity) {
        return DevOpsComponentEntity.builder()
                .id(existingEntity.getId())
                .name(component.getName())
                .description(component.getDescription())
                .repositoryUrl(component.getRepositoryUrl())
                .repositoryType(component.getRepositoryType())
                .status(component.getStatus())
                .userId(existingEntity.getUserId())
                .createdAt(existingEntity.getCreatedAt())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    /**
     * 将实体对象转换为DTO
     * @param entity 组件实体
     * @return 组件DTO
     */
    public DevOpsComponent toDto(DevOpsComponentEntity entity) {
        DevOpsComponent component = new DevOpsComponent();
        component.setId(entity.getId());
        component.setName(entity.getName());
        component.setDescription(entity.getDescription());
        component.setRepositoryUrl(entity.getRepositoryUrl());
        component.setRepositoryType(entity.getRepositoryType());
        component.setStatus(entity.getStatus());
        component.setUserId(entity.getUserId());
        component.setCreatedAt(entity.getCreatedAt());
        component.setUpdatedAt(entity.getUpdatedAt());
        return component;
    }
}
