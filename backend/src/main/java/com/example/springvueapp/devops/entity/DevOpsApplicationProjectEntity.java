package com.example.springvueapp.devops.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 应用和项目关联关系实体类
 * 对应devops_application_projects表
 */
@Table("devops_application_projects")
public class DevOpsApplicationProjectEntity {

    @Id
    private Long id;
    
    private Long applicationId;
    
    private Long projectId;

    public DevOpsApplicationProjectEntity() {
    }

    public DevOpsApplicationProjectEntity(Long id, Long applicationId, Long projectId) {
        this.id = id;
        this.applicationId = applicationId;
        this.projectId = projectId;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    @Override
    public String toString() {
        return "DevOpsApplicationProjectEntity{" +
                "id=" + id +
                ", applicationId=" + applicationId +
                ", projectId=" + projectId +
                '}';
    }
}