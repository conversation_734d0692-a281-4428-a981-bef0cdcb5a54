package com.example.springvueapp.devops.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 组件和应用关联关系实体类
 * 对应devops_component_applications表
 */
@Table("devops_component_applications")
public class DevOpsComponentApplicationEntity {

    @Id
    private Long id;
    
    private Long componentId;
    
    private Long applicationId;

    public DevOpsComponentApplicationEntity() {
    }

    public DevOpsComponentApplicationEntity(Long id, Long componentId, Long applicationId) {
        this.id = id;
        this.componentId = componentId;
        this.applicationId = applicationId;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getComponentId() {
        return componentId;
    }

    public void setComponentId(Long componentId) {
        this.componentId = componentId;
    }

    public Long getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    @Override
    public String toString() {
        return "DevOpsComponentApplicationEntity{" +
                "id=" + id +
                ", componentId=" + componentId +
                ", applicationId=" + applicationId +
                '}';
    }
}