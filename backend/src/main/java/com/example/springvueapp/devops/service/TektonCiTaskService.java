package com.example.springvueapp.devops.service;

import com.example.springvueapp.devops.entity.DevOpsCiTaskEntity;
import com.example.springvueapp.devops.entity.DevOpsCiTaskInstanceEntity;
import com.example.springvueapp.devops.mapper.DevOpsCiTaskInstanceMapper;
import com.example.springvueapp.devops.mapper.DevOpsCiTaskMapper;
import com.example.springvueapp.devops.model.DevOpsCiTask;
import com.example.springvueapp.devops.model.DevOpsCiTaskInstance;
import com.example.springvueapp.devops.repository.DevOpsCiTaskInstanceRepository;
import com.example.springvueapp.devops.repository.DevOpsCiTaskRepository;
import com.example.springvueapp.devops.repository.DevOpsComponentRepository;
import com.example.springvueapp.devops.util.TektonYamlConfigBuilder;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Tekton CI任务服务实现
 * 实现CiTaskService抽象接口，专注于CI任务的YAML配置生成和管理
 * 不包含任务执行逻辑，任务执行由TektonExecutorService处理
 */
@Service
public class TektonCiTaskService implements CiTaskService {

    private static final Logger logger = LoggerFactory.getLogger(TektonCiTaskService.class);

    private final DevOpsCiTaskRepository ciTaskRepository;
    private final DevOpsCiTaskInstanceRepository ciTaskInstanceRepository;
    private final DevOpsComponentRepository componentRepository;
    private final DevOpsCiTaskMapper ciTaskMapper;
    private final DevOpsCiTaskInstanceMapper ciTaskInstanceMapper;
    private final TektonExecutorService tektonExecutorService;
    private final TektonYamlConfigBuilder yamlConfigBuilder;

    @Autowired
    public TektonCiTaskService(DevOpsCiTaskRepository ciTaskRepository,
                              DevOpsCiTaskInstanceRepository ciTaskInstanceRepository,
                              DevOpsComponentRepository componentRepository,
                              DevOpsCiTaskMapper ciTaskMapper,
                              DevOpsCiTaskInstanceMapper ciTaskInstanceMapper,
                              TektonExecutorService tektonExecutorService,
                              TektonYamlConfigBuilder yamlConfigBuilder) {
        this.ciTaskRepository = ciTaskRepository;
        this.ciTaskInstanceRepository = ciTaskInstanceRepository;
        this.componentRepository = componentRepository;
        this.ciTaskMapper = ciTaskMapper;
        this.ciTaskInstanceMapper = ciTaskInstanceMapper;
        this.tektonExecutorService = tektonExecutorService;
        this.yamlConfigBuilder = yamlConfigBuilder;
    }

    @Override
    public Mono<DevOpsCiTask> createCiTask(DevOpsCiTask ciTask, Long componentId, Long userId) {
        // 首先验证组件是否存在且用户有权限
        return componentRepository.findByUserIdAndId(userId, componentId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")))
                .flatMap(component -> 
                    ciTaskRepository.existsByComponentIdAndName(componentId, ciTask.getName())
                            .flatMap(exists -> {
                                if (exists) {
                                    return Mono.error(new IllegalArgumentException("CI任务名称在该组件中已存在"));
                                }
                                
                                // 验证配置
                                return validateConfiguration(ciTask.getConfiguration())
                                        .flatMap(valid -> {
                                            if (!valid) {
                                                return Mono.error(new IllegalArgumentException("CI任务配置无效"));
                                            }
                                            
                                            DevOpsCiTaskEntity entity = ciTaskMapper.toNewEntity(ciTask, componentId, userId);
                                            return ciTaskRepository.save(entity)
                                                    .map(ciTaskMapper::toDto);
                                        });
                            })
                );
    }

    @Override
    public Mono<DevOpsCiTask> updateCiTask(Long taskId, DevOpsCiTask ciTask, Long userId) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")))
                .flatMap(existingEntity -> {
                    // 如果名称发生变化，检查新名称是否已存在
                    if (!existingEntity.getName().equals(ciTask.getName())) {
                        return ciTaskRepository.existsByComponentIdAndName(existingEntity.getComponentId(), ciTask.getName())
                                .flatMap(exists -> {
                                    if (exists) {
                                        return Mono.error(new IllegalArgumentException("CI任务名称在该组件中已存在"));
                                    }
                                    return updateTaskEntity(ciTask, existingEntity);
                                });
                    } else {
                        return updateTaskEntity(ciTask, existingEntity);
                    }
                });
    }

    private Mono<DevOpsCiTask> updateTaskEntity(DevOpsCiTask ciTask, DevOpsCiTaskEntity existingEntity) {
        // 验证配置
        return validateConfiguration(ciTask.getConfiguration())
                .flatMap(valid -> {
                    if (!valid) {
                        return Mono.error(new IllegalArgumentException("CI任务配置无效"));
                    }
                    
                    DevOpsCiTaskEntity updatedEntity = ciTaskMapper.toUpdateEntity(ciTask, existingEntity);
                    return ciTaskRepository.save(updatedEntity)
                            .map(ciTaskMapper::toDto);
                });
    }

    @Override
    public Mono<Boolean> deleteCiTask(Long taskId, Long userId) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")))
                .flatMap(entity -> ciTaskRepository.deleteByUserIdAndId(userId, taskId))
                .map(deletedCount -> deletedCount > 0);
    }

    @Override
    public Mono<DevOpsCiTask> getCiTaskById(Long taskId, Long userId) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .map(ciTaskMapper::toDto)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")));
    }

    @Override
    public Flux<DevOpsCiTask> getCiTasksByComponent(Long componentId, Long userId) {
        // 首先验证组件是否存在且用户有权限
        return componentRepository.findByUserIdAndId(userId, componentId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")))
                .flatMapMany(component -> 
                    ciTaskRepository.findByUserIdAndComponentId(userId, componentId)
                            .map(ciTaskMapper::toDto)
                );
    }

    @Override
    public Flux<DevOpsCiTask> getAllCiTasks(Long userId) {
        return ciTaskRepository.findByUserId(userId)
                .map(ciTaskMapper::toDto);
    }

    @Override
    public Mono<DevOpsCiTaskInstance> startCiTask(Long taskId, Long userId, Map<String, Object> parameters) {
        // 检查参数中是否指定了云平台
        Long cloudPlatformId = null;
        if (parameters != null && parameters.containsKey("cloudPlatformId")) {
            try {
                cloudPlatformId = Long.valueOf(parameters.get("cloudPlatformId").toString());
            } catch (NumberFormatException e) {
                logger.warn("无效的云平台ID: {}", parameters.get("cloudPlatformId"));
            }
        }

        // 如果指定了云平台，先切换到该平台
        Mono<Boolean> platformSetup = cloudPlatformId != null ?
            tektonExecutorService.setCloudPlatform(userId, cloudPlatformId) :
            Mono.just(true);

        return platformSetup
                .flatMap(setupSuccess -> {
                    if (!setupSuccess) {
                        return Mono.error(new RuntimeException("云平台连接设置失败"));
                    }
                    return ciTaskRepository.findByUserIdAndId(userId, taskId);
                })
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")))
                .flatMap(ciTaskEntity -> {
                    // 生成唯一的实例ID
                    String instanceId = generateInstanceId(ciTaskEntity.getName());
                    
                    // 创建任务实例
                    DevOpsCiTaskInstance instance = DevOpsCiTaskInstance.builder()
                            .ciTaskId(taskId)
                            .instanceId(instanceId)
                            .status("PENDING")
                            .startTime(LocalDateTime.now())
                            .resultData(parameters != null ? parameters : new HashMap<>())
                            .build();
                    
                    DevOpsCiTaskInstanceEntity instanceEntity = ciTaskInstanceMapper.toNewEntity(instance, taskId, userId);
                    
                    return ciTaskInstanceRepository.save(instanceEntity)
                            .map(ciTaskInstanceMapper::toDto)
                            .flatMap(savedInstance -> {
                                // 调用Tekton API启动任务
                                return submitTektonTask(ciTaskEntity, savedInstance);
                            });
                });
    }

    @Override
    public Mono<Boolean> stopCiTaskInstance(String instanceId, Long userId) {
        return ciTaskInstanceRepository.findByUserIdAndInstanceId(userId, instanceId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务实例不存在或无权限访问")))
                .flatMap(instanceEntity ->
                    // 通过TektonExecutorService停止任务
                    tektonExecutorService.stopTask(instanceId)
                            .flatMap(stopped -> {
                                if (stopped) {
                                    // 只更新数据库中的元数据，运行时状态从Tekton获取
                                    instanceEntity.setUpdatedAt(LocalDateTime.now());
                                    return ciTaskInstanceRepository.save(instanceEntity)
                                            .map(saved -> true);
                                } else {
                                    return Mono.error(new RuntimeException("停止Tekton任务失败"));
                                }
                            })
                );
    }

    @Override
    public Mono<Boolean> cancelCiTaskInstance(String instanceId, Long userId) {
        return ciTaskInstanceRepository.findByUserIdAndInstanceId(userId, instanceId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务实例不存在或无权限访问")))
                .flatMap(instanceEntity ->
                    // 通过TektonExecutorService取消任务
                    tektonExecutorService.cancelTask(instanceId)
                            .flatMap(cancelled -> {
                                if (cancelled) {
                                    // 只更新数据库中的元数据，运行时状态从Tekton获取
                                    instanceEntity.setUpdatedAt(LocalDateTime.now());
                                    return ciTaskInstanceRepository.save(instanceEntity)
                                            .map(saved -> true);
                                } else {
                                    return Mono.error(new RuntimeException("取消Tekton任务失败"));
                                }
                            })
                );
    }

    @Override
    public Mono<DevOpsCiTaskInstance> getCiTaskInstanceStatus(String instanceId, Long userId) {
        return ciTaskInstanceRepository.findByUserIdAndInstanceId(userId, instanceId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务实例不存在或无权限访问")))
                .flatMap(instanceEntity ->
                    // 通过TektonExecutorService获取实时状态
                    tektonExecutorService.getTaskStatus(instanceId)
                            .map(statusInfo -> {
                                // 将实时状态信息合并到模型中
                                DevOpsCiTaskInstance instance = ciTaskInstanceMapper.toDto(instanceEntity);

                                // 设置从Tekton获取的实时信息
                                instance.setStatus((String) statusInfo.get("status"));
                                instance.setStartTime((LocalDateTime) statusInfo.get("startTime"));
                                instance.setEndTime((LocalDateTime) statusInfo.get("endTime"));

                                return instance;
                            })
                            .onErrorReturn(ciTaskInstanceMapper.toDto(instanceEntity)) // 出错时返回数据库状态
                );
    }

    @Override
    public Flux<DevOpsCiTaskInstance> getCiTaskInstances(Long taskId, Long userId) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")))
                .flatMapMany(ciTaskEntity -> 
                    ciTaskInstanceRepository.findByUserIdAndCiTaskId(userId, taskId)
                            .map(ciTaskInstanceMapper::toDto)
                );
    }

    @Override
    public Mono<String> getCiTaskInstanceLogs(String instanceId, Long userId) {
        return ciTaskInstanceRepository.findByUserIdAndInstanceId(userId, instanceId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务实例不存在或无权限访问")))
                .flatMap(instanceEntity ->
                    // 通过TektonExecutorService获取实时日志
                    tektonExecutorService.getTaskLogs(instanceId)
                            .onErrorReturn("暂无日志或获取日志失败")
                );
    }

    @Override
    public Mono<Integer> cleanupCompletedInstances(Long taskId, Long userId, int keepCount) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")))
                .flatMap(ciTaskEntity -> {
                    // 通过TektonExecutorService清理已完成的实例
                    // 这里可以获取所有实例，然后删除旧的已完成实例
                    // 暂时返回0，表示没有删除任何实例
                    return Mono.just(0);
                });
    }

    @Override
    public Mono<Boolean> validateConfiguration(Map<String, Object> configuration) {
        // 实现Tekton配置验证逻辑
        if (configuration == null) {
            return Mono.just(true); // 允许空配置
        }
        
        // 基本验证：检查必要的字段
        // 这里可以添加更复杂的Tekton YAML验证逻辑
        return Mono.just(true);
    }

    @Override
    public Mono<Map<String, Object>> getTaskTemplate(String taskType) {
        // 返回Tekton任务模板
        Map<String, Object> template = new HashMap<>();
        
        switch (taskType.toLowerCase()) {
            case "build":
                template = createBuildTemplate();
                break;
            case "test":
                template = createTestTemplate();
                break;
            case "deploy":
                template = createDeployTemplate();
                break;
            default:
                template = createDefaultTemplate();
        }
        
        return Mono.just(template);
    }

    @Override
    public Flux<String> getSupportedTaskTypes() {
        return Flux.just("build", "test", "deploy", "custom");
    }

    @Override
    public Mono<Boolean> checkConnection() {
        // 这里应该检查与Tekton集群的连接
        // 暂时返回true
        return Mono.just(true);
    }

    @Override
    public Mono<Map<String, Object>> getPlatformInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("platform", "Tekton");
        info.put("version", "v0.50.0");
        info.put("status", "connected");
        return Mono.just(info);
    }

    /**
     * 生成实例ID
     */
    private String generateInstanceId(String taskName) {
        return taskName.toLowerCase().replaceAll("[^a-z0-9-]", "-") + "-" + 
               UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 提交Tekton任务
     * 使用TektonYamlConfigBuilder生成YAML配置，然后通过TektonExecutorService创建TaskRun
     */
    private Mono<DevOpsCiTaskInstance> submitTektonTask(DevOpsCiTaskEntity ciTaskEntity, DevOpsCiTaskInstance instance) {
        try {
            // 解析配置字符串为Map
            Map<String, Object> configMap = new HashMap<>();
            if (ciTaskEntity.getConfiguration() != null && !ciTaskEntity.getConfiguration().trim().isEmpty()) {
                // 这里假设配置是JSON格式，实际项目中可能需要更复杂的解析逻辑
                configMap.put("rawConfig", ciTaskEntity.getConfiguration());
            }

            // 使用YAML配置构建器生成CI任务配置
            Map<String, Object> taskConfig = yamlConfigBuilder.buildCiTaskConfig(
                ciTaskEntity.getName(),
                ciTaskEntity.getTaskType(),
                configMap
            );

            // 生成TaskRun配置
            Map<String, Object> taskRunConfig = yamlConfigBuilder.buildCiTaskRunConfig(
                ciTaskEntity.getName(),
                instance.getInstanceId(),
                instance.getResultData()
            );

            // 准备任务参数
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("taskConfig", taskConfig);
            parameters.put("taskRunConfig", taskRunConfig);
            parameters.put("taskName", ciTaskEntity.getName());
            parameters.put("instanceId", instance.getInstanceId());

            // 通过TektonExecutorService创建TaskRun
            return tektonExecutorService.createTaskRun("default", ciTaskEntity.getName(), parameters)
                    .map(taskRunInfo -> {
                        // 更新实例状态为已提交
                        instance.setStatus("SUBMITTED");
                        return instance;
                    })
                    .onErrorReturn(instance); // 出错时返回原实例

        } catch (Exception e) {
            // YAML配置生成失败
            instance.setStatus("FAILED");
            return Mono.just(instance);
        }
    }
    


    /**
     * 创建构建模板
     * 使用TektonYamlConfigBuilder生成标准的构建任务配置
     */
    private Map<String, Object> createBuildTemplate() {
        Map<String, Object> buildConfig = new HashMap<>();
        buildConfig.put("buildTool", "maven");
        buildConfig.put("javaVersion", "17");
        buildConfig.put("goals", "clean compile package");

        return yamlConfigBuilder.buildCiTaskConfig("build-task", "build", buildConfig);
    }

    /**
     * 创建测试模板
     * 使用TektonYamlConfigBuilder生成标准的测试任务配置
     */
    private Map<String, Object> createTestTemplate() {
        Map<String, Object> testConfig = new HashMap<>();
        testConfig.put("testTool", "maven");
        testConfig.put("testGoals", "test");
        testConfig.put("reportFormat", "junit");

        return yamlConfigBuilder.buildCiTaskConfig("test-task", "test", testConfig);
    }

    /**
     * 创建部署模板
     * 使用TektonYamlConfigBuilder生成标准的部署任务配置
     */
    private Map<String, Object> createDeployTemplate() {
        Map<String, Object> deployConfig = new HashMap<>();
        deployConfig.put("deploymentTool", "kubectl");
        deployConfig.put("targetEnvironment", "staging");
        deployConfig.put("strategy", "rolling-update");

        return yamlConfigBuilder.buildCiTaskConfig("deploy-task", "deploy", deployConfig);
    }

    /**
     * 创建默认模板
     * 使用TektonYamlConfigBuilder生成默认任务配置
     */
    private Map<String, Object> createDefaultTemplate() {
        Map<String, Object> defaultConfig = new HashMap<>();
        defaultConfig.put("image", "alpine:latest");
        defaultConfig.put("script", "echo 'Hello World'");

        return yamlConfigBuilder.buildCiTaskConfig("default-task", "custom", defaultConfig);
    }
}
