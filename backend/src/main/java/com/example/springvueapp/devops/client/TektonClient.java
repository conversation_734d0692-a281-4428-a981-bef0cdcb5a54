package com.example.springvueapp.devops.client;

import io.fabric8.kubernetes.api.model.ObjectMeta;
import io.fabric8.kubernetes.api.model.Pod;
import io.fabric8.kubernetes.api.model.PodList;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.DefaultKubernetesClient;
import io.fabric8.kubernetes.client.dsl.LogWatch;


import io.fabric8.tekton.pipeline.v1beta1.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.example.springvueapp.cloudplatform.service.CloudPlatformService;
import com.example.springvueapp.cloudplatform.entity.CloudPlatform;
import com.example.springvueapp.cloudplatform.model.CloudPlatformDTO;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Tekton客户端实现
 * 基于fabric8 Tekton客户端库实现与Kubernetes Tekton API的实际交互
 * 提供Task和Pipeline的CRUD操作、实例管理和日志检索功能
 */
@Component
public class TektonClient {

    private static final Logger logger = LoggerFactory.getLogger(TektonClient.class);

    @Value("${tekton.namespace:default}")
    private String defaultNamespace;

    @Value("${tekton.connection.timeout:30}")
    private int connectionTimeoutSeconds;

    private io.fabric8.tekton.client.TektonClient tektonClient;
    private KubernetesClient kubernetesClient;

    @Autowired
    private CloudPlatformService cloudPlatformService;

    // 当前使用的云平台ID
    private String currentCloudPlatformId;

    @PostConstruct
    public void initialize() {
        try {
            // 初始化默认Kubernetes客户端
            kubernetesClient = new DefaultKubernetesClient();

            // 初始化Tekton客户端
            tektonClient = kubernetesClient.adapt(io.fabric8.tekton.client.TektonClient.class);

            logger.info("Tekton客户端初始化成功，默认命名空间: {}", defaultNamespace);
        } catch (Exception e) {
            logger.error("Tekton客户端初始化失败", e);
            throw new RuntimeException("无法连接到Tekton API", e);
        }
    }

    /**
     * 设置云平台连接
     * 根据云平台配置重新初始化Kubernetes和Tekton客户端
     */
    public Mono<Boolean> setCloudPlatform(Long userId, Long cloudPlatformId) {
        return cloudPlatformService.findCloudPlatformById(userId, cloudPlatformId)
                .flatMap(platformDto -> {
                    try {
                        // 关闭现有连接
                        if (tektonClient != null) {
                            tektonClient.close();
                        }
                        if (kubernetesClient != null) {
                            kubernetesClient.close();
                        }

                        // 根据云平台类型创建新的客户端
                        if ("KUBERNETES".equals(platformDto.getType())) {
                            // 使用云平台的URL和认证信息创建新的Kubernetes客户端
                            kubernetesClient = createKubernetesClient(platformDto);
                            tektonClient = kubernetesClient.adapt(io.fabric8.tekton.client.TektonClient.class);

                            currentCloudPlatformId = cloudPlatformId.toString();
                            logger.info("已切换到云平台: {} ({})", platformDto.getName(), platformDto.getUrl());
                            return Mono.just(true);
                        } else {
                            logger.warn("不支持的云平台类型: {}", platformDto.getType());
                            return Mono.just(false);
                        }
                    } catch (Exception e) {
                        logger.error("切换云平台失败: {}", platformDto.getName(), e);
                        return Mono.just(false);
                    }
                })
                .onErrorReturn(false);
    }

    /**
     * 根据云平台配置创建Kubernetes客户端
     */
    private KubernetesClient createKubernetesClient(CloudPlatformDTO platform) {
        // 这里应该根据云平台的认证信息创建客户端
        // 目前使用默认配置，实际应用中需要解析platform的认证配置
        return new DefaultKubernetesClient();
    }

    /**
     * 获取当前使用的云平台ID
     */
    public String getCurrentCloudPlatformId() {
        return currentCloudPlatformId;
    }

    @PreDestroy
    public void cleanup() {
        if (tektonClient != null) {
            tektonClient.close();
        }
        if (kubernetesClient != null) {
            kubernetesClient.close();
        }
        logger.info("Tekton客户端已关闭");
    }

    /**
     * 检查连接状态
     */
    public Mono<Boolean> checkConnection() {
        return Mono.fromCallable(() -> {
            try {
                // 尝试列出命名空间来验证连接
                kubernetesClient.namespaces().list();
                return true;
            } catch (Exception e) {
                logger.warn("Tekton连接检查失败", e);
                return false;
            }
        });
    }

    /**
     * 获取集群信息
     */
    public Mono<Map<String, Object>> getClusterInfo() {
        return Mono.fromCallable(() -> {
            Map<String, Object> info = new HashMap<>();
            try {
                var version = kubernetesClient.getVersion();
                info.put("kubernetesVersion", version.getGitVersion());
                info.put("platform", "Tekton");
                info.put("defaultNamespace", defaultNamespace);
                info.put("connected", true);
                info.put("lastChecked", LocalDateTime.now().toString());
            } catch (Exception e) {
                logger.error("获取集群信息失败", e);
                info.put("connected", false);
                info.put("error", e.getMessage());
            }
            return info;
        });
    }

    // ==================== 命名空间管理 ====================

    /**
     * 列出所有命名空间
     */
    public Flux<String> listNamespaces() {
        return Mono.fromCallable(() -> {
            try {
                return kubernetesClient.namespaces().list().getItems().stream()
                        .map(ns -> ns.getMetadata().getName())
                        .toList();
            } catch (Exception e) {
                logger.error("列出命名空间失败", e);
                return Collections.<String>emptyList();
            }
        }).flatMapMany(Flux::fromIterable);
    }

    /**
     * 创建命名空间
     */
    public Mono<Boolean> createNamespace(String namespace) {
        return Mono.fromCallable(() -> {
            try {
                var ns = kubernetesClient.namespaces().withName(namespace).get();
                if (ns == null) {
                    var namespaceObj = new io.fabric8.kubernetes.api.model.NamespaceBuilder()
                            .withNewMetadata()
                            .withName(namespace)
                            .endMetadata()
                            .build();
                    kubernetesClient.namespaces().create(namespaceObj);
                    logger.info("命名空间 {} 创建成功", namespace);
                }
                return true;
            } catch (Exception e) {
                logger.error("创建命名空间 {} 失败", namespace, e);
                return false;
            }
        });
    }

    /**
     * 删除命名空间
     */
    public Mono<Boolean> deleteNamespace(String namespace) {
        return Mono.fromCallable(() -> {
            try {
                Boolean deleted = kubernetesClient.namespaces().withName(namespace).delete();
                if (Boolean.TRUE.equals(deleted)) {
                    logger.info("命名空间 {} 删除成功", namespace);
                    return true;
                } else {
                    logger.warn("命名空间 {} 不存在或删除失败", namespace);
                    return false;
                }
            } catch (Exception e) {
                logger.error("删除命名空间 {} 失败", namespace, e);
                return false;
            }
        });
    }

    // ==================== TaskRun管理 ====================

    /**
     * 创建TaskRun实例
     */
    public Mono<Map<String, Object>> createTaskRun(String namespace, String taskName, 
                                                   Map<String, Object> parameters) {
        return Mono.fromCallable(() -> {
            try {
                String taskRunName = generateTaskRunName(taskName);
                
                TaskRunBuilder taskRunBuilder = new TaskRunBuilder()
                        .withNewMetadata()
                        .withName(taskRunName)
                        .withNamespace(namespace != null ? namespace : defaultNamespace)
                        .endMetadata()
                        .withNewSpec()
                        .withNewTaskRef()
                        .withName(taskName)
                        .endTaskRef()
                        .endSpec();

                // 添加参数
                if (parameters != null && !parameters.isEmpty()) {
                    List<Param> params = new ArrayList<>();
                    parameters.forEach((key, value) -> {
                        params.add(new ParamBuilder()
                                .withName(key)
                                .withNewValue(value.toString())
                                .build());
                    });
                    taskRunBuilder.editSpec().withParams(params).endSpec();
                }

                TaskRun taskRun = taskRunBuilder.build();
                
                TaskRun created = tektonClient.v1beta1().taskRuns()
                        .inNamespace(namespace != null ? namespace : defaultNamespace)
                        .create(taskRun);

                return taskRunToMap(created);
            } catch (Exception e) {
                logger.error("创建TaskRun失败: taskName={}, namespace={}", taskName, namespace, e);
                throw new RuntimeException("创建TaskRun失败", e);
            }
        });
    }

    /**
     * 获取TaskRun状态
     */
    public Mono<Map<String, Object>> getTaskRunStatus(String namespace, String taskRunName) {
        return Mono.fromCallable(() -> {
            try {
                TaskRun taskRun = tektonClient.v1beta1().taskRuns()
                        .inNamespace(namespace != null ? namespace : defaultNamespace)
                        .withName(taskRunName)
                        .get();

                if (taskRun == null) {
                    throw new RuntimeException("TaskRun不存在: " + taskRunName);
                }

                return taskRunToMap(taskRun);
            } catch (Exception e) {
                logger.error("获取TaskRun状态失败: taskRunName={}, namespace={}", taskRunName, namespace, e);
                throw new RuntimeException("获取TaskRun状态失败", e);
            }
        });
    }

    /**
     * 列出TaskRun实例
     */
    public Flux<Map<String, Object>> listTaskRuns(String namespace, String taskName) {
        return Mono.fromCallable(() -> {
            try {
                TaskRunList taskRunList;
                if (taskName != null) {
                    taskRunList = tektonClient.v1beta1().taskRuns()
                            .inNamespace(namespace != null ? namespace : defaultNamespace)
                            .withLabel("tekton.dev/task", taskName)
                            .list();
                } else {
                    taskRunList = tektonClient.v1beta1().taskRuns()
                            .inNamespace(namespace != null ? namespace : defaultNamespace)
                            .list();
                }

                return taskRunList.getItems().stream()
                        .map(this::taskRunToMap)
                        .toList();
            } catch (Exception e) {
                logger.error("列出TaskRun失败: taskName={}, namespace={}", taskName, namespace, e);
                return Collections.<Map<String, Object>>emptyList();
            }
        }).flatMapMany(Flux::fromIterable);
    }

    /**
     * 删除TaskRun实例
     */
    public Mono<Boolean> deleteTaskRun(String namespace, String taskRunName) {
        return Mono.fromCallable(() -> {
            try {
                Boolean deleted = tektonClient.v1beta1().taskRuns()
                        .inNamespace(namespace != null ? namespace : defaultNamespace)
                        .withName(taskRunName)
                        .delete();

                if (Boolean.TRUE.equals(deleted)) {
                    logger.info("TaskRun {} 删除成功", taskRunName);
                    return true;
                } else {
                    logger.warn("TaskRun {} 不存在或删除失败", taskRunName);
                    return false;
                }
            } catch (Exception e) {
                logger.error("删除TaskRun失败: taskRunName={}, namespace={}", taskRunName, namespace, e);
                return false;
            }
        });
    }

    // ==================== 辅助方法 ====================

    /**
     * 生成TaskRun名称
     */
    private String generateTaskRunName(String taskName) {
        return taskName.toLowerCase().replaceAll("[^a-z0-9-]", "-") + "-run-" +
               UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 将TaskRun转换为Map
     */
    private Map<String, Object> taskRunToMap(TaskRun taskRun) {
        Map<String, Object> result = new HashMap<>();
        
        ObjectMeta metadata = taskRun.getMetadata();
        result.put("name", metadata.getName());
        result.put("namespace", metadata.getNamespace());
        result.put("createdAt", metadata.getCreationTimestamp());
        
        TaskRunStatus status = taskRun.getStatus();
        if (status != null) {
            result.put("status", getTaskRunPhase(status));
            result.put("startTime", status.getStartTime());
            result.put("completionTime", status.getCompletionTime());
            
            if (status.getConditions() != null && !status.getConditions().isEmpty()) {
                var condition = status.getConditions().get(0);
                result.put("reason", condition.getReason());
                result.put("message", condition.getMessage());
            }
        } else {
            result.put("status", "PENDING");
        }
        
        return result;
    }

    /**
     * 获取TaskRun阶段状态
     */
    private String getTaskRunPhase(TaskRunStatus status) {
        if (status.getConditions() == null || status.getConditions().isEmpty()) {
            return "PENDING";
        }

        var condition = status.getConditions().get(0);
        String conditionStatus = condition.getStatus();
        String reason = condition.getReason();

        if ("True".equals(conditionStatus)) {
            return "COMPLETED";
        } else if ("False".equals(conditionStatus)) {
            return "FAILED";
        } else if ("TaskRunCancelled".equals(reason)) {
            return "CANCELLED";
        } else {
            return "RUNNING";
        }
    }

    // ==================== PipelineRun管理 ====================

    /**
     * 创建PipelineRun实例
     */
    public Mono<Map<String, Object>> createPipelineRun(String namespace, String pipelineName,
                                                       Map<String, Object> parameters) {
        return Mono.fromCallable(() -> {
            try {
                String pipelineRunName = generatePipelineRunName(pipelineName);

                PipelineRunBuilder pipelineRunBuilder = new PipelineRunBuilder()
                        .withNewMetadata()
                        .withName(pipelineRunName)
                        .withNamespace(namespace != null ? namespace : defaultNamespace)
                        .endMetadata()
                        .withNewSpec()
                        .withNewPipelineRef()
                        .withName(pipelineName)
                        .endPipelineRef()
                        .endSpec();

                // 添加参数
                if (parameters != null && !parameters.isEmpty()) {
                    List<Param> params = new ArrayList<>();
                    parameters.forEach((key, value) -> {
                        params.add(new ParamBuilder()
                                .withName(key)
                                .withNewValue(value.toString())
                                .build());
                    });
                    pipelineRunBuilder.editSpec().withParams(params).endSpec();
                }

                PipelineRun pipelineRun = pipelineRunBuilder.build();

                PipelineRun created = tektonClient.v1beta1().pipelineRuns()
                        .inNamespace(namespace != null ? namespace : defaultNamespace)
                        .create(pipelineRun);

                return pipelineRunToMap(created);
            } catch (Exception e) {
                logger.error("创建PipelineRun失败: pipelineName={}, namespace={}", pipelineName, namespace, e);
                throw new RuntimeException("创建PipelineRun失败", e);
            }
        });
    }

    /**
     * 获取PipelineRun状态
     */
    public Mono<Map<String, Object>> getPipelineRunStatus(String namespace, String pipelineRunName) {
        return Mono.fromCallable(() -> {
            try {
                PipelineRun pipelineRun = tektonClient.v1beta1().pipelineRuns()
                        .inNamespace(namespace != null ? namespace : defaultNamespace)
                        .withName(pipelineRunName)
                        .get();

                if (pipelineRun == null) {
                    throw new RuntimeException("PipelineRun不存在: " + pipelineRunName);
                }

                return pipelineRunToMap(pipelineRun);
            } catch (Exception e) {
                logger.error("获取PipelineRun状态失败: pipelineRunName={}, namespace={}", pipelineRunName, namespace, e);
                throw new RuntimeException("获取PipelineRun状态失败", e);
            }
        });
    }

    /**
     * 列出PipelineRun实例
     */
    public Flux<Map<String, Object>> listPipelineRuns(String namespace, String pipelineName) {
        return Mono.fromCallable(() -> {
            try {
                PipelineRunList pipelineRunList;
                if (pipelineName != null) {
                    pipelineRunList = tektonClient.v1beta1().pipelineRuns()
                            .inNamespace(namespace != null ? namespace : defaultNamespace)
                            .withLabel("tekton.dev/pipeline", pipelineName)
                            .list();
                } else {
                    pipelineRunList = tektonClient.v1beta1().pipelineRuns()
                            .inNamespace(namespace != null ? namespace : defaultNamespace)
                            .list();
                }

                return pipelineRunList.getItems().stream()
                        .map(this::pipelineRunToMap)
                        .toList();
            } catch (Exception e) {
                logger.error("列出PipelineRun失败: pipelineName={}, namespace={}", pipelineName, namespace, e);
                return Collections.<Map<String, Object>>emptyList();
            }
        }).flatMapMany(Flux::fromIterable);
    }

    /**
     * 删除PipelineRun实例
     */
    public Mono<Boolean> deletePipelineRun(String namespace, String pipelineRunName) {
        return Mono.fromCallable(() -> {
            try {
                Boolean deleted = tektonClient.v1beta1().pipelineRuns()
                        .inNamespace(namespace != null ? namespace : defaultNamespace)
                        .withName(pipelineRunName)
                        .delete();

                if (Boolean.TRUE.equals(deleted)) {
                    logger.info("PipelineRun {} 删除成功", pipelineRunName);
                    return true;
                } else {
                    logger.warn("PipelineRun {} 不存在或删除失败", pipelineRunName);
                    return false;
                }
            } catch (Exception e) {
                logger.error("删除PipelineRun失败: pipelineRunName={}, namespace={}", pipelineRunName, namespace, e);
                return false;
            }
        });
    }

    /**
     * 生成PipelineRun名称
     */
    private String generatePipelineRunName(String pipelineName) {
        return pipelineName.toLowerCase().replaceAll("[^a-z0-9-]", "-") + "-run-" +
               UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 将PipelineRun转换为Map
     */
    private Map<String, Object> pipelineRunToMap(PipelineRun pipelineRun) {
        Map<String, Object> result = new HashMap<>();

        ObjectMeta metadata = pipelineRun.getMetadata();
        result.put("name", metadata.getName());
        result.put("namespace", metadata.getNamespace());
        result.put("createdAt", metadata.getCreationTimestamp());

        PipelineRunStatus status = pipelineRun.getStatus();
        if (status != null) {
            result.put("status", getPipelineRunPhase(status));
            result.put("startTime", status.getStartTime());
            result.put("completionTime", status.getCompletionTime());

            if (status.getConditions() != null && !status.getConditions().isEmpty()) {
                var condition = status.getConditions().get(0);
                result.put("reason", condition.getReason());
                result.put("message", condition.getMessage());
            }
        } else {
            result.put("status", "PENDING");
        }

        return result;
    }

    /**
     * 获取PipelineRun阶段状态
     */
    private String getPipelineRunPhase(PipelineRunStatus status) {
        if (status.getConditions() == null || status.getConditions().isEmpty()) {
            return "PENDING";
        }

        var condition = status.getConditions().get(0);
        String conditionStatus = condition.getStatus();
        String reason = condition.getReason();

        if ("True".equals(conditionStatus)) {
            return "COMPLETED";
        } else if ("False".equals(conditionStatus)) {
            return "FAILED";
        } else if ("PipelineRunCancelled".equals(reason)) {
            return "CANCELLED";
        } else {
            return "RUNNING";
        }
    }

    // ==================== 日志管理 ====================

    /**
     * 获取TaskRun日志
     */
    public Mono<String> getTaskRunLogs(String namespace, String taskRunName) {
        return Mono.fromCallable(() -> {
            try {
                // 首先获取TaskRun以找到相关的Pod
                TaskRun taskRun = tektonClient.v1beta1().taskRuns()
                        .inNamespace(namespace != null ? namespace : defaultNamespace)
                        .withName(taskRunName)
                        .get();

                if (taskRun == null) {
                    return "TaskRun不存在: " + taskRunName;
                }

                // 查找与TaskRun关联的Pod
                String podName = findTaskRunPod(namespace, taskRunName);
                if (podName == null) {
                    return "未找到与TaskRun关联的Pod: " + taskRunName;
                }

                // 获取Pod日志
                return getPodLogs(namespace, podName);
            } catch (Exception e) {
                logger.error("获取TaskRun日志失败: taskRunName={}, namespace={}", taskRunName, namespace, e);
                return "获取日志失败: " + e.getMessage();
            }
        });
    }

    /**
     * 获取PipelineRun日志
     */
    public Mono<String> getPipelineRunLogs(String namespace, String pipelineRunName) {
        return Mono.fromCallable(() -> {
            try {
                // 首先获取PipelineRun以找到相关的TaskRun
                PipelineRun pipelineRun = tektonClient.v1beta1().pipelineRuns()
                        .inNamespace(namespace != null ? namespace : defaultNamespace)
                        .withName(pipelineRunName)
                        .get();

                if (pipelineRun == null) {
                    return "PipelineRun不存在: " + pipelineRunName;
                }

                StringBuilder logs = new StringBuilder();
                logs.append("=== PipelineRun日志: ").append(pipelineRunName).append(" ===\n");

                // 获取所有相关TaskRun的日志
                TaskRunList taskRuns = tektonClient.v1beta1().taskRuns()
                        .inNamespace(namespace != null ? namespace : defaultNamespace)
                        .withLabel("tekton.dev/pipelineRun", pipelineRunName)
                        .list();

                for (TaskRun taskRun : taskRuns.getItems()) {
                    String taskRunName = taskRun.getMetadata().getName();
                    logs.append("\n--- TaskRun: ").append(taskRunName).append(" ---\n");

                    String podName = findTaskRunPod(namespace, taskRunName);
                    if (podName != null) {
                        String taskLogs = getPodLogs(namespace, podName);
                        logs.append(taskLogs);
                    } else {
                        logs.append("未找到Pod日志");
                    }
                    logs.append("\n");
                }

                return logs.toString();
            } catch (Exception e) {
                logger.error("获取PipelineRun日志失败: pipelineRunName={}, namespace={}", pipelineRunName, namespace, e);
                return "获取日志失败: " + e.getMessage();
            }
        });
    }

    /**
     * 查找TaskRun关联的Pod
     */
    private String findTaskRunPod(String namespace, String taskRunName) {
        try {
            PodList pods = kubernetesClient.pods()
                    .inNamespace(namespace != null ? namespace : defaultNamespace)
                    .withLabel("tekton.dev/taskRun", taskRunName)
                    .list();

            if (!pods.getItems().isEmpty()) {
                return pods.getItems().get(0).getMetadata().getName();
            }
        } catch (Exception e) {
            logger.warn("查找TaskRun Pod失败: taskRunName={}", taskRunName, e);
        }
        return null;
    }

    /**
     * 获取Pod日志
     */
    private String getPodLogs(String namespace, String podName) {
        try {
            String logs = kubernetesClient.pods()
                    .inNamespace(namespace != null ? namespace : defaultNamespace)
                    .withName(podName)
                    .getLog();

            return logs != null ? logs : "暂无日志内容";
        } catch (Exception e) {
            logger.warn("获取Pod日志失败: podName={}", podName, e);
            return "获取Pod日志失败: " + e.getMessage();
        }
    }

    // ==================== 清理操作 ====================

    /**
     * 清理已完成的TaskRun
     */
    public Mono<Integer> cleanupCompletedTaskRuns(String namespace, String taskName, int keepCount) {
        return Mono.fromCallable(() -> {
            try {
                TaskRunList taskRuns = tektonClient.v1beta1().taskRuns()
                        .inNamespace(namespace != null ? namespace : defaultNamespace)
                        .withLabel("tekton.dev/task", taskName)
                        .list();

                // 按创建时间排序，保留最新的keepCount个
                List<TaskRun> sortedTaskRuns = taskRuns.getItems().stream()
                        .filter(tr -> isTaskRunCompleted(tr))
                        .sorted((a, b) -> b.getMetadata().getCreationTimestamp()
                                .compareTo(a.getMetadata().getCreationTimestamp()))
                        .toList();

                int deletedCount = 0;
                if (sortedTaskRuns.size() > keepCount) {
                    List<TaskRun> toDelete = sortedTaskRuns.subList(keepCount, sortedTaskRuns.size());
                    for (TaskRun taskRun : toDelete) {
                        Boolean deleted = tektonClient.v1beta1().taskRuns()
                                .inNamespace(namespace != null ? namespace : defaultNamespace)
                                .withName(taskRun.getMetadata().getName())
                                .delete();
                        if (Boolean.TRUE.equals(deleted)) {
                            deletedCount++;
                        }
                    }
                }

                logger.info("清理TaskRun完成: 删除{}个实例", deletedCount);
                return deletedCount;
            } catch (Exception e) {
                logger.error("清理TaskRun失败: taskName={}, namespace={}", taskName, namespace, e);
                return 0;
            }
        });
    }

    /**
     * 清理已完成的PipelineRun
     */
    public Mono<Integer> cleanupCompletedPipelineRuns(String namespace, String pipelineName, int keepCount) {
        return Mono.fromCallable(() -> {
            try {
                PipelineRunList pipelineRuns = tektonClient.v1beta1().pipelineRuns()
                        .inNamespace(namespace != null ? namespace : defaultNamespace)
                        .withLabel("tekton.dev/pipeline", pipelineName)
                        .list();

                // 按创建时间排序，保留最新的keepCount个
                List<PipelineRun> sortedPipelineRuns = pipelineRuns.getItems().stream()
                        .filter(pr -> isPipelineRunCompleted(pr))
                        .sorted((a, b) -> b.getMetadata().getCreationTimestamp()
                                .compareTo(a.getMetadata().getCreationTimestamp()))
                        .toList();

                int deletedCount = 0;
                if (sortedPipelineRuns.size() > keepCount) {
                    List<PipelineRun> toDelete = sortedPipelineRuns.subList(keepCount, sortedPipelineRuns.size());
                    for (PipelineRun pipelineRun : toDelete) {
                        Boolean deleted = tektonClient.v1beta1().pipelineRuns()
                                .inNamespace(namespace != null ? namespace : defaultNamespace)
                                .withName(pipelineRun.getMetadata().getName())
                                .delete();
                        if (Boolean.TRUE.equals(deleted)) {
                            deletedCount++;
                        }
                    }
                }

                logger.info("清理PipelineRun完成: 删除{}个实例", deletedCount);
                return deletedCount;
            } catch (Exception e) {
                logger.error("清理PipelineRun失败: pipelineName={}, namespace={}", pipelineName, namespace, e);
                return 0;
            }
        });
    }

    /**
     * 检查TaskRun是否已完成
     */
    private boolean isTaskRunCompleted(TaskRun taskRun) {
        if (taskRun.getStatus() == null || taskRun.getStatus().getConditions() == null) {
            return false;
        }

        String phase = getTaskRunPhase(taskRun.getStatus());
        return "COMPLETED".equals(phase) || "FAILED".equals(phase) || "CANCELLED".equals(phase);
    }

    /**
     * 检查PipelineRun是否已完成
     */
    private boolean isPipelineRunCompleted(PipelineRun pipelineRun) {
        if (pipelineRun.getStatus() == null || pipelineRun.getStatus().getConditions() == null) {
            return false;
        }

        String phase = getPipelineRunPhase(pipelineRun.getStatus());
        return "COMPLETED".equals(phase) || "FAILED".equals(phase) || "CANCELLED".equals(phase);
    }
}
