package com.example.springvueapp.devops.service;

import com.example.springvueapp.devops.entity.DevOpsApplicationEntity;
import com.example.springvueapp.devops.entity.DevOpsComponentApplicationEntity;
import com.example.springvueapp.devops.mapper.DevOpsApplicationMapper;
import com.example.springvueapp.devops.model.DevOpsApplication;
import com.example.springvueapp.devops.repository.DevOpsApplicationRepository;
import com.example.springvueapp.devops.repository.DevOpsComponentApplicationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * DevOps应用管理服务
 */
@Service
public class DevOpsApplicationService {

    private final DevOpsApplicationRepository applicationRepository;
    private final DevOpsComponentApplicationRepository componentApplicationRepository;
    private final DevOpsApplicationMapper applicationMapper;

    @Autowired
    public DevOpsApplicationService(DevOpsApplicationRepository applicationRepository,
                                   DevOpsComponentApplicationRepository componentApplicationRepository,
                                   DevOpsApplicationMapper applicationMapper) {
        this.applicationRepository = applicationRepository;
        this.componentApplicationRepository = componentApplicationRepository;
        this.applicationMapper = applicationMapper;
    }

    /**
     * 创建新应用
     * @param application 应用信息
     * @param userId 用户ID
     * @return 创建的应用
     */
    @Transactional
    public Mono<DevOpsApplication> createApplication(DevOpsApplication application, Long userId) {
        DevOpsApplicationEntity entity = applicationMapper.toNewEntity(application, userId);
        return applicationRepository.save(entity)
                .flatMap(savedEntity -> {
                    DevOpsApplication dto = applicationMapper.toDto(savedEntity);
                    // 处理组件关联
                    return updateApplicationComponents(dto.getId(), application.getComponents())
                            .thenReturn(dto);
                });
    }

    /**
     * 根据ID获取应用
     * @param applicationId 应用ID
     * @param userId 用户ID
     * @return 应用信息
     */
    public Mono<DevOpsApplication> getApplicationById(Long applicationId, Long userId) {
        return applicationRepository.findByUserIdAndId(userId, applicationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("应用不存在或无权限访问")))
                .flatMap(this::fillComponentIds);
    }

    /**
     * 获取用户的所有应用
     * @param userId 用户ID
     * @return 应用列表
     */
    public Flux<DevOpsApplication> getAllApplications(Long userId) {
        return applicationRepository.findByUserId(userId)
                .concatMap(this::fillComponentIds);
    }

    /**
     * 根据状态获取应用
     * @param userId 用户ID
     * @param status 应用状态
     * @return 应用列表
     */
    public Flux<DevOpsApplication> getApplicationsByStatus(Long userId, String status) {
        return applicationRepository.findByUserIdAndStatus(userId, status)
                .concatMap(this::fillComponentIds);
    }

    /**
     * 更新应用
     * @param applicationId 应用ID
     * @param application 更新的应用信息
     * @param userId 用户ID
     * @return 更新后的应用
     */
    @Transactional
    public Mono<DevOpsApplication> updateApplication(Long applicationId, DevOpsApplication application, Long userId) {
        return applicationRepository.findByUserIdAndId(userId, applicationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("应用不存在或无权限访问")))
                .flatMap(existingEntity -> {
                    DevOpsApplicationEntity updatedEntity = applicationMapper.toUpdateEntity(application, existingEntity);
                    return applicationRepository.save(updatedEntity);
                })
                .flatMap(updatedEntity -> {
                    DevOpsApplication dto = applicationMapper.toDto(updatedEntity);
                    // 处理组件关联
                    return updateApplicationComponents(dto.getId(), application.getComponents())
                            .thenReturn(dto);
                });
    }

    /**
     * 删除应用
     * @param applicationId 应用ID
     * @param userId 用户ID
     * @return 删除结果
     */
    public Mono<Boolean> deleteApplication(Long applicationId, Long userId) {
        return applicationRepository.findByUserIdAndId(userId, applicationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("应用不存在或无权限访问")))
                .flatMap(entity -> applicationRepository.deleteByUserIdAndId(userId, applicationId))
                .map(deletedCount -> deletedCount > 0);
    }

    /**
     * 搜索应用
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @return 应用列表
     */
    public Flux<DevOpsApplication> searchApplications(Long userId, String keyword) {
        String pattern = "%" + keyword + "%";
        return applicationRepository.findByUserIdAndNameContaining(userId, pattern)
                .concatMap(this::fillComponentIds);
    }

    /**
     * 统计用户的应用数量
     * @param userId 用户ID
     * @return 应用数量
     */
    public Mono<Long> countApplications(Long userId) {
        return applicationRepository.countByUserId(userId);
    }
    
    /**
     * 更新应用的组件关联
     * @param applicationId 应用ID
     * @param componentIds 组件ID列表
     * @return 更新结果
     */
    @Transactional
    private Mono<Void> updateApplicationComponents(Long applicationId, List<Long> componentIds) {
        if (componentIds == null) {
            componentIds = List.of(); // 空列表而不是null
        }
        
        // 先删除现有的关联
        return componentApplicationRepository.deleteByApplicationId(applicationId)
                // 然后添加新的关联
                .thenMany(Flux.fromIterable(componentIds))
                .flatMap(componentId -> {
                    DevOpsComponentApplicationEntity entity = new DevOpsComponentApplicationEntity();
                    entity.setApplicationId(applicationId);
                    entity.setComponentId(componentId);
                    return componentApplicationRepository.save(entity)
                            .then();
                })
                .then();
    }
    
    /**
     * 填充应用的组件ID列表
     * @param entity 应用实体
     * @return 填充了组件ID列表的应用DTO
     */
    private Mono<DevOpsApplication> fillComponentIds(DevOpsApplicationEntity entity) {
        return componentApplicationRepository.findByApplicationId(entity.getId())
                .map(DevOpsComponentApplicationEntity::getComponentId)
                .collectList()
                .map(componentIds -> applicationMapper.toDto(entity, componentIds));
    }
}