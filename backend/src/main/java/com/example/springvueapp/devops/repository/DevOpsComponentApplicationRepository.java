package com.example.springvueapp.devops.repository;

import com.example.springvueapp.devops.entity.DevOpsComponentApplicationEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 组件和应用关联关系的响应式Repository接口
 */
@Repository
public interface DevOpsComponentApplicationRepository extends ReactiveCrudRepository<DevOpsComponentApplicationEntity, Long> {

    /**
     * 根据组件ID查找所有关联的应用
     * @param componentId 组件ID
     * @return 关联关系列表
     */
    Flux<DevOpsComponentApplicationEntity> findByComponentId(Long componentId);

    /**
     * 根据应用ID查找所有关联的组件
     * @param applicationId 应用ID
     * @return 关联关系列表
     */
    Flux<DevOpsComponentApplicationEntity> findByApplicationId(Long applicationId);

    /**
     * 根据组件ID和应用ID查找关联关系
     * @param componentId 组件ID
     * @param applicationId 应用ID
     * @return 关联关系
     */
    Mono<DevOpsComponentApplicationEntity> findByComponentIdAndApplicationId(Long componentId, Long applicationId);

    /**
     * 根据组件ID删除所有关联关系
     * @param componentId 组件ID
     * @return 删除的行数
     */
    Mono<Void> deleteByComponentId(Long componentId);

    /**
     * 根据应用ID删除所有关联关系
     * @param applicationId 应用ID
     * @return 删除的行数
     */
    Mono<Void> deleteByApplicationId(Long applicationId);

    /**
     * 检查组件和应用之间是否存在关联关系
     * @param componentId 组件ID
     * @param applicationId 应用ID
     * @return 是否存在关联关系
     */
    Mono<Boolean> existsByComponentIdAndApplicationId(Long componentId, Long applicationId);
}