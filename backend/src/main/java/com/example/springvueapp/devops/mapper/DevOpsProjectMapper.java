package com.example.springvueapp.devops.mapper;

import com.example.springvueapp.devops.entity.DevOpsProjectEntity;
import com.example.springvueapp.devops.model.DevOpsProject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DevOps项目的实体与DTO转换器
 */
@Component
public class DevOpsProjectMapper {

    /**
     * 将实体转换为DTO，并填充关联的应用ID列表
     * @param entity 项目实体
     * @param applicationIds 关联的应用ID列表
     * @return 项目DTO
     */
    public DevOpsProject toDto(DevOpsProjectEntity entity, List<Long> applicationIds) {
        if (entity == null) {
            return null;
        }

        return DevOpsProject.builder()
                .id(entity.getId())
                .name(entity.getName())
                .description(entity.getDescription())
                .status(entity.getStatus())
                .userId(entity.getUserId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .applicationIds(applicationIds)
                .build();
    }

    /**
     * 将实体转换为DTO
     * @param entity 项目实体
     * @return 项目DTO
     */
    public DevOpsProject toDto(DevOpsProjectEntity entity) {
        if (entity == null) {
            return null;
        }

        return DevOpsProject.builder()
                .id(entity.getId())
                .name(entity.getName())
                .description(entity.getDescription())
                .status(entity.getStatus())
                .userId(entity.getUserId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为实体
     * @param dto 项目DTO
     * @return 项目实体
     */
    public DevOpsProjectEntity toEntity(DevOpsProject dto) {
        if (dto == null) {
            return null;
        }

        return DevOpsProjectEntity.builder()
                .id(dto.getId())
                .name(dto.getName())
                .description(dto.getDescription())
                .status(dto.getStatus())
                .userId(dto.getUserId())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为新建实体（不包含ID和时间戳）
     * @param dto 项目DTO
     * @param userId 用户ID
     * @return 项目实体
     */
    public DevOpsProjectEntity toNewEntity(DevOpsProject dto, Long userId) {
        if (dto == null) {
            return null;
        }

        LocalDateTime now = LocalDateTime.now();
        return DevOpsProjectEntity.builder()
                .name(dto.getName())
                .description(dto.getDescription())
                .status(dto.getStatus() != null ? dto.getStatus() : "ACTIVE")
                .userId(userId)
                .createdAt(now)
                .updatedAt(now)
                .build();
    }

    /**
     * 将DTO转换为更新实体（保留ID，更新时间戳）
     * @param dto 项目DTO
     * @param existingEntity 现有实体
     * @return 更新后的项目实体
     */
    public DevOpsProjectEntity toUpdateEntity(DevOpsProject dto, DevOpsProjectEntity existingEntity) {
        if (dto == null || existingEntity == null) {
            return null;
        }

        return DevOpsProjectEntity.builder()
                .id(existingEntity.getId())
                .name(dto.getName() != null ? dto.getName() : existingEntity.getName())
                .description(dto.getDescription() != null ? dto.getDescription() : existingEntity.getDescription())
                .status(dto.getStatus() != null ? dto.getStatus() : existingEntity.getStatus())
                .userId(existingEntity.getUserId())
                .createdAt(existingEntity.getCreatedAt())
                .updatedAt(LocalDateTime.now())
                .build();
    }
}