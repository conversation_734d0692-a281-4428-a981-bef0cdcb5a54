package com.example.springvueapp.devops.controller;

import com.example.springvueapp.common.auth.AuthUtils;
import com.example.springvueapp.devops.model.DevOpsProject;
import com.example.springvueapp.devops.service.DevOpsProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import jakarta.validation.Valid;

/**
 * DevOps项目管理控制器
 */
@RestController
@RequestMapping("/api/devops/projects")
public class DevOpsProjectController {

    private final DevOpsProjectService projectService;
    private final AuthUtils authUtils;

    @Autowired
    public DevOpsProjectController(DevOpsProjectService projectService, AuthUtils authUtils) {
        this.projectService = projectService;
        this.authUtils = authUtils;
    }

    /**
     * 创建新项目
     * @param project 项目信息
     * @param authentication 认证信息
     * @return 创建的项目
     */
    @PostMapping
    public Mono<ResponseEntity<DevOpsProject>> createProject(
            @Valid @RequestBody DevOpsProject project,
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return projectService.createProject(project, userId)
                .map(createdProject -> ResponseEntity.status(HttpStatus.CREATED).body(createdProject))
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.badRequest().build());
    }

    /**
     * 获取所有项目
     * @param authentication 认证信息
     * @return 项目列表
     */
    @GetMapping
    public Flux<DevOpsProject> getAllProjects(Authentication authentication) {
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return projectService.getAllProjects(userId);
    }

    /**
     * 根据ID获取项目
     * @param projectId 项目ID
     * @param authentication 认证信息
     * @return 项目信息
     */
    @GetMapping("/{projectId}")
    public Mono<ResponseEntity<DevOpsProject>> getProjectById(
            @PathVariable Long projectId,
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return projectService.getProjectById(projectId, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.notFound().build());
    }

    /**
     * 更新项目
     * @param projectId 项目ID
     * @param project 更新的项目信息
     * @param authentication 认证信息
     * @return 更新后的项目
     */
    @PutMapping("/{projectId}")
    public Mono<ResponseEntity<DevOpsProject>> updateProject(
            @PathVariable Long projectId,
            @Valid @RequestBody DevOpsProject project,
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return projectService.updateProject(projectId, project, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class, 
                    ResponseEntity.badRequest().build());
    }

    /**
     * 删除项目
     * @param projectId 项目ID
     * @param authentication 认证信息
     * @return 删除结果
     */
    @DeleteMapping("/{projectId}")
    public Mono<ResponseEntity<Void>> deleteProject(
            @PathVariable Long projectId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return projectService.deleteProject(projectId, userId)
                .then(Mono.just(ResponseEntity.noContent().<Void>build()))
                .onErrorReturn(IllegalArgumentException.class,
                    ResponseEntity.notFound().build());
    }

    /**
     * 根据状态获取项目
     * @param status 项目状态
     * @param authentication 认证信息
     * @return 项目列表
     */
    @GetMapping("/status/{status}")
    public Flux<DevOpsProject> getProjectsByStatus(
            @PathVariable String status,
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return projectService.getProjectsByStatus(userId, status);
    }

    /**
     * 搜索项目
     * @param keyword 搜索关键词
     * @param authentication 认证信息
     * @return 项目列表
     */
    @GetMapping("/search")
    public Flux<DevOpsProject> searchProjects(
            @RequestParam String keyword,
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return projectService.searchProjects(userId, keyword);
    }

    /**
     * 统计项目数量
     * @param authentication 认证信息
     * @return 项目数量
     */
    @GetMapping("/count")
    public Mono<Long> countProjects(Authentication authentication) {
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return projectService.countProjects(userId);
    }
}