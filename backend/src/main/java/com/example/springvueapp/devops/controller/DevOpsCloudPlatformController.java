package com.example.springvueapp.devops.controller;

import com.example.springvueapp.cloudplatform.model.CloudPlatformDTO;
import com.example.springvueapp.cloudplatform.service.CloudPlatformService;
import com.example.springvueapp.devops.service.TektonExecutorService;
import com.example.springvueapp.common.model.ApiResponse;
import com.example.springvueapp.common.auth.AuthUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.HashMap;

/**
 * DevOps云平台集成控制器
 * 提供DevOps模块与云平台管理的集成接口
 * 支持云平台选择、连接测试和任务执行平台配置
 */
@RestController
@RequestMapping("/api/devops/cloud-platforms")
public class DevOpsCloudPlatformController {

    private final CloudPlatformService cloudPlatformService;
    private final TektonExecutorService tektonExecutorService;
    private final AuthUtils authUtils;

    @Autowired
    public DevOpsCloudPlatformController(CloudPlatformService cloudPlatformService,
                                       TektonExecutorService tektonExecutorService,
                                       AuthUtils authUtils) {
        this.cloudPlatformService = cloudPlatformService;
        this.tektonExecutorService = tektonExecutorService;
        this.authUtils = authUtils;
    }

    /**
     * 获取用户的云平台列表（仅支持Kubernetes类型）
     * @param authentication 认证信息
     * @return 云平台列表
     */
    @GetMapping
    public Mono<ResponseEntity<ApiResponse<Flux<CloudPlatformDTO>>>> getAvailableCloudPlatforms(
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        
        // 只获取Kubernetes类型的云平台，因为DevOps模块目前只支持Kubernetes
        Flux<CloudPlatformDTO> platforms = cloudPlatformService.findCloudPlatforms(
            userId, 0, 100, null, "KUBERNETES", "CONNECTED", null)
            .flatMapMany(pageResult -> Flux.fromIterable(pageResult.getContent()));
            
        return Mono.just(ResponseEntity.ok(ApiResponse.success("获取云平台列表成功", platforms)));
    }

    /**
     * 设置当前DevOps任务使用的云平台
     * @param cloudPlatformId 云平台ID
     * @param authentication 认证信息
     * @return 设置结果
     */
    @PostMapping("/{cloudPlatformId}/select")
    public Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> selectCloudPlatform(
            @PathVariable Long cloudPlatformId,
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        
        return tektonExecutorService.setCloudPlatform(userId, cloudPlatformId)
                .map(success -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("success", success);
                    result.put("cloudPlatformId", cloudPlatformId);
                    result.put("message", success ? "云平台切换成功" : "云平台切换失败");
                    
                    if (success) {
                        return ResponseEntity.ok(ApiResponse.success("云平台切换成功", result));
                    } else {
                        return ResponseEntity.badRequest()
                            .body(ApiResponse.error("云平台切换失败", result));
                    }
                });
    }

    /**
     * 获取当前使用的云平台信息
     * @param authentication 认证信息
     * @return 当前云平台信息
     */
    @GetMapping("/current")
    public Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> getCurrentCloudPlatform(
            Authentication authentication) {
        
        String currentPlatformId = tektonExecutorService.getCurrentCloudPlatformId();
        
        Map<String, Object> result = new HashMap<>();
        result.put("currentCloudPlatformId", currentPlatformId);
        
        if (currentPlatformId != null) {
            Long userId = authUtils.getUserIdFromAuthentication(authentication);
            return cloudPlatformService.findCloudPlatformById(userId, Long.valueOf(currentPlatformId))
                    .map(platform -> {
                        result.put("platform", platform);
                        return ResponseEntity.ok(ApiResponse.success("获取当前云平台成功", result));
                    })
                    .onErrorReturn(ResponseEntity.ok(ApiResponse.success("获取当前云平台成功", result)));
        } else {
            result.put("message", "未设置云平台");
            return Mono.just(ResponseEntity.ok(ApiResponse.success("获取当前云平台成功", result)));
        }
    }

    /**
     * 测试云平台连接状态
     * @param cloudPlatformId 云平台ID
     * @param authentication 认证信息
     * @return 连接测试结果
     */
    @PostMapping("/{cloudPlatformId}/test")
    public Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> testCloudPlatformConnection(
            @PathVariable Long cloudPlatformId,
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        
        return tektonExecutorService.setCloudPlatform(userId, cloudPlatformId)
                .flatMap(setupSuccess -> {
                    if (!setupSuccess) {
                        Map<String, Object> result = new HashMap<>();
                        result.put("connected", false);
                        result.put("message", "云平台连接设置失败");
                        return Mono.just(ResponseEntity.badRequest()
                            .body(ApiResponse.error("连接测试失败", result)));
                    }
                    
                    // 获取执行器信息来验证连接
                    return tektonExecutorService.getExecutorInfo()
                            .map(info -> {
                                Map<String, Object> result = new HashMap<>();
                                result.put("connected", info.getOrDefault("connected", false));
                                result.put("platform", info.getOrDefault("platform", "unknown"));
                                result.put("kubernetesVersion", info.getOrDefault("kubernetesVersion", "unknown"));
                                result.put("capabilities", info.getOrDefault("capabilities", new HashMap<>()));
                                
                                return ResponseEntity.ok(ApiResponse.success("连接测试成功", result));
                            });
                });
    }

    /**
     * 获取云平台的执行器信息
     * @param authentication 认证信息
     * @return 执行器信息
     */
    @GetMapping("/executor/info")
    public Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> getExecutorInfo(
            Authentication authentication) {
        
        return tektonExecutorService.getExecutorInfo()
                .map(info -> ResponseEntity.ok(ApiResponse.success("获取执行器信息成功", info)));
    }
}