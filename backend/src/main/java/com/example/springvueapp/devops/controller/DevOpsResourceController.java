package com.example.springvueapp.devops.controller;

import com.example.springvueapp.common.auth.AuthUtils;
import com.example.springvueapp.devops.model.DevOpsResource;
import com.example.springvueapp.devops.service.DevOpsResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import jakarta.validation.Valid;

/**
 * DevOps资源管理控制器
 */
@RestController
@RequestMapping("/api/devops/resources")
public class DevOpsResourceController {

    private final DevOpsResourceService resourceService;
    private final AuthUtils authUtils;

    @Autowired
    public DevOpsResourceController(DevOpsResourceService resourceService, AuthUtils authUtils) {
        this.resourceService = resourceService;
        this.authUtils = authUtils;
    }

    /**
     * 创建新资源
     * @param resource 资源信息
     * @param authentication 认证信息
     * @return 创建的资源
     */
    @PostMapping
    public Mono<ResponseEntity<DevOpsResource>> createResource(
            @Valid @RequestBody DevOpsResource resource,
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        // 确保资源关联的组件存在且用户有权限
        return resourceService.createResource(resource, resource.getComponentId(), userId)
                .map(createdResource -> ResponseEntity.status(HttpStatus.CREATED).body(createdResource))
                .onErrorReturn(ResponseEntity.badRequest().build());
    }

    /**
     * 获取所有资源
     * @param authentication 认证信息
     * @return 资源列表
     */
    @GetMapping
    public Flux<DevOpsResource> getAllResources(Authentication authentication) {
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return resourceService.getAllResources(userId);
    }

    /**
     * 获取指定组件下的所有资源
     * @param componentId 组件ID
     * @param authentication 认证信息
     * @return 资源列表
     */
    @GetMapping("/component/{componentId}")
    public Flux<DevOpsResource> getResourcesByComponent(
            @PathVariable Long componentId,
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return resourceService.getResourcesByComponent(componentId, userId);
    }

    /**
     * 根据ID获取资源
     * @param resourceId 资源ID
     * @param authentication 认证信息
     * @return 资源信息
     */
    @GetMapping("/{resourceId}")
    public Mono<ResponseEntity<DevOpsResource>> getResourceById(
            @PathVariable Long resourceId,
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return resourceService.getResourceById(resourceId, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.notFound().build());
    }

    /**
     * 更新资源
     * @param resourceId 资源ID
     * @param resource 更新的资源信息
     * @param authentication 认证信息
     * @return 更新后的资源
     */
    @PutMapping("/{resourceId}")
    public Mono<ResponseEntity<DevOpsResource>> updateResource(
            @PathVariable Long resourceId,
            @Valid @RequestBody DevOpsResource resource,
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return resourceService.updateResource(resourceId, resource, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.badRequest().build());
    }

    /**
     * 删除资源
     * @param resourceId 资源ID
     * @param authentication 认证信息
     * @return 删除结果
     */
    @DeleteMapping("/{resourceId}")
    public Mono<ResponseEntity<Void>> deleteResource(
            @PathVariable Long resourceId,
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return resourceService.deleteResource(resourceId, userId)
                .then(Mono.just(ResponseEntity.noContent().<Void>build()))
                .onErrorReturn(ResponseEntity.notFound().build());
    }

    /**
     * 根据类型获取资源
     * @param type 资源类型
     * @param authentication 认证信息
     * @return 资源列表
     */
    @GetMapping("/type/{type}")
    public Flux<DevOpsResource> getResourcesByType(
            @PathVariable String type,
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return resourceService.getResourcesByType(userId, type);
    }

    /**
     * 搜索资源
     * @param keyword 搜索关键字
     * @param authentication 认证信息
     * @return 资源列表
     */
    @GetMapping("/search")
    public Flux<DevOpsResource> searchResources(
            @RequestParam String keyword,
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return resourceService.searchResources(userId, keyword);
    }

    /**
     * 在指定组件中搜索资源
     * @param componentId 组件ID
     * @param keyword 搜索关键字
     * @param authentication 认证信息
     * @return 资源列表
     */
    @GetMapping("/component/{componentId}/search")
    public Flux<DevOpsResource> searchResourcesInComponent(
            @PathVariable Long componentId,
            @RequestParam String keyword,
            Authentication authentication) {
        
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return resourceService.searchResourcesInComponent(componentId, userId, keyword);
    }

    /**
     * 获取资源类型列表
     * @param authentication 认证信息
     * @return 资源类型列表
     */
    @GetMapping("/types")
    public Flux<String> getResourceTypes(Authentication authentication) {
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return resourceService.getAllResourceTypes(userId);
    }

    /**
     * 从认证信息中获取用户ID
     * @param authentication 认证信息
     * @return 用户ID
     */
}