package com.example.springvueapp.devops.controller;

import com.example.springvueapp.common.auth.AuthUtils;
import com.example.springvueapp.devops.model.DevOpsCdTask;
import com.example.springvueapp.devops.model.DevOpsCdTaskInstance;
import com.example.springvueapp.devops.service.CdTaskService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * DevOps CD任务管理控制器
 */
@RestController
@RequestMapping("/api/devops/cd/tasks")
public class DevOpsCdTaskController {

    private final CdTaskService cdTaskService;

    private final AuthUtils authUtils;

    @Autowired
    public DevOpsCdTaskController(CdTaskService cdTaskService, AuthUtils authUtils) {
        this.cdTaskService = cdTaskService;
        this.authUtils = authUtils;
    }

    /**
     * 在指定应用中创建新CD任务
     *
     * @param applicationId  应用ID
     * @param cdTask         CD任务信息
     * @param authentication 认证信息
     * @return 创建的CD任务
     */
    @PostMapping("/application/{applicationId}")
    public Mono<ResponseEntity<DevOpsCdTask>> createCdTask(
            @PathVariable Long applicationId,
            @Valid @RequestBody DevOpsCdTask cdTask,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.createCdTask(cdTask, applicationId, userId)
                .map(createdTask -> ResponseEntity.status(HttpStatus.CREATED).body(createdTask))
                .onErrorReturn(IllegalArgumentException.class,
                        ResponseEntity.badRequest().build());
    }

    /**
     * 获取所有CD任务
     *
     * @param authentication 认证信息
     * @return CD任务列表
     */
    @GetMapping
    public Flux<DevOpsCdTask> getAllCdTasks(Authentication authentication) {
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.getAllCdTasks(userId);
    }

    /**
     * 获取指定应用下的所有CD任务
     *
     * @param applicationId  应用ID
     * @param authentication 认证信息
     * @return CD任务列表
     */
    @GetMapping("/application/{applicationId}")
    public Flux<DevOpsCdTask> getCdTasksByApplication(
            @PathVariable Long applicationId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.getCdTasksByApplication(applicationId, userId)
                .onErrorResume(IllegalArgumentException.class,
                        error -> Flux.empty());
    }

    /**
     * 根据ID获取CD任务
     *
     * @param taskId         CD任务ID
     * @param authentication 认证信息
     * @return CD任务信息
     */
    @GetMapping("/{taskId}")
    public Mono<ResponseEntity<DevOpsCdTask>> getCdTaskById(
            @PathVariable Long taskId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.getCdTaskById(taskId, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class,
                        ResponseEntity.notFound().build());
    }

    /**
     * 更新CD任务
     *
     * @param taskId         CD任务ID
     * @param cdTask         更新的CD任务信息
     * @param authentication 认证信息
     * @return 更新后的CD任务
     */
    @PutMapping("/{taskId}")
    public Mono<ResponseEntity<DevOpsCdTask>> updateCdTask(
            @PathVariable Long taskId,
            @Valid @RequestBody DevOpsCdTask cdTask,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.updateCdTask(taskId, cdTask, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class,
                        ResponseEntity.badRequest().build());
    }

    /**
     * 删除CD任务
     *
     * @param taskId         CD任务ID
     * @param authentication 认证信息
     * @return 删除结果
     */
    @DeleteMapping("/{taskId}")
    public Mono<ResponseEntity<Void>> deleteCdTask(
            @PathVariable Long taskId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.deleteCdTask(taskId, userId)
                .map(deleted -> deleted ?
                        ResponseEntity.noContent().<Void>build() :
                        ResponseEntity.notFound().<Void>build())
                .onErrorReturn(IllegalArgumentException.class,
                        ResponseEntity.notFound().build());
    }

    /**
     * 启动CD任务
     *
     * @param taskId         CD任务ID
     * @param parameters     启动参数
     * @param authentication 认证信息
     * @return 部署结果
     */
    @PostMapping("/{taskId}/start")
    public Mono<ResponseEntity<Map<String, Object>>> startCdTask(
            @PathVariable Long taskId,
            @RequestBody(required = false) Map<String, Object> parameters,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.startCdTask(taskId, userId, parameters)
                .map(result -> ResponseEntity.status(HttpStatus.CREATED).body(result))
                .onErrorReturn(IllegalArgumentException.class,
                        ResponseEntity.badRequest().build());
    }

    /**
     * 停止CD任务
     *
     * @param taskId         CD任务ID
     * @param authentication 认证信息
     * @return 停止结果
     */
    @PostMapping("/{taskId}/stop")
    public Mono<ResponseEntity<Void>> stopCdTask(
            @PathVariable Long taskId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.stopCdTask(taskId, userId)
                .map(stopped -> stopped ?
                        ResponseEntity.ok().<Void>build() :
                        ResponseEntity.notFound().<Void>build())
                .onErrorReturn(IllegalArgumentException.class,
                        ResponseEntity.notFound().build());
    }

    /**
     * 获取CD任务状态
     *
     * @param taskId         CD任务ID
     * @param authentication 认证信息
     * @return 任务状态信息
     */
    @GetMapping("/{taskId}/status")
    public Mono<ResponseEntity<Map<String, Object>>> getCdTaskStatus(
            @PathVariable Long taskId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.getCdTaskStatus(taskId, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class,
                        ResponseEntity.notFound().build());
    }

    /**
     * 获取CD任务部署历史
     *
     * @param taskId         CD任务ID
     * @param authentication 认证信息
     * @return 部署历史列表
     */
    @GetMapping("/{taskId}/history")
    public Flux<Map<String, Object>> getCdTaskDeploymentHistory(
            @PathVariable Long taskId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.getCdTaskDeploymentHistory(taskId, userId)
                .onErrorResume(IllegalArgumentException.class,
                        error -> Flux.empty());
    }

    /**
     * 回滚CD任务到指定版本
     *
     * @param taskId         CD任务ID
     * @param targetVersion  目标版本
     * @param authentication 认证信息
     * @return 回滚结果
     */
    @PostMapping("/{taskId}/rollback")
    public Mono<ResponseEntity<Map<String, Object>>> rollbackCdTask(
            @PathVariable Long taskId,
            @RequestParam String targetVersion,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.rollbackCdTask(taskId, userId, targetVersion)
                .map(result -> ResponseEntity.status(HttpStatus.CREATED).body(result))
                .onErrorReturn(IllegalArgumentException.class,
                        ResponseEntity.badRequest().build());
    }

    /**
     * 生成部署命令
     *
     * @param taskId         CD任务ID
     * @param parameters     部署参数
     * @param authentication 认证信息
     * @return 部署命令
     */
    @PostMapping("/{taskId}/command")
    public Mono<ResponseEntity<String>> generateDeploymentCommand(
            @PathVariable Long taskId,
            @RequestBody(required = false) Map<String, Object> parameters,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.getCdTaskById(taskId, userId)
                .flatMap(cdTask -> cdTaskService.generateDeploymentCommand(cdTask, parameters))
                .map(command -> ResponseEntity.ok().body(command))
                .onErrorReturn(IllegalArgumentException.class,
                        ResponseEntity.notFound().build());
    }

    /**
     * 获取资源包信息
     *
     * @param componentVersions 组件版本信息
     * @return 资源包信息
     */
    @PostMapping("/packages")
    public Mono<ResponseEntity<Map<String, Object>>> getResourcePackages(
            @RequestBody Map<String, String> componentVersions) {

        return cdTaskService.getResourcePackages(componentVersions)
                .map(ResponseEntity::ok);
    }

    /**
     * 验证CD任务配置
     *
     * @param configuration 配置信息
     * @return 验证结果
     */
    @PostMapping("/validate")
    public Mono<ResponseEntity<Boolean>> validateConfiguration(
            @RequestBody Map<String, Object> configuration) {

        return cdTaskService.validateConfiguration(configuration)
                .map(ResponseEntity::ok);
    }

    /**
     * 获取部署模板
     *
     * @param templateType 模板类型
     * @return 模板内容
     */
    @GetMapping("/templates/{templateType}")
    public Mono<ResponseEntity<Map<String, Object>>> getDeploymentTemplate(
            @PathVariable String templateType) {

        return cdTaskService.getDeploymentTemplate(templateType)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class,
                        ResponseEntity.notFound().build());
    }

    /**
     * 获取支持的部署类型
     *
     * @return 部署类型列表
     */
    @GetMapping("/deployment-types")
    public Flux<String> getSupportedDeploymentTypes() {
        return cdTaskService.getSupportedDeploymentTypes();
    }

    /**
     * 检查CD平台连接状态
     *
     * @return 连接状态
     */
    @GetMapping("/platform/status")
    public Mono<ResponseEntity<Boolean>> checkConnection() {
        return cdTaskService.checkConnection()
                .map(ResponseEntity::ok);
    }

    /**
     * 获取CD平台信息
     *
     * @return 平台信息
     */
    @GetMapping("/platform/info")
    public Mono<ResponseEntity<Map<String, Object>>> getPlatformInfo() {
        return cdTaskService.getPlatformInfo()
                .map(ResponseEntity::ok);
    }

    /**
     * 获取CD任务的所有实例
     *
     * @param taskId         CD任务ID
     * @param authentication 认证信息
     * @return 任务实例列表
     */
    @GetMapping("/{taskId}/instances")
    public Flux<DevOpsCdTaskInstance> getCdTaskInstances(
            @PathVariable Long taskId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.getCdTaskInstances(taskId, userId)
                .onErrorResume(IllegalArgumentException.class,
                        error -> Flux.empty());
    }

    /**
     * 获取CD任务实例状态
     *
     * @param instanceId     实例ID
     * @param authentication 认证信息
     * @return 任务实例信息
     */
    @GetMapping("/instances/{instanceId}")
    public Mono<ResponseEntity<DevOpsCdTaskInstance>> getCdTaskInstanceStatus(
            @PathVariable String instanceId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.getCdTaskInstanceStatus(instanceId, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class,
                        ResponseEntity.notFound().build());
    }

    /**
     * 停止CD任务实例
     *
     * @param instanceId     实例ID
     * @param authentication 认证信息
     * @return 停止结果
     */
    @PostMapping("/instances/{instanceId}/stop")
    public Mono<ResponseEntity<Void>> stopCdTaskInstance(
            @PathVariable String instanceId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.stopCdTaskInstance(instanceId, userId)
                .map(stopped -> stopped ?
                        ResponseEntity.ok().<Void>build() :
                        ResponseEntity.notFound().<Void>build())
                .onErrorReturn(IllegalArgumentException.class,
                        ResponseEntity.notFound().build());
    }

    /**
     * 取消CD任务实例
     *
     * @param instanceId     实例ID
     * @param authentication 认证信息
     * @return 取消结果
     */
    @PostMapping("/instances/{instanceId}/cancel")
    public Mono<ResponseEntity<Void>> cancelCdTaskInstance(
            @PathVariable String instanceId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.cancelCdTaskInstance(instanceId, userId)
                .map(cancelled -> cancelled ?
                        ResponseEntity.ok().<Void>build() :
                        ResponseEntity.notFound().<Void>build())
                .onErrorReturn(IllegalArgumentException.class,
                        ResponseEntity.notFound().build());
    }

    /**
     * 获取CD任务实例日志
     *
     * @param instanceId     实例ID
     * @param authentication 认证信息
     * @return 日志内容
     */
    @GetMapping("/instances/{instanceId}/logs")
    public Mono<ResponseEntity<String>> getCdTaskInstanceLogs(
            @PathVariable String instanceId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return cdTaskService.getCdTaskInstanceLogs(instanceId, userId)
                .map(logs -> ResponseEntity.ok().body(logs))
                .onErrorReturn(IllegalArgumentException.class,
                        ResponseEntity.notFound().build());
    }

    /**
     * 清理已完成的CD任务实例
     *
     * @param taskId         CD任务ID
     * @param keepCount      保留的实例数量
     * @param authentication 认证信息
     * @return 清理的实例数量
     */
    @PostMapping("/{taskId}/cleanup")
    public Mono<ResponseEntity<Integer>> cleanupCompletedInstances(
            @PathVariable Long taskId,
            @RequestBody Map<String, Object> params,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        int keepCount = params.get("keepCount") != null ?
                Integer.parseInt(params.get("keepCount").toString()) : 10;

        return cdTaskService.cleanupCompletedInstances(taskId, userId, keepCount)
                .map(ResponseEntity::ok)
                .onErrorReturn(IllegalArgumentException.class,
                        ResponseEntity.badRequest().build());
    }
}
