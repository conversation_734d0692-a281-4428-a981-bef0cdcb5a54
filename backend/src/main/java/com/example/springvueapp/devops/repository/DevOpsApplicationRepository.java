package com.example.springvueapp.devops.repository;

import com.example.springvueapp.devops.entity.DevOpsApplicationEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * DevOps应用的响应式Repository接口
 */
@Repository
public interface DevOpsApplicationRepository extends ReactiveCrudRepository<DevOpsApplicationEntity, Long> {

    /**
     * 根据用户ID查找所有应用
     * @param userId 用户ID
     * @return 应用列表
     */
    Flux<DevOpsApplicationEntity> findByUserId(Long userId);



    /**
     * 根据用户ID和应用名称查找应用
     * @param userId 用户ID
     * @param name 应用名称
     * @return 应用实体
     */
    Mono<DevOpsApplicationEntity> findByUserIdAndName(Long userId, String name);


    /**
     * 根据用户ID和状态查找应用
     * @param userId 用户ID
     * @param status 应用状态
     * @return 应用列表
     */
    Flux<DevOpsApplicationEntity> findByUserIdAndStatus(Long userId, String status);


    /**
     * 根据用户ID和应用ID查找应用
     * @param userId 用户ID
     * @param id 应用ID
     * @return 应用实体
     */
    Mono<DevOpsApplicationEntity> findByUserIdAndId(Long userId, Long id);

    /**
     * 根据用户ID删除应用
     * @param userId 用户ID
     * @param id 应用ID
     * @return 删除的行数
     */
    @Query("DELETE FROM devops_applications WHERE user_id = :userId AND id = :id")
    Mono<Integer> deleteByUserIdAndId(Long userId, Long id);


    /**
     * 统计用户的应用数量
     * @param userId 用户ID
     * @return 应用数量
     */
    Mono<Long> countByUserId(Long userId);

    /**
     * 根据名称模糊查询用户的应用
     * @param userId 用户ID
     * @param namePattern 名称模式
     * @return 应用列表
     */
    @Query("SELECT * FROM devops_applications WHERE user_id = :userId AND name LIKE :namePattern ORDER BY created_at DESC")
    Flux<DevOpsApplicationEntity> findByUserIdAndNameContaining(Long userId, String namePattern);

}
