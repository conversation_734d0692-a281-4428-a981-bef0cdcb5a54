package com.example.springvueapp.devops.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Tekton YAML配置构建器
 * 专门用于生成和验证Tekton Task和Pipeline的YAML配置
 * 支持CI和CD任务的配置组装
 */
@Component
public class TektonYamlConfigBuilder {

    private static final Logger logger = LoggerFactory.getLogger(TektonYamlConfigBuilder.class);
    
    private final ObjectMapper yamlMapper;

    public TektonYamlConfigBuilder() {
        this.yamlMapper = new ObjectMapper(new YAMLFactory());
    }

    // ==================== CI任务配置生成 ====================

    /**
     * 构建CI任务的Task配置
     * @param taskName 任务名称
     * @param taskType 任务类型 (build, test, package等)
     * @param configuration 任务配置参数
     * @return Task YAML配置
     */
    public Map<String, Object> buildCiTaskConfig(String taskName, String taskType, Map<String, Object> configuration) {
        Map<String, Object> taskConfig = new HashMap<>();
        
        // 基础元数据
        taskConfig.put("apiVersion", "tekton.dev/v1beta1");
        taskConfig.put("kind", "Task");
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("name", sanitizeResourceName(taskName));
        metadata.put("labels", Map.of(
            "app.kubernetes.io/name", "ci-task",
            "app.kubernetes.io/component", taskType,
            "tekton.dev/task-type", "ci"
        ));
        taskConfig.put("metadata", metadata);

        // 任务规格
        Map<String, Object> spec = buildCiTaskSpec(taskType, configuration);
        taskConfig.put("spec", spec);

        return taskConfig;
    }

    /**
     * 构建CI任务的TaskRun配置
     * @param taskName 任务名称
     * @param instanceId 实例ID
     * @param parameters 运行参数
     * @return TaskRun YAML配置
     */
    public Map<String, Object> buildCiTaskRunConfig(String taskName, String instanceId, Map<String, Object> parameters) {
        Map<String, Object> taskRunConfig = new HashMap<>();
        
        // 基础元数据
        taskRunConfig.put("apiVersion", "tekton.dev/v1beta1");
        taskRunConfig.put("kind", "TaskRun");
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("name", sanitizeResourceName(instanceId));
        metadata.put("labels", Map.of(
            "tekton.dev/task", sanitizeResourceName(taskName),
            "app.kubernetes.io/name", "ci-taskrun",
            "tekton.dev/task-type", "ci"
        ));
        taskRunConfig.put("metadata", metadata);

        // TaskRun规格
        Map<String, Object> spec = new HashMap<>();
        spec.put("taskRef", Map.of("name", sanitizeResourceName(taskName)));
        
        // 添加参数
        if (parameters != null && !parameters.isEmpty()) {
            List<Map<String, Object>> params = new ArrayList<>();
            parameters.forEach((key, value) -> {
                params.add(Map.of(
                    "name", key,
                    "value", value.toString()
                ));
            });
            spec.put("params", params);
        }

        taskRunConfig.put("spec", spec);
        return taskRunConfig;
    }

    // ==================== CD任务配置生成 ====================

    /**
     * 构建CD任务的Pipeline配置
     * @param pipelineName Pipeline名称
     * @param configuration 部署配置参数
     * @return Pipeline YAML配置
     */
    public Map<String, Object> buildCdPipelineConfig(String pipelineName, Map<String, Object> configuration) {
        Map<String, Object> pipelineConfig = new HashMap<>();
        
        // 基础元数据
        pipelineConfig.put("apiVersion", "tekton.dev/v1beta1");
        pipelineConfig.put("kind", "Pipeline");
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("name", sanitizeResourceName(pipelineName));
        metadata.put("labels", Map.of(
            "app.kubernetes.io/name", "cd-pipeline",
            "app.kubernetes.io/component", "deployment",
            "tekton.dev/pipeline-type", "cd"
        ));
        pipelineConfig.put("metadata", metadata);

        // Pipeline规格
        Map<String, Object> spec = buildCdPipelineSpec(configuration);
        pipelineConfig.put("spec", spec);

        return pipelineConfig;
    }

    /**
     * 构建CD任务的PipelineRun配置
     * @param pipelineName Pipeline名称
     * @param instanceId 实例ID
     * @param parameters 运行参数
     * @return PipelineRun YAML配置
     */
    public Map<String, Object> buildCdPipelineRunConfig(String pipelineName, String instanceId, Map<String, Object> parameters) {
        Map<String, Object> pipelineRunConfig = new HashMap<>();
        
        // 基础元数据
        pipelineRunConfig.put("apiVersion", "tekton.dev/v1beta1");
        pipelineRunConfig.put("kind", "PipelineRun");
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("name", sanitizeResourceName(instanceId));
        metadata.put("labels", Map.of(
            "tekton.dev/pipeline", sanitizeResourceName(pipelineName),
            "app.kubernetes.io/name", "cd-pipelinerun",
            "tekton.dev/pipeline-type", "cd"
        ));
        pipelineRunConfig.put("metadata", metadata);

        // PipelineRun规格
        Map<String, Object> spec = new HashMap<>();
        spec.put("pipelineRef", Map.of("name", sanitizeResourceName(pipelineName)));
        
        // 添加参数
        if (parameters != null && !parameters.isEmpty()) {
            List<Map<String, Object>> params = new ArrayList<>();
            parameters.forEach((key, value) -> {
                params.add(Map.of(
                    "name", key,
                    "value", value.toString()
                ));
            });
            spec.put("params", params);
        }

        pipelineRunConfig.put("spec", spec);
        return pipelineRunConfig;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建CI任务规格
     */
    private Map<String, Object> buildCiTaskSpec(String taskType, Map<String, Object> configuration) {
        Map<String, Object> spec = new HashMap<>();
        
        // 参数定义
        List<Map<String, Object>> params = buildCiTaskParams(taskType);
        if (!params.isEmpty()) {
            spec.put("params", params);
        }

        // 步骤定义
        List<Map<String, Object>> steps = buildCiTaskSteps(taskType, configuration);
        spec.put("steps", steps);

        // 工作空间定义
        List<Map<String, Object>> workspaces = buildCiTaskWorkspaces();
        if (!workspaces.isEmpty()) {
            spec.put("workspaces", workspaces);
        }

        return spec;
    }

    /**
     * 构建CI任务参数
     */
    private List<Map<String, Object>> buildCiTaskParams(String taskType) {
        List<Map<String, Object>> params = new ArrayList<>();
        
        // 通用参数
        params.add(Map.of(
            "name", "source-url",
            "type", "string",
            "description", "源代码仓库URL"
        ));
        
        params.add(Map.of(
            "name", "source-revision",
            "type", "string",
            "description", "源代码版本",
            "default", "main"
        ));

        // 根据任务类型添加特定参数
        switch (taskType.toLowerCase()) {
            case "build":
                params.add(Map.of(
                    "name", "build-image",
                    "type", "string",
                    "description", "构建镜像",
                    "default", "maven:3.8-openjdk-17"
                ));
                break;
            case "test":
                params.add(Map.of(
                    "name", "test-image",
                    "type", "string",
                    "description", "测试镜像",
                    "default", "maven:3.8-openjdk-17"
                ));
                break;
            case "package":
                params.add(Map.of(
                    "name", "registry-url",
                    "type", "string",
                    "description", "镜像仓库URL"
                ));
                break;
        }

        return params;
    }

    /**
     * 构建CI任务步骤
     */
    private List<Map<String, Object>> buildCiTaskSteps(String taskType, Map<String, Object> configuration) {
        List<Map<String, Object>> steps = new ArrayList<>();

        // 源代码克隆步骤
        steps.add(Map.of(
            "name", "git-clone",
            "image", "alpine/git:latest",
            "workingDir", "/workspace/source",
            "script", buildGitCloneScript()
        ));

        // 根据任务类型添加特定步骤
        switch (taskType.toLowerCase()) {
            case "build":
                steps.addAll(buildBuildSteps(configuration));
                break;
            case "test":
                steps.addAll(buildTestSteps(configuration));
                break;
            case "package":
                steps.addAll(buildPackageSteps(configuration));
                break;
            default:
                steps.add(buildDefaultStep());
        }

        return steps;
    }

    /**
     * 构建CI任务工作空间
     */
    private List<Map<String, Object>> buildCiTaskWorkspaces() {
        List<Map<String, Object>> workspaces = new ArrayList<>();
        
        workspaces.add(Map.of(
            "name", "source",
            "description", "源代码工作空间"
        ));
        
        workspaces.add(Map.of(
            "name", "cache",
            "description", "构建缓存工作空间",
            "optional", true
        ));

        return workspaces;
    }

    /**
     * 构建CD Pipeline规格
     */
    private Map<String, Object> buildCdPipelineSpec(Map<String, Object> configuration) {
        Map<String, Object> spec = new HashMap<>();
        
        // 参数定义
        List<Map<String, Object>> params = buildCdPipelineParams();
        spec.put("params", params);

        // 任务定义
        List<Map<String, Object>> tasks = buildCdPipelineTasks(configuration);
        spec.put("tasks", tasks);

        // 工作空间定义
        List<Map<String, Object>> workspaces = buildCdPipelineWorkspaces();
        spec.put("workspaces", workspaces);

        return spec;
    }

    /**
     * 构建CD Pipeline参数
     */
    private List<Map<String, Object>> buildCdPipelineParams() {
        List<Map<String, Object>> params = new ArrayList<>();
        
        params.add(Map.of(
            "name", "image-url",
            "type", "string",
            "description", "部署镜像URL"
        ));
        
        params.add(Map.of(
            "name", "target-environment",
            "type", "string",
            "description", "目标环境",
            "default", "staging"
        ));
        
        params.add(Map.of(
            "name", "deployment-strategy",
            "type", "string",
            "description", "部署策略",
            "default", "rolling_update"
        ));

        return params;
    }

    /**
     * 构建CD Pipeline任务
     */
    private List<Map<String, Object>> buildCdPipelineTasks(Map<String, Object> configuration) {
        List<Map<String, Object>> tasks = new ArrayList<>();

        // 部署前检查任务
        tasks.add(Map.of(
            "name", "pre-deploy-check",
            "taskRef", Map.of("name", "pre-deploy-check-task"),
            "params", List.of(
                Map.of("name", "target-environment", "value", "$(params.target-environment)")
            )
        ));

        // 部署任务
        tasks.add(Map.of(
            "name", "deploy",
            "taskRef", Map.of("name", "deploy-task"),
            "runAfter", List.of("pre-deploy-check"),
            "params", List.of(
                Map.of("name", "image-url", "value", "$(params.image-url)"),
                Map.of("name", "target-environment", "value", "$(params.target-environment)"),
                Map.of("name", "deployment-strategy", "value", "$(params.deployment-strategy)")
            )
        ));

        // 部署后验证任务
        tasks.add(Map.of(
            "name", "post-deploy-verify",
            "taskRef", Map.of("name", "post-deploy-verify-task"),
            "runAfter", List.of("deploy"),
            "params", List.of(
                Map.of("name", "target-environment", "value", "$(params.target-environment)")
            )
        ));

        return tasks;
    }

    /**
     * 构建CD Pipeline工作空间
     */
    private List<Map<String, Object>> buildCdPipelineWorkspaces() {
        List<Map<String, Object>> workspaces = new ArrayList<>();

        workspaces.add(Map.of(
            "name", "shared-data",
            "description", "共享数据工作空间"
        ));

        return workspaces;
    }

    // ==================== 步骤构建辅助方法 ====================

    /**
     * 构建Git克隆脚本
     */
    private String buildGitCloneScript() {
        return """
            #!/bin/sh
            set -e
            echo "正在克隆源代码..."
            git clone $(params.source-url) .
            git checkout $(params.source-revision)
            echo "源代码克隆完成"
            """;
    }

    /**
     * 构建构建步骤
     */
    private List<Map<String, Object>> buildBuildSteps(Map<String, Object> configuration) {
        List<Map<String, Object>> steps = new ArrayList<>();

        steps.add(Map.of(
            "name", "maven-build",
            "image", "$(params.build-image)",
            "workingDir", "/workspace/source",
            "script", buildMavenBuildScript(configuration)
        ));

        return steps;
    }

    /**
     * 构建测试步骤
     */
    private List<Map<String, Object>> buildTestSteps(Map<String, Object> configuration) {
        List<Map<String, Object>> steps = new ArrayList<>();

        steps.add(Map.of(
            "name", "maven-test",
            "image", "$(params.test-image)",
            "workingDir", "/workspace/source",
            "script", buildMavenTestScript(configuration)
        ));

        return steps;
    }

    /**
     * 构建打包步骤
     */
    private List<Map<String, Object>> buildPackageSteps(Map<String, Object> configuration) {
        List<Map<String, Object>> steps = new ArrayList<>();

        steps.add(Map.of(
            "name", "docker-build",
            "image", "gcr.io/kaniko-project/executor:latest",
            "workingDir", "/workspace/source",
            "script", buildDockerBuildScript(configuration)
        ));

        return steps;
    }

    /**
     * 构建默认步骤
     */
    private Map<String, Object> buildDefaultStep() {
        return Map.of(
            "name", "default-step",
            "image", "alpine:latest",
            "script", """
                #!/bin/sh
                echo "执行默认任务步骤"
                ls -la /workspace/source
                """
        );
    }

    /**
     * 构建Maven构建脚本
     */
    private String buildMavenBuildScript(Map<String, Object> configuration) {
        StringBuilder script = new StringBuilder();
        script.append("#!/bin/sh\n");
        script.append("set -e\n");
        script.append("echo \"开始Maven构建...\"\n");

        // 检查是否有自定义Maven目标
        String mavenGoals = (String) configuration.getOrDefault("maven.goals", "clean compile");
        script.append("mvn ").append(mavenGoals).append("\n");

        script.append("echo \"Maven构建完成\"\n");
        return script.toString();
    }

    /**
     * 构建Maven测试脚本
     */
    private String buildMavenTestScript(Map<String, Object> configuration) {
        StringBuilder script = new StringBuilder();
        script.append("#!/bin/sh\n");
        script.append("set -e\n");
        script.append("echo \"开始Maven测试...\"\n");

        // 检查是否有自定义测试配置
        String testProfile = (String) configuration.getOrDefault("test.profile", "");
        if (!testProfile.isEmpty()) {
            script.append("mvn test -P").append(testProfile).append("\n");
        } else {
            script.append("mvn test\n");
        }

        script.append("echo \"Maven测试完成\"\n");
        return script.toString();
    }

    /**
     * 构建Docker构建脚本
     */
    private String buildDockerBuildScript(Map<String, Object> configuration) {
        StringBuilder script = new StringBuilder();
        script.append("#!/busybox/sh\n");
        script.append("set -e\n");
        script.append("echo \"开始Docker镜像构建...\"\n");

        String dockerfile = (String) configuration.getOrDefault("docker.file", "Dockerfile");
        script.append("/kaniko/executor --dockerfile=").append(dockerfile);
        script.append(" --destination=$(params.registry-url)\n");

        script.append("echo \"Docker镜像构建完成\"\n");
        return script.toString();
    }

    // ==================== 工具方法 ====================

    /**
     * 清理资源名称，确保符合Kubernetes命名规范
     */
    private String sanitizeResourceName(String name) {
        if (name == null || name.isEmpty()) {
            return "unnamed-resource";
        }

        // 转换为小写，替换非法字符为连字符
        String sanitized = name.toLowerCase()
                .replaceAll("[^a-z0-9-]", "-")
                .replaceAll("-+", "-")
                .replaceAll("^-|-$", "");

        // 确保名称不为空且不超过63个字符
        if (sanitized.isEmpty()) {
            sanitized = "unnamed-resource";
        }
        if (sanitized.length() > 63) {
            sanitized = sanitized.substring(0, 63).replaceAll("-$", "");
        }

        return sanitized;
    }

    /**
     * 将配置对象转换为YAML字符串
     */
    public String toYamlString(Map<String, Object> config) {
        try {
            return yamlMapper.writeValueAsString(config);
        } catch (Exception e) {
            logger.error("转换YAML失败", e);
            throw new RuntimeException("YAML转换失败", e);
        }
    }

    /**
     * 从YAML字符串解析配置对象
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> fromYamlString(String yaml) {
        try {
            return yamlMapper.readValue(yaml, Map.class);
        } catch (Exception e) {
            logger.error("解析YAML失败", e);
            throw new RuntimeException("YAML解析失败", e);
        }
    }

    /**
     * 验证YAML配置的有效性
     */
    public boolean validateYamlConfig(Map<String, Object> config) {
        try {
            // 检查必需字段
            if (!config.containsKey("apiVersion") || !config.containsKey("kind") || !config.containsKey("metadata")) {
                logger.warn("YAML配置缺少必需字段");
                return false;
            }

            // 检查元数据
            @SuppressWarnings("unchecked")
            Map<String, Object> metadata = (Map<String, Object>) config.get("metadata");
            if (!metadata.containsKey("name")) {
                logger.warn("YAML配置元数据缺少name字段");
                return false;
            }

            // 尝试序列化以验证格式
            toYamlString(config);
            return true;
        } catch (Exception e) {
            logger.warn("YAML配置验证失败", e);
            return false;
        }
    }
}
