package com.example.springvueapp.devops.controller;

import com.example.springvueapp.common.auth.AuthUtils;
import com.example.springvueapp.devops.model.DevOpsApplication;
import com.example.springvueapp.devops.service.DevOpsApplicationService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * DevOps应用管理控制器
 */
@RestController
@RequestMapping("/api/devops/applications")
public class DevOpsApplicationController {
    private static final Logger logger = LoggerFactory.getLogger(DevOpsApplicationController.class);

    private final DevOpsApplicationService applicationService;

    private final AuthUtils authUtils;

    @Autowired
    public DevOpsApplicationController(DevOpsApplicationService applicationService, AuthUtils authUtils) {
        this.applicationService = applicationService;
        this.authUtils = authUtils;
    }

    /**
     * 创建新应用
     *
     * @param application    应用信息
     * @param authentication 认证信息
     * @return 创建的应用
     */
    @PostMapping
    public Mono<ResponseEntity<DevOpsApplication>> createApplication(
            @Valid @RequestBody DevOpsApplication application,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return applicationService.createApplication(application, userId)
                .map(createdApplication -> ResponseEntity.status(HttpStatus.CREATED).body(createdApplication))
                .onErrorReturn(e -> {
                    logger.error("", e);
                    return true;
                }, ResponseEntity.badRequest().build());
    }

    /**
     * 获取所有应用
     *
     * @param authentication 认证信息
     * @return 应用列表
     */
    @GetMapping
    public Flux<DevOpsApplication> getAllApplications(Authentication authentication) {
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return applicationService.getAllApplications(userId);
    }

    /**
     * 根据ID获取应用
     *
     * @param applicationId  应用ID
     * @param authentication 认证信息
     * @return 应用信息
     */
    @GetMapping("/{applicationId}")
    public Mono<ResponseEntity<DevOpsApplication>> getApplicationById(
            @PathVariable Long applicationId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return applicationService.getApplicationById(applicationId, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.notFound().build());
    }

    /**
     * 更新应用
     *
     * @param applicationId  应用ID
     * @param application    更新的应用信息
     * @param authentication 认证信息
     * @return 更新后的应用
     */
    @PutMapping("/{applicationId}")
    public Mono<ResponseEntity<DevOpsApplication>> updateApplication(
            @PathVariable Long applicationId,
            @Valid @RequestBody DevOpsApplication application,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return applicationService.updateApplication(applicationId, application, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.badRequest().build());
    }

    /**
     * 删除应用
     *
     * @param applicationId  应用ID
     * @param authentication 认证信息
     * @return 删除结果
     */
    @DeleteMapping("/{applicationId}")
    public Mono<ResponseEntity<Void>> deleteApplication(
            @PathVariable Long applicationId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return applicationService.deleteApplication(applicationId, userId)
                .then(Mono.just(ResponseEntity.noContent().<Void>build()))
                .onErrorReturn(ResponseEntity.notFound().build());
    }

    /**
     * 根据状态获取应用
     *
     * @param status         应用状态
     * @param authentication 认证信息
     * @return 应用列表
     */
    @GetMapping("/status/{status}")
    public Flux<DevOpsApplication> getApplicationsByStatus(
            @PathVariable String status,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return applicationService.getApplicationsByStatus(userId, status);
    }

    /**
     * 搜索应用
     *
     * @param keyword        搜索关键词
     * @param authentication 认证信息
     * @return 应用列表
     */
    @GetMapping("/search")
    public Flux<DevOpsApplication> searchApplications(
            @RequestParam String keyword,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return applicationService.searchApplications(userId, keyword);
    }

    /**
     * 统计应用数量
     *
     * @param authentication 认证信息
     * @return 应用数量
     */
    @GetMapping("/count")
    public Mono<Long> countApplications(Authentication authentication) {
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return applicationService.countApplications(userId);
    }
}