package com.example.springvueapp.devops.controller;

import com.example.springvueapp.common.auth.AuthUtils;
import com.example.springvueapp.devops.model.DevOpsComponent;
import com.example.springvueapp.devops.service.DevOpsComponentService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * DevOps组件管理控制器
 */
@RestController
@RequestMapping("/api/devops/components")
public class DevOpsComponentController {
    private static final Logger logger = LoggerFactory.getLogger(DevOpsComponentController.class);

    private final DevOpsComponentService componentService;

    private final AuthUtils authUtils;

    @Autowired
    public DevOpsComponentController(DevOpsComponentService componentService, AuthUtils authUtils) {
        this.componentService = componentService;
        this.authUtils = authUtils;
    }

    /**
     * 创建新组件
     *
     * @param component      组件信息
     * @param authentication 认证信息
     * @return 创建的组件
     */
    @PostMapping
    public Mono<ResponseEntity<DevOpsComponent>> createComponent(
            @Valid @RequestBody DevOpsComponent component,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return componentService.createComponent(component, userId)
                .map(createdComponent -> ResponseEntity.status(HttpStatus.CREATED).body(createdComponent))
                .onErrorReturn(e -> {
                    logger.error("", e);
                    return true;
                }, ResponseEntity.badRequest().build());
    }

    /**
     * 获取所有组件
     *
     * @param authentication 认证信息
     * @return 组件列表
     */
    @GetMapping
    public Flux<DevOpsComponent> getAllComponents(Authentication authentication) {
        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return componentService.getAllComponents(userId);
    }

    /**
     * 根据ID获取组件
     *
     * @param componentId    组件ID
     * @param authentication 认证信息
     * @return 组件信息
     */
    @GetMapping("/{componentId}")
    public Mono<ResponseEntity<DevOpsComponent>> getComponentById(
            @PathVariable Long componentId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return componentService.getComponentById(componentId, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.notFound().build());
    }

    /**
     * 更新组件
     *
     * @param componentId    组件ID
     * @param component      更新的组件信息
     * @param authentication 认证信息
     * @return 更新后的组件
     */
    @PutMapping("/{componentId}")
    public Mono<ResponseEntity<DevOpsComponent>> updateComponent(
            @PathVariable Long componentId,
            @Valid @RequestBody DevOpsComponent component,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return componentService.updateComponent(componentId, component, userId)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.badRequest().build());
    }

    /**
     * 删除组件
     *
     * @param componentId    组件ID
     * @param authentication 认证信息
     * @return 删除结果
     */
    @DeleteMapping("/{componentId}")
    public Mono<ResponseEntity<Void>> deleteComponent(
            @PathVariable Long componentId,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return componentService.deleteComponent(componentId, userId)
                .then(Mono.just(ResponseEntity.noContent().<Void>build()))
                .onErrorReturn(ResponseEntity.notFound().build());
    }

    /**
     * 根据状态获取组件
     *
     * @param status         组件状态
     * @param authentication 认证信息
     * @return 组件列表
     */
    @GetMapping("/status/{status}")
    public Flux<DevOpsComponent> getComponentsByStatus(
            @PathVariable String status,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return componentService.getComponentsByStatus(userId, status);
    }

    /**
     * 根据仓库类型获取组件
     *
     * @param repositoryType 仓库类型
     * @param authentication 认证信息
     * @return 组件列表
     */
    @GetMapping("/repository-type/{repositoryType}")
    public Flux<DevOpsComponent> getComponentsByRepositoryType(
            @PathVariable String repositoryType,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return componentService.getComponentsByRepositoryType(userId, repositoryType);
    }

    /**
     * 搜索组件
     *
     * @param query          搜索关键字
     * @param authentication 认证信息
     * @return 组件列表
     */
    @GetMapping("/search")
    public Flux<DevOpsComponent> searchComponents(
            @RequestParam String q,
            Authentication authentication) {

        Long userId = authUtils.getUserIdFromAuthentication(authentication);
        return componentService.searchComponents(userId, q);
    }
}