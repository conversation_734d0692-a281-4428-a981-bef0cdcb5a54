package com.example.springvueapp.devops.service;

import com.example.springvueapp.devops.client.TektonClient;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


/**
 * Tekton执行器服务实现
 * 实现ExecutorService抽象接口，作为TektonClient的包装器
 * 提供Tekton任务执行、状态监控和日志管理的统一接口
 * 专注于CRUD操作和日志检索，不包含YAML配置生成逻辑
 */
@Service
public class TektonExecutorService implements ExecutorService {

    private static final Logger logger = LoggerFactory.getLogger(TektonExecutorService.class);

    private final TektonClient tektonClient;

    @Value("${tekton.namespace:default}")
    private String defaultNamespace;

    @Autowired
    public TektonExecutorService(TektonClient tektonClient) {
        this.tektonClient = tektonClient;
    }

    /**
     * 设置云平台连接
     */
    public Mono<Boolean> setCloudPlatform(Long userId, Long cloudPlatformId) {
        return tektonClient.setCloudPlatform(userId, cloudPlatformId);
    }

    /**
     * 获取当前使用的云平台ID
     */
    public String getCurrentCloudPlatformId() {
        return tektonClient.getCurrentCloudPlatformId();
    }
    // ==================== 任务状态管理 ====================

    @Override
    public Mono<Map<String, Object>> getTaskStatus(String taskId) {
        logger.debug("获取任务状态: {}", taskId);

        // 尝试作为TaskRun获取状态
        return tektonClient.getTaskRunStatus(defaultNamespace, taskId)
                .onErrorResume(taskRunError -> {
                    // 如果TaskRun不存在，尝试作为PipelineRun获取状态
                    logger.debug("TaskRun不存在，尝试获取PipelineRun状态: {}", taskId);
                    return tektonClient.getPipelineRunStatus(defaultNamespace, taskId);
                })
                .onErrorResume(pipelineRunError -> {
                    logger.warn("任务不存在: {}", taskId);
                    Map<String, Object> errorStatus = new HashMap<>();
                    errorStatus.put("taskId", taskId);
                    errorStatus.put("status", "NOT_FOUND");
                    errorStatus.put("message", "任务不存在");
                    return Mono.just(errorStatus);
                });
    }

    @Override
    public Mono<Boolean> stopTask(String taskId) {
        logger.info("停止任务: {}", taskId);

        // Tekton不支持直接停止任务，只能删除TaskRun/PipelineRun
        return cancelTask(taskId);
    }

    @Override
    public Mono<Boolean> cancelTask(String taskId) {
        logger.info("取消任务: {}", taskId);

        // 尝试删除TaskRun
        return tektonClient.deleteTaskRun(defaultNamespace, taskId)
                .flatMap(taskRunDeleted -> {
                    if (taskRunDeleted) {
                        return Mono.just(true);
                    } else {
                        // 如果TaskRun删除失败，尝试删除PipelineRun
                        return tektonClient.deletePipelineRun(defaultNamespace, taskId);
                    }
                })
                .onErrorReturn(false);
    }

    @Override
    public Mono<String> getTaskLogs(String taskId) {
        logger.debug("获取任务日志: {}", taskId);

        // 尝试获取TaskRun日志
        return tektonClient.getTaskRunLogs(defaultNamespace, taskId)
                .onErrorResume(taskRunError -> {
                    // 如果TaskRun日志获取失败，尝试获取PipelineRun日志
                    logger.debug("TaskRun日志获取失败，尝试获取PipelineRun日志: {}", taskId);
                    return tektonClient.getPipelineRunLogs(defaultNamespace, taskId);
                })
                .onErrorReturn("无法获取任务日志: " + taskId);
    }

    // ==================== 任务结果管理 ====================

    @Override
    public Mono<Map<String, Object>> getTaskResult(String taskId) {
        logger.debug("获取任务结果: {}", taskId);

        // 获取任务状态，如果任务已完成则返回结果信息
        return getTaskStatus(taskId)
                .map(status -> {
                    Map<String, Object> result = new HashMap<>(status);

                    // 如果任务已完成，添加结果信息
                    String taskStatus = (String) status.get("status");
                    if ("COMPLETED".equals(taskStatus) || "FAILED".equals(taskStatus)) {
                        result.put("completedAt", status.get("completionTime"));
                        result.put("startedAt", status.get("startTime"));

                        // 计算执行时长
                        if (status.get("startTime") != null && status.get("completionTime") != null) {
                            // 这里可以添加时长计算逻辑
                            result.put("duration", "计算中...");
                        }

                        result.put("exitCode", "COMPLETED".equals(taskStatus) ? 0 : 1);
                    }

                    return result;
                });
    }

    // ==================== 任务列表管理 ====================

    @Override
    public Flux<Map<String, Object>> listTasks(String namespace) {
        logger.debug("列出任务，命名空间: {}", namespace);

        String targetNamespace = namespace != null ? namespace : defaultNamespace;

        // 获取TaskRun列表
        Flux<Map<String, Object>> taskRuns = tektonClient.listTaskRuns(targetNamespace, null);

        // 获取PipelineRun列表
        Flux<Map<String, Object>> pipelineRuns = tektonClient.listPipelineRuns(targetNamespace, null);

        // 合并两个列表
        return Flux.concat(taskRuns, pipelineRuns)
                .onErrorResume(error -> {
                    logger.warn("列出任务失败: {}", error.getMessage());
                    return Flux.empty();
                });
    }

    // ==================== 任务清理管理 ====================

    @Override
    public Mono<Integer> cleanupCompletedTasks(String namespace, int keepCount) {
        logger.info("清理已完成任务，命名空间: {}, 保留数量: {}", namespace, keepCount);

        String targetNamespace = namespace != null ? namespace : defaultNamespace;

        // 清理TaskRun和PipelineRun
        Mono<Integer> taskRunCleanup = tektonClient.cleanupCompletedTaskRuns(targetNamespace, null, keepCount);
        Mono<Integer> pipelineRunCleanup = tektonClient.cleanupCompletedPipelineRuns(targetNamespace, null, keepCount);

        return Mono.zip(taskRunCleanup, pipelineRunCleanup)
                .map(tuple -> tuple.getT1() + tuple.getT2())
                .onErrorReturn(0);
    }

    // ==================== 命名空间管理 ====================

    @Override
    public Mono<Boolean> createNamespace(String namespace) {
        logger.info("创建命名空间: {}", namespace);
        return tektonClient.createNamespace(namespace);
    }

    @Override
    public Mono<Boolean> deleteNamespace(String namespace) {
        logger.info("删除命名空间: {}", namespace);
        return tektonClient.deleteNamespace(namespace);
    }

    @Override
    public Flux<String> listNamespaces() {
        logger.debug("列出所有命名空间");
        return tektonClient.listNamespaces();
    }

    // ==================== 连接和信息管理 ====================

    @Override
    public Mono<Boolean> checkConnection() {
        logger.debug("检查Tekton连接状态");
        return tektonClient.checkConnection();
    }

    @Override
    public Mono<Map<String, Object>> getExecutorInfo() {
        logger.debug("获取执行器信息");

        return tektonClient.getClusterInfo()
                .map(clusterInfo -> {
                    Map<String, Object> info = new HashMap<>();
                    info.put("executor", "Tekton");
                    info.put("platform", clusterInfo.getOrDefault("platform", "Tekton"));
                    info.put("connected", clusterInfo.getOrDefault("connected", false));
                    info.put("defaultNamespace", defaultNamespace);
                    info.put("kubernetesVersion", clusterInfo.getOrDefault("kubernetesVersion", "unknown"));
                    info.put("lastChecked", clusterInfo.getOrDefault("lastChecked", LocalDateTime.now().toString()));
                    info.put("capabilities", Map.of(
                        "supportsPipelines", true,
                        "supportsTasks", true,
                        "supportsWorkspaces", true,
                        "supportsResults", true,
                        "supportsLogs", true,
                        "supportsCleanup", true
                    ));

                    return info;
                })
                .onErrorReturn(Map.of(
                    "executor", "Tekton",
                    "connected", false,
                    "error", "无法获取集群信息"
                ));
    }

    // ==================== 验证和支持功能 ====================

    @Override
    public Mono<Boolean> validateDeploymentCommand(String deploymentCommand) {
        logger.debug("验证部署命令: {}", deploymentCommand);

        if (deploymentCommand == null || deploymentCommand.trim().isEmpty()) {
            return Mono.just(false);
        }

        // 基本验证：检查是否包含Tekton相关关键词
        String lowerCommand = deploymentCommand.toLowerCase();
        boolean isValid = lowerCommand.contains("tekton") ||
                         lowerCommand.contains("task") ||
                         lowerCommand.contains("pipeline") ||
                         lowerCommand.contains("kubectl") ||
                         lowerCommand.contains("deploy");

        return Mono.just(isValid);
    }

    @Override
    public Flux<String> getSupportedCommandTypes() {
        logger.debug("获取支持的命令类型");
        return Flux.just("tekton-task", "tekton-pipeline", "kubectl", "shell");
    }

    // ==================== 任务监控功能 ====================

    @Override
    public Flux<Map<String, Object>> monitorTaskProgress(String taskId) {
        logger.debug("监控任务进度: {}", taskId);

        // 通过定期获取任务状态来模拟进度监控
        return Flux.interval(java.time.Duration.ofSeconds(5))
                .take(10) // 最多监控10次
                .flatMap(tick -> getTaskStatus(taskId))
                .map(status -> {
                    Map<String, Object> progress = new HashMap<>(status);
                    progress.put("timestamp", LocalDateTime.now());

                    // 根据状态计算进度百分比
                    String taskStatus = (String) status.get("status");
                    int progressPercent = switch (taskStatus) {
                        case "PENDING" -> 0;
                        case "RUNNING" -> 50;
                        case "COMPLETED" -> 100;
                        case "FAILED" -> 100;
                        default -> 25;
                    };
                    progress.put("progress", progressPercent);

                    return progress;
                })
                .distinctUntilChanged(progress -> progress.get("status"))
                .onErrorResume(error -> {
                    logger.warn("监控任务进度失败: {}", error.getMessage());
                    return Flux.empty();
                });
    }

    @Override
    public Mono<Map<String, Object>> getTaskMetrics(String taskId) {
        logger.debug("获取任务指标: {}", taskId);

        // 获取任务状态作为基础指标
        return getTaskStatus(taskId)
                .map(status -> {
                    Map<String, Object> metrics = new HashMap<>();
                    metrics.put("taskId", taskId);
                    metrics.put("status", status.get("status"));
                    metrics.put("startTime", status.get("startTime"));
                    metrics.put("completionTime", status.get("completionTime"));

                    // 计算执行时长
                    if (status.get("startTime") != null) {
                        if (status.get("completionTime") != null) {
                            metrics.put("duration", "已完成");
                        } else {
                            metrics.put("duration", "运行中");
                        }
                    } else {
                        metrics.put("duration", "未开始");
                    }

                    // 添加基本资源信息（实际环境中可以从Kubernetes API获取）
                    metrics.put("resourceUsage", Map.of(
                        "note", "资源使用情况需要从Kubernetes Metrics API获取"
                    ));

                    return metrics;
                })
                .onErrorReturn(Map.of(
                    "taskId", taskId,
                    "error", "无法获取任务指标"
                ));
    }

    // ==================== 任务控制功能 ====================

    @Override
    public Mono<Map<String, Object>> retryTask(String taskId) {
        logger.info("重试任务: {}", taskId);

        // Tekton不支持直接重试，需要创建新的TaskRun/PipelineRun
        // 这里返回原任务信息，实际重试逻辑应该由调用方处理
        return getTaskStatus(taskId)
                .map(originalStatus -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("originalTaskId", taskId);
                    result.put("message", "Tekton不支持直接重试，请重新创建任务");
                    result.put("originalStatus", originalStatus);
                    result.put("retriedAt", LocalDateTime.now());
                    return result;
                })
                .onErrorReturn(Map.of(
                    "originalTaskId", taskId,
                    "error", "无法获取原任务信息"
                ));
    }

    @Override
    public Mono<Boolean> pauseTask(String taskId) {
        logger.info("暂停任务: {}", taskId);

        // Tekton不支持暂停任务，只能取消
        logger.warn("Tekton不支持暂停任务，建议使用取消操作");
        return Mono.just(false);
    }

    @Override
    public Mono<Boolean> resumeTask(String taskId) {
        logger.info("恢复任务: {}", taskId);

        // Tekton不支持恢复任务
        logger.warn("Tekton不支持恢复任务，需要重新创建任务");
        return Mono.just(false);
    }

    // ==================== 任务实例管理 ====================
    // 注意：CI/CD特定的任务创建和配置应该由TektonCiTaskService和TektonCdTaskService处理
    // 这里只提供通用的TaskRun和PipelineRun操作接口

    /**
     * 创建TaskRun实例
     * @param namespace 命名空间
     * @param taskName 任务名称
     * @param parameters 执行参数
     * @return TaskRun实例信息
     */
    public Mono<Map<String, Object>> createTaskRun(String namespace, String taskName, Map<String, Object> parameters) {
        logger.info("创建TaskRun: namespace={}, taskName={}", namespace, taskName);

        String targetNamespace = namespace != null ? namespace : defaultNamespace;
        return tektonClient.createTaskRun(targetNamespace, taskName, parameters);
    }

    /**
     * 创建PipelineRun实例
     * @param namespace 命名空间
     * @param pipelineName Pipeline名称
     * @param parameters 执行参数
     * @return PipelineRun实例信息
     */
    public Mono<Map<String, Object>> createPipelineRun(String namespace, String pipelineName, Map<String, Object> parameters) {
        logger.info("创建PipelineRun: namespace={}, pipelineName={}", namespace, pipelineName);

        String targetNamespace = namespace != null ? namespace : defaultNamespace;
        return tektonClient.createPipelineRun(targetNamespace, pipelineName, parameters);
    }

    /**
     * 列出TaskRun实例
     * @param namespace 命名空间
     * @param taskName 任务名称（可选）
     * @return TaskRun列表
     */
    public Flux<Map<String, Object>> listTaskRuns(String namespace, String taskName) {
        logger.debug("列出TaskRun: namespace={}, taskName={}", namespace, taskName);

        String targetNamespace = namespace != null ? namespace : defaultNamespace;
        return tektonClient.listTaskRuns(targetNamespace, taskName);
    }

    /**
     * 列出PipelineRun实例
     * @param namespace 命名空间
     * @param pipelineName Pipeline名称（可选）
     * @return PipelineRun列表
     */
    public Flux<Map<String, Object>> listPipelineRuns(String namespace, String pipelineName) {
        logger.debug("列出PipelineRun: namespace={}, pipelineName={}", namespace, pipelineName);

        String targetNamespace = namespace != null ? namespace : defaultNamespace;
        return tektonClient.listPipelineRuns(targetNamespace, pipelineName);
    }

}
