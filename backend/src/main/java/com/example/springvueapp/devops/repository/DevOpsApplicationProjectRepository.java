package com.example.springvueapp.devops.repository;

import com.example.springvueapp.devops.entity.DevOpsApplicationProjectEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 应用和项目关联关系的响应式Repository接口
 */
@Repository
public interface DevOpsApplicationProjectRepository extends ReactiveCrudRepository<DevOpsApplicationProjectEntity, Long> {

    /**
     * 根据应用ID查找所有关联的项目
     * @param applicationId 应用ID
     * @return 关联关系列表
     */
    Flux<DevOpsApplicationProjectEntity> findByApplicationId(Long applicationId);

    /**
     * 根据项目ID查找所有关联的应用
     * @param projectId 项目ID
     * @return 关联关系列表
     */
    Flux<DevOpsApplicationProjectEntity> findByProjectId(Long projectId);

    /**
     * 根据应用ID和项目ID查找关联关系
     * @param applicationId 应用ID
     * @param projectId 项目ID
     * @return 关联关系
     */
    Mono<DevOpsApplicationProjectEntity> findByApplicationIdAndProjectId(Long applicationId, Long projectId);

    /**
     * 根据应用ID删除所有关联关系
     * @param applicationId 应用ID
     * @return 删除的行数
     */
    Mono<Void> deleteByApplicationId(Long applicationId);

    /**
     * 根据项目ID删除所有关联关系
     * @param projectId 项目ID
     * @return 删除的行数
     */
    Mono<Void> deleteByProjectId(Long projectId);

    /**
     * 检查应用和项目之间是否存在关联关系
     * @param applicationId 应用ID
     * @param projectId 项目ID
     * @return 是否存在关联关系
     */
    Mono<Boolean> existsByApplicationIdAndProjectId(Long applicationId, Long projectId);
}