package com.example.springvueapp.portal.service;

import com.example.springvueapp.portal.enums.TargetType;
import com.example.springvueapp.portal.model.QueryRequest;
import com.example.springvueapp.portal.model.QueryResponse;
import com.example.springvueapp.portal.model.QueryableObject;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 抽象查询服务类
 * 提供统一的查询接口，支持查询不同类型的对象
 */
@Service
public class QueryService {

    /**
     * 执行查询
     */
    public Mono<QueryResponse> executeQuery(QueryRequest request) {
        // 根据目标类型执行不同的查询逻辑
        return switch (request.getTargetType()) {
            case "CiTask" -> queryCiTasks(request);
            case "CdTask" -> queryCdTasks(request);
            case "McpServer" -> queryMcpServers(request);
            case "CloudPlatform" -> queryCloudPlatforms(request);
            case "DevOpsProject" -> queryDevOpsProjects(request);
            case "DevOpsApplication" -> queryDevOpsApplications(request);
            default -> Mono.error(new IllegalArgumentException("不支持的查询目标类型: " + request.getTargetType()));
        };
    }

    /**
     * 获取支持的目标类型列表
     */
    public List<Map<String, Object>> getSupportedTargetTypes() {
        List<Map<String, Object>> targetTypes = new ArrayList<>();
        
        for (TargetType type : TargetType.values()) {
            Map<String, Object> typeInfo = new HashMap<>();
            typeInfo.put("type", type.getCode());
            typeInfo.put("displayName", type.getDisplayName());
            typeInfo.put("description", type.getDescription());
            typeInfo.put("availableFields", getAvailableFields(type.getCode()));
            typeInfo.put("availableStatuses", getAvailableStatuses(type.getCode()));
            
            targetTypes.add(typeInfo);
        }
        
        return targetTypes;
    }

    /**
     * 查询CI任务
     */
    private Mono<QueryResponse> queryCiTasks(QueryRequest request) {
        // 模拟查询CI任务的逻辑
        // 在实际实现中，这里应该调用DevOps模块的服务来查询CI任务
        List<QueryableObject> items = new ArrayList<>();
        
        // 模拟数据
        QueryableObject task1 = new QueryableObject();
        task1.setId("ci-task-001");
        task1.setName("前端构建任务");
        task1.setType("CiTask");
        task1.setTags(Arrays.asList("build", "frontend", "vue"));
        task1.setStatus("running");
        task1.setDescription("Vue.js前端项目构建任务");
        
        Map<String, Object> metadata1 = new HashMap<>();
        metadata1.put("projectId", 1);
        metadata1.put("branch", "main");
        metadata1.put("commit", "abc123");
        metadata1.put("duration", 180);
        task1.setMetadata(metadata1);
        task1.setCreatedAt(LocalDateTime.now().minusMinutes(30));
        task1.setUpdatedAt(LocalDateTime.now().minusMinutes(5));
        
        items.add(task1);
        
        // 应用过滤器
        items = applyFilters(items, request);
        
        // 应用排序
        items = applySorting(items, request);
        
        // 应用分页
        int total = items.size();
        items = applyPagination(items, request);
        
        QueryResponse response = new QueryResponse();
        response.setItems(items);
        response.setTotal((long) total);
        response.setPage(request.getPage() != null ? request.getPage() : 0);
        response.setSize(request.getSize() != null ? request.getSize() : 10);
        response.setHasNext(response.getPage() * response.getSize() + items.size() < total);
        response.setHasPrevious(response.getPage() > 0);
        
        return Mono.just(response);
    }

    /**
     * 查询CD任务
     */
    private Mono<QueryResponse> queryCdTasks(QueryRequest request) {
        // 模拟查询CD任务的逻辑
        List<QueryableObject> items = new ArrayList<>();
        
        // 模拟数据
        QueryableObject task1 = new QueryableObject();
        task1.setId("cd-task-001");
        task1.setName("生产环境部署");
        task1.setType("CdTask");
        task1.setTags(Arrays.asList("deploy", "production"));
        task1.setStatus("completed");
        task1.setDescription("生产环境应用部署任务");
        
        Map<String, Object> metadata1 = new HashMap<>();
        metadata1.put("applicationId", 1);
        metadata1.put("environment", "production");
        metadata1.put("version", "v1.2.0");
        task1.setMetadata(metadata1);
        task1.setCreatedAt(LocalDateTime.now().minusHours(2));
        task1.setUpdatedAt(LocalDateTime.now().minusHours(1));
        
        items.add(task1);
        
        // 应用过滤和分页逻辑
        items = applyFilters(items, request);
        items = applySorting(items, request);
        int total = items.size();
        items = applyPagination(items, request);
        
        QueryResponse response = new QueryResponse();
        response.setItems(items);
        response.setTotal((long) total);
        response.setPage(request.getPage() != null ? request.getPage() : 0);
        response.setSize(request.getSize() != null ? request.getSize() : 10);
        response.setHasNext(response.getPage() * response.getSize() + items.size() < total);
        response.setHasPrevious(response.getPage() > 0);
        
        return Mono.just(response);
    }

    /**
     * 查询MCP服务器
     */
    private Mono<QueryResponse> queryMcpServers(QueryRequest request) {
        // 模拟查询MCP服务器的逻辑
        List<QueryableObject> items = new ArrayList<>();
        
        // 模拟数据
        QueryableObject server1 = new QueryableObject();
        server1.setId("mcp-server-001");
        server1.setName("开发环境MCP服务器");
        server1.setType("McpServer");
        server1.setTags(Arrays.asList("mcp", "development"));
        server1.setStatus("active");
        server1.setDescription("开发环境的MCP服务器配置");
        
        Map<String, Object> metadata1 = new HashMap<>();
        metadata1.put("host", "dev-mcp.example.com");
        metadata1.put("port", 8080);
        metadata1.put("protocol", "http");
        server1.setMetadata(metadata1);
        server1.setCreatedAt(LocalDateTime.now().minusDays(7));
        server1.setUpdatedAt(LocalDateTime.now().minusHours(3));
        
        items.add(server1);
        
        // 应用过滤和分页逻辑
        items = applyFilters(items, request);
        items = applySorting(items, request);
        int total = items.size();
        items = applyPagination(items, request);
        
        QueryResponse response = new QueryResponse();
        response.setItems(items);
        response.setTotal((long) total);
        response.setPage(request.getPage() != null ? request.getPage() : 0);
        response.setSize(request.getSize() != null ? request.getSize() : 10);
        response.setHasNext(response.getPage() * response.getSize() + items.size() < total);
        response.setHasPrevious(response.getPage() > 0);
        
        return Mono.just(response);
    }

    /**
     * 查询云平台配置
     */
    private Mono<QueryResponse> queryCloudPlatforms(QueryRequest request) {
        // 模拟查询云平台配置的逻辑
        List<QueryableObject> items = new ArrayList<>();
        
        // 应用过滤和分页逻辑
        items = applyFilters(items, request);
        items = applySorting(items, request);
        int total = items.size();
        items = applyPagination(items, request);
        
        QueryResponse response = new QueryResponse();
        response.setItems(items);
        response.setTotal((long) total);
        response.setPage(request.getPage() != null ? request.getPage() : 0);
        response.setSize(request.getSize() != null ? request.getSize() : 10);
        response.setHasNext(response.getPage() * response.getSize() + items.size() < total);
        response.setHasPrevious(response.getPage() > 0);
        
        return Mono.just(response);
    }

    /**
     * 查询DevOps项目
     */
    private Mono<QueryResponse> queryDevOpsProjects(QueryRequest request) {
        // 模拟查询DevOps项目的逻辑
        List<QueryableObject> items = new ArrayList<>();
        
        // 应用过滤和分页逻辑
        items = applyFilters(items, request);
        items = applySorting(items, request);
        int total = items.size();
        items = applyPagination(items, request);
        
        QueryResponse response = new QueryResponse();
        response.setItems(items);
        response.setTotal((long) total);
        response.setPage(request.getPage() != null ? request.getPage() : 0);
        response.setSize(request.getSize() != null ? request.getSize() : 10);
        response.setHasNext(response.getPage() * response.getSize() + items.size() < total);
        response.setHasPrevious(response.getPage() > 0);
        
        return Mono.just(response);
    }

    /**
     * 查询DevOps应用
     */
    private Mono<QueryResponse> queryDevOpsApplications(QueryRequest request) {
        // 模拟查询DevOps应用的逻辑
        List<QueryableObject> items = new ArrayList<>();
        
        // 应用过滤和分页逻辑
        items = applyFilters(items, request);
        items = applySorting(items, request);
        int total = items.size();
        items = applyPagination(items, request);
        
        QueryResponse response = new QueryResponse();
        response.setItems(items);
        response.setTotal((long) total);
        response.setPage(request.getPage() != null ? request.getPage() : 0);
        response.setSize(request.getSize() != null ? request.getSize() : 10);
        response.setHasNext(response.getPage() * response.getSize() + items.size() < total);
        response.setHasPrevious(response.getPage() > 0);
        
        return Mono.just(response);
    }

    /**
     * 应用过滤器
     */
    private List<QueryableObject> applyFilters(List<QueryableObject> items, QueryRequest request) {
        return items.stream()
                .filter(item -> {
                    // 名称过滤
                    if (request.getNameFilter() != null && !request.getNameFilter().trim().isEmpty()) {
                        if (!item.getName().toLowerCase().contains(request.getNameFilter().toLowerCase())) {
                            return false;
                        }
                    }
                    
                    // 状态过滤
                    if (request.getStatusFilter() != null && !request.getStatusFilter().isEmpty()) {
                        if (!request.getStatusFilter().contains(item.getStatus())) {
                            return false;
                        }
                    }
                    
                    // 标签过滤
                    if (request.getTagFilters() != null && !request.getTagFilters().isEmpty()) {
                        if (item.getTags() == null || item.getTags().isEmpty()) {
                            return false;
                        }
                        boolean hasMatchingTag = request.getTagFilters().stream()
                                .anyMatch(tag -> item.getTags().contains(tag));
                        if (!hasMatchingTag) {
                            return false;
                        }
                    }
                    
                    return true;
                })
                .toList();
    }

    /**
     * 应用排序
     */
    private List<QueryableObject> applySorting(List<QueryableObject> items, QueryRequest request) {
        if (request.getSortBy() == null || request.getSortBy().trim().isEmpty()) {
            return items;
        }
        
        Comparator<QueryableObject> comparator = switch (request.getSortBy()) {
            case "name" -> Comparator.comparing(QueryableObject::getName);
            case "status" -> Comparator.comparing(QueryableObject::getStatus);
            case "createdAt" -> Comparator.comparing(QueryableObject::getCreatedAt);
            case "updatedAt" -> Comparator.comparing(QueryableObject::getUpdatedAt);
            default -> Comparator.comparing(QueryableObject::getCreatedAt);
        };
        
        if ("desc".equalsIgnoreCase(request.getSortOrder())) {
            comparator = comparator.reversed();
        }
        
        return items.stream().sorted(comparator).toList();
    }

    /**
     * 应用分页
     */
    private List<QueryableObject> applyPagination(List<QueryableObject> items, QueryRequest request) {
        int page = request.getPage() != null ? request.getPage() : 0;
        int size = request.getSize() != null ? request.getSize() : 10;
        
        int start = page * size;
        int end = Math.min(start + size, items.size());
        
        if (start >= items.size()) {
            return new ArrayList<>();
        }
        
        return items.subList(start, end);
    }

    /**
     * 获取可用字段列表
     */
    private List<String> getAvailableFields(String targetType) {
        return switch (targetType) {
            case "CiTask" -> Arrays.asList("name", "status", "tags", "projectId", "branch");
            case "CdTask" -> Arrays.asList("name", "status", "tags", "applicationId", "environment");
            case "McpServer" -> Arrays.asList("name", "status", "tags", "host", "port");
            case "CloudPlatform" -> Arrays.asList("name", "status", "tags", "provider", "region");
            case "DevOpsProject" -> Arrays.asList("name", "status", "tags", "description");
            case "DevOpsApplication" -> Arrays.asList("name", "status", "tags", "projectId", "type");
            default -> Arrays.asList("name", "status", "tags");
        };
    }

    /**
     * 获取可用状态列表
     */
    private List<String> getAvailableStatuses(String targetType) {
        return switch (targetType) {
            case "CiTask" -> Arrays.asList("pending", "running", "completed", "failed", "cancelled");
            case "CdTask" -> Arrays.asList("pending", "running", "completed", "failed", "rolled_back");
            case "McpServer" -> Arrays.asList("active", "inactive", "error");
            case "CloudPlatform" -> Arrays.asList("connected", "disconnected", "error");
            case "DevOpsProject" -> Arrays.asList("active", "inactive", "archived");
            case "DevOpsApplication" -> Arrays.asList("active", "inactive", "deployed", "stopped");
            default -> Arrays.asList("active", "inactive");
        };
    }
}
