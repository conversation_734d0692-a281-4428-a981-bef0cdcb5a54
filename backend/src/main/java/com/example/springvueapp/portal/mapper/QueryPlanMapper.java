package com.example.springvueapp.portal.mapper;

import com.example.springvueapp.portal.entity.QueryPlanEntity;
import com.example.springvueapp.portal.model.QueryPlan;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 查询计划实体与DTO之间的映射器
 */
@Component
public class QueryPlanMapper {

    private final ObjectMapper objectMapper;

    @Autowired
    public QueryPlanMapper(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 将实体转换为DTO
     */
    public QueryPlan toDto(QueryPlanEntity entity) {
        if (entity == null) {
            return null;
        }

        QueryPlan queryPlan = new QueryPlan();
        queryPlan.setId(entity.getId());
        queryPlan.setName(entity.getName());
        queryPlan.setDescription(entity.getDescription());
        queryPlan.setPortalId(entity.getPortalId());
        queryPlan.setTargetType(entity.getTargetType());
        queryPlan.setQueryConfig(parseQueryConfig(entity.getQueryConfig()));
        queryPlan.setColor(entity.getColor());
        queryPlan.setIcon(entity.getIcon());
        queryPlan.setStatus(entity.getStatus());
        queryPlan.setUserId(entity.getUserId());
        queryPlan.setCreatedAt(entity.getCreatedAt());
        queryPlan.setUpdatedAt(entity.getUpdatedAt());
        
        return queryPlan;
    }

    /**
     * 将DTO转换为实体
     */
    public QueryPlanEntity toEntity(QueryPlan queryPlan) {
        if (queryPlan == null) {
            return null;
        }

        QueryPlanEntity entity = new QueryPlanEntity();
        entity.setId(queryPlan.getId());
        entity.setName(queryPlan.getName());
        entity.setDescription(queryPlan.getDescription());
        entity.setPortalId(queryPlan.getPortalId());
        entity.setTargetType(queryPlan.getTargetType());
        entity.setQueryConfig(serializeQueryConfig(queryPlan.getQueryConfig()));
        entity.setColor(queryPlan.getColor());
        entity.setIcon(queryPlan.getIcon());
        entity.setStatus(queryPlan.getStatus());
        entity.setUserId(queryPlan.getUserId());
        entity.setCreatedAt(queryPlan.getCreatedAt());
        entity.setUpdatedAt(queryPlan.getUpdatedAt());
        
        return entity;
    }

    /**
     * 将实体转换为DTO（包含结果数量和最后执行时间）
     */
    public QueryPlan toDtoWithExecutionInfo(QueryPlanEntity entity, Integer resultCount) {
        QueryPlan queryPlan = toDto(entity);
        if (queryPlan != null) {
            queryPlan.setResultCount(resultCount);
            // 最后执行时间可以从查询配置或其他地方获取
        }
        return queryPlan;
    }

    /**
     * 更新实体的基本信息（不包括ID、门户ID、用户ID、创建时间）
     */
    public void updateEntityFromDto(QueryPlanEntity entity, QueryPlan queryPlan) {
        if (entity == null || queryPlan == null) {
            return;
        }

        entity.setName(queryPlan.getName());
        entity.setDescription(queryPlan.getDescription());
        entity.setTargetType(queryPlan.getTargetType());
        entity.setQueryConfig(serializeQueryConfig(queryPlan.getQueryConfig()));
        entity.setColor(queryPlan.getColor());
        entity.setIcon(queryPlan.getIcon());
        entity.setStatus(queryPlan.getStatus());
        // 更新时间会在保存时自动设置
    }

    /**
     * 解析JSON格式的查询配置
     */
    private Map<String, Object> parseQueryConfig(String queryConfigJson) {
        if (queryConfigJson == null || queryConfigJson.trim().isEmpty()) {
            return new HashMap<>();
        }

        try {
            return objectMapper.readValue(queryConfigJson, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            // 如果解析失败，返回空Map
            return new HashMap<>();
        }
    }

    /**
     * 序列化查询配置为JSON格式
     */
    public String serializeQueryConfig(Map<String, Object> queryConfig) {
        if (queryConfig == null || queryConfig.isEmpty()) {
            return "{}";
        }

        try {
            return objectMapper.writeValueAsString(queryConfig);
        } catch (JsonProcessingException e) {
            // 如果序列化失败，返回空JSON对象
            return "{}";
        }
    }
}
