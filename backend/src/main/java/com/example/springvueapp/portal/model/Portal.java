package com.example.springvueapp.portal.model;

import java.time.LocalDateTime;

/**
 * 门户的DTO类
 * 与前端TypeScript接口保持一致，用于API交互
 */
public class Portal {

    private Long id;

    private String name;

    private String description;

    private String color;

    private String icon;

    private String status;

    private Long userId;

    private Integer queryPlanCount; // 查询计划数量

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public Portal() {
    }

    public Portal(Long id, String name, String description, String color, String icon,
                 String status, Long userId, Integer queryPlanCount, 
                 LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.color = color;
        this.icon = icon;
        this.status = status;
        this.userId = userId;
        this.queryPlanCount = queryPlanCount;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getQueryPlanCount() {
        return queryPlanCount;
    }

    public void setQueryPlanCount(Integer queryPlanCount) {
        this.queryPlanCount = queryPlanCount;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "Portal{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", color='" + color + '\'' +
                ", icon='" + icon + '\'' +
                ", status='" + status + '\'' +
                ", userId=" + userId +
                ", queryPlanCount=" + queryPlanCount +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
