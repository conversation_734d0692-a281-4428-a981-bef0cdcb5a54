package com.example.springvueapp.portal.enums;

/**
 * 查询目标类型枚举
 */
public enum TargetType {
    
    CI_TASK("CiTask", "CI任务", "持续集成构建任务"),
    CD_TASK("CdTask", "CD任务", "持续部署任务"),
    MCP_SERVER("McpServer", "MCP服务器", "MCP服务器配置"),
    CLOUD_PLATFORM("CloudPlatform", "云平台", "云平台配置"),
    DEVOPS_PROJECT("DevOpsProject", "DevOps项目", "DevOps项目管理"),
    DEVOPS_APPLICATION("DevOpsApplication", "DevOps应用", "DevOps应用管理");

    private final String code;
    private final String displayName;
    private final String description;

    TargetType(String code, String displayName, String description) {
        this.code = code;
        this.displayName = displayName;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举值
     */
    public static TargetType fromCode(String code) {
        for (TargetType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的目标类型代码: " + code);
    }

    @Override
    public String toString() {
        return code;
    }
}
