package com.example.springvueapp.portal.model;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 查询计划的DTO类
 * 与前端TypeScript接口保持一致，用于API交互
 */
public class QueryPlan {

    private Long id;

    private String name;

    private String description;

    private Long portalId;

    private String targetType;

    private Map<String, Object> queryConfig; // 查询配置

    private String color;

    private String icon;

    private String status;

    private Long userId;

    private Integer resultCount; // 查询结果数量

    private LocalDateTime lastExecuted; // 最后执行时间

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public QueryPlan() {
    }

    public QueryPlan(Long id, String name, String description, Long portalId, String targetType,
                    Map<String, Object> queryConfig, String color, String icon, String status,
                    Long userId, Integer resultCount, LocalDateTime lastExecuted,
                    LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.portalId = portalId;
        this.targetType = targetType;
        this.queryConfig = queryConfig;
        this.color = color;
        this.icon = icon;
        this.status = status;
        this.userId = userId;
        this.resultCount = resultCount;
        this.lastExecuted = lastExecuted;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getPortalId() {
        return portalId;
    }

    public void setPortalId(Long portalId) {
        this.portalId = portalId;
    }

    public String getTargetType() {
        return targetType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    public Map<String, Object> getQueryConfig() {
        return queryConfig;
    }

    public void setQueryConfig(Map<String, Object> queryConfig) {
        this.queryConfig = queryConfig;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getResultCount() {
        return resultCount;
    }

    public void setResultCount(Integer resultCount) {
        this.resultCount = resultCount;
    }

    public LocalDateTime getLastExecuted() {
        return lastExecuted;
    }

    public void setLastExecuted(LocalDateTime lastExecuted) {
        this.lastExecuted = lastExecuted;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "QueryPlan{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", portalId=" + portalId +
                ", targetType='" + targetType + '\'' +
                ", queryConfig=" + queryConfig +
                ", color='" + color + '\'' +
                ", icon='" + icon + '\'' +
                ", status='" + status + '\'' +
                ", userId=" + userId +
                ", resultCount=" + resultCount +
                ", lastExecuted=" + lastExecuted +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
