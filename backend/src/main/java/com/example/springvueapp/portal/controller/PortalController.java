package com.example.springvueapp.portal.controller;

import com.example.springvueapp.portal.model.Portal;
import com.example.springvueapp.portal.service.PortalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * 门户管理控制器
 * 提供门户的CRUD操作API
 */
@RestController
@RequestMapping("/api/portals")
@CrossOrigin(origins = "*")
public class PortalController {

    private final PortalService portalService;

    @Autowired
    public PortalController(PortalService portalService) {
        this.portalService = portalService;
    }

    /**
     * 创建门户
     */
    @PostMapping
    public Mono<ResponseEntity<Portal>> createPortal(@RequestBody Portal portal,
                                                    @RequestHeader("X-User-Id") Long userId) {
        return portalService.createPortal(portal, userId)
                .map(createdPortal -> ResponseEntity.status(HttpStatus.CREATED).body(createdPortal))
                .onErrorResume(IllegalArgumentException.class, 
                    e -> Mono.just(ResponseEntity.badRequest().build()))
                .onErrorResume(Exception.class, 
                    e -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()));
    }

    /**
     * 获取门户列表（分页）
     */
    @GetMapping
    public Mono<ResponseEntity<Map<String, Object>>> getPortals(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestHeader("X-User-Id") Long userId) {
        
        Flux<Portal> portalsFlux;
        Mono<Long> totalMono;
        
        if (search != null && !search.trim().isEmpty()) {
            portalsFlux = portalService.searchPortals(userId, search)
                    .skip((long) page * size)
                    .take(size);
            totalMono = portalService.countPortalsByUserAndKeyword(userId, search);
        } else {
            portalsFlux = portalService.getPortalsByUser(userId, page, size);
            totalMono = portalService.countPortalsByUser(userId);
        }
        
        return Mono.zip(
                portalsFlux.collectList(),
                totalMono
        ).map(tuple -> {
            Map<String, Object> response = new HashMap<>();
            response.put("content", tuple.getT1());
            response.put("page", page);
            response.put("size", size);
            response.put("total", tuple.getT2());
            response.put("totalPages", (tuple.getT2() + size - 1) / size);
            response.put("hasNext", (long) page * size + tuple.getT1().size() < tuple.getT2());
            response.put("hasPrevious", page > 0);
            
            return ResponseEntity.ok(response);
        }).onErrorResume(Exception.class, 
            e -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()));
    }

    /**
     * 获取门户详情
     */
    @GetMapping("/{id}")
    public Mono<ResponseEntity<Portal>> getPortalById(@PathVariable Long id,
                                                     @RequestHeader("X-User-Id") Long userId) {
        return portalService.getPortalById(id, userId)
                .map(portal -> ResponseEntity.ok(portal))
                .onErrorResume(IllegalArgumentException.class, 
                    e -> Mono.just(ResponseEntity.notFound().build()))
                .onErrorResume(Exception.class, 
                    e -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()));
    }

    /**
     * 更新门户
     */
    @PutMapping("/{id}")
    public Mono<ResponseEntity<Portal>> updatePortal(@PathVariable Long id,
                                                    @RequestBody Portal portal,
                                                    @RequestHeader("X-User-Id") Long userId) {
        return portalService.updatePortal(id, portal, userId)
                .map(updatedPortal -> ResponseEntity.ok(updatedPortal))
                .onErrorResume(IllegalArgumentException.class, 
                    e -> Mono.just(ResponseEntity.badRequest().build()))
                .onErrorResume(Exception.class, 
                    e -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()));
    }

    /**
     * 删除门户
     */
    @DeleteMapping("/{id}")
    public Mono<ResponseEntity<Void>> deletePortal(@PathVariable Long id,
                                                   @RequestHeader("X-User-Id") Long userId) {
        return portalService.deletePortal(id, userId)
                .then(Mono.just(ResponseEntity.noContent().<Void>build()))
                .onErrorResume(IllegalArgumentException.class, 
                    e -> Mono.just(ResponseEntity.notFound().build()))
                .onErrorResume(Exception.class, 
                    e -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()));
    }

    /**
     * 获取用户的所有门户（不分页）
     */
    @GetMapping("/all")
    public Mono<ResponseEntity<Flux<Portal>>> getAllPortals(@RequestHeader("X-User-Id") Long userId) {
        return Mono.just(ResponseEntity.ok(portalService.getPortalsByUser(userId)))
                .onErrorResume(Exception.class, 
                    e -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()));
    }

    /**
     * 统计用户的门户数量
     */
    @GetMapping("/count")
    public Mono<ResponseEntity<Map<String, Object>>> getPortalCount(@RequestHeader("X-User-Id") Long userId) {
        return portalService.countPortalsByUser(userId)
                .map(count -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("count", count);
                    return ResponseEntity.ok(response);
                })
                .onErrorResume(Exception.class, 
                    e -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()));
    }
}
