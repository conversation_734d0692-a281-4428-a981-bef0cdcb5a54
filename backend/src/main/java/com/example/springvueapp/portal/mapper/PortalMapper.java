package com.example.springvueapp.portal.mapper;

import com.example.springvueapp.portal.entity.PortalEntity;
import com.example.springvueapp.portal.model.Portal;
import org.springframework.stereotype.Component;

/**
 * 门户实体与DTO之间的映射器
 */
@Component
public class PortalMapper {

    /**
     * 将实体转换为DTO
     */
    public Portal toDto(PortalEntity entity) {
        if (entity == null) {
            return null;
        }

        Portal portal = new Portal();
        portal.setId(entity.getId());
        portal.setName(entity.getName());
        portal.setDescription(entity.getDescription());
        portal.setColor(entity.getColor());
        portal.setIcon(entity.getIcon());
        portal.setStatus(entity.getStatus());
        portal.setUserId(entity.getUserId());
        portal.setCreatedAt(entity.getCreatedAt());
        portal.setUpdatedAt(entity.getUpdatedAt());
        
        return portal;
    }

    /**
     * 将DTO转换为实体
     */
    public PortalEntity toEntity(Portal portal) {
        if (portal == null) {
            return null;
        }

        PortalEntity entity = new PortalEntity();
        entity.setId(portal.getId());
        entity.setName(portal.getName());
        entity.setDescription(portal.getDescription());
        entity.setColor(portal.getColor());
        entity.setIcon(portal.getIcon());
        entity.setStatus(portal.getStatus());
        entity.setUserId(portal.getUserId());
        entity.setCreatedAt(portal.getCreatedAt());
        entity.setUpdatedAt(portal.getUpdatedAt());
        
        return entity;
    }

    /**
     * 将实体转换为DTO（包含查询计划数量）
     */
    public Portal toDtoWithQueryPlanCount(PortalEntity entity, Integer queryPlanCount) {
        Portal portal = toDto(entity);
        if (portal != null) {
            portal.setQueryPlanCount(queryPlanCount);
        }
        return portal;
    }

    /**
     * 更新实体的基本信息（不包括ID、用户ID、创建时间）
     */
    public void updateEntityFromDto(PortalEntity entity, Portal portal) {
        if (entity == null || portal == null) {
            return;
        }

        entity.setName(portal.getName());
        entity.setDescription(portal.getDescription());
        entity.setColor(portal.getColor());
        entity.setIcon(portal.getIcon());
        entity.setStatus(portal.getStatus());
        // 更新时间会在保存时自动设置
    }
}
