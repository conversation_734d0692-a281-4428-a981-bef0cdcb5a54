package com.example.springvueapp.portal.controller;

import com.example.springvueapp.portal.model.QueryPlan;
import com.example.springvueapp.portal.model.QueryRequest;
import com.example.springvueapp.portal.model.QueryResponse;
import com.example.springvueapp.portal.service.QueryPlanService;
import com.example.springvueapp.portal.service.QueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 查询计划管理控制器
 * 提供查询计划的CRUD操作和查询执行API
 */
@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*")
public class QueryPlanController {

    private final QueryPlanService queryPlanService;
    private final QueryService queryService;

    @Autowired
    public QueryPlanController(QueryPlanService queryPlanService, QueryService queryService) {
        this.queryPlanService = queryPlanService;
        this.queryService = queryService;
    }

    /**
     * 创建查询计划
     */
    @PostMapping("/portals/{portalId}/query-plans")
    public Mono<ResponseEntity<QueryPlan>> createQueryPlan(@PathVariable Long portalId,
                                                          @RequestBody QueryPlan queryPlan,
                                                          @RequestHeader("X-User-Id") Long userId) {
        queryPlan.setPortalId(portalId);
        return queryPlanService.createQueryPlan(queryPlan, userId)
                .map(createdQueryPlan -> ResponseEntity.status(HttpStatus.CREATED).body(createdQueryPlan))
                .onErrorResume(IllegalArgumentException.class, 
                    e -> Mono.just(ResponseEntity.badRequest().build()))
                .onErrorResume(Exception.class, 
                    e -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()));
    }

    /**
     * 获取门户下的查询计划列表（分页）
     */
    @GetMapping("/portals/{portalId}/query-plans")
    public Mono<ResponseEntity<Map<String, Object>>> getQueryPlans(
            @PathVariable Long portalId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestHeader("X-User-Id") Long userId) {
        
        Flux<QueryPlan> queryPlansFlux;
        Mono<Long> totalMono;
        
        if (search != null && !search.trim().isEmpty()) {
            queryPlansFlux = queryPlanService.searchQueryPlans(portalId, userId, search)
                    .skip((long) page * size)
                    .take(size);
            // 注意：这里需要实现搜索的计数方法
            totalMono = queryPlanService.countQueryPlansByPortal(portalId, userId);
        } else {
            queryPlansFlux = queryPlanService.getQueryPlansByPortal(portalId, userId, page, size);
            totalMono = queryPlanService.countQueryPlansByPortal(portalId, userId);
        }
        
        return Mono.zip(
                queryPlansFlux.collectList(),
                totalMono
        ).map(tuple -> {
            Map<String, Object> response = new HashMap<>();
            response.put("content", tuple.getT1());
            response.put("page", page);
            response.put("size", size);
            response.put("total", tuple.getT2());
            response.put("totalPages", (tuple.getT2() + size - 1) / size);
            response.put("hasNext", (long) page * size + tuple.getT1().size() < tuple.getT2());
            response.put("hasPrevious", page > 0);
            
            return ResponseEntity.ok(response);
        }).onErrorResume(Exception.class, 
            e -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()));
    }

    /**
     * 获取查询计划详情
     */
    @GetMapping("/query-plans/{id}")
    public Mono<ResponseEntity<QueryPlan>> getQueryPlanById(@PathVariable Long id,
                                                           @RequestHeader("X-User-Id") Long userId) {
        return queryPlanService.getQueryPlanById(id, userId)
                .map(queryPlan -> ResponseEntity.ok(queryPlan))
                .onErrorResume(IllegalArgumentException.class, 
                    e -> Mono.just(ResponseEntity.notFound().build()))
                .onErrorResume(Exception.class, 
                    e -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()));
    }

    /**
     * 更新查询计划
     */
    @PutMapping("/query-plans/{id}")
    public Mono<ResponseEntity<QueryPlan>> updateQueryPlan(@PathVariable Long id,
                                                          @RequestBody QueryPlan queryPlan,
                                                          @RequestHeader("X-User-Id") Long userId) {
        return queryPlanService.updateQueryPlan(id, queryPlan, userId)
                .map(updatedQueryPlan -> ResponseEntity.ok(updatedQueryPlan))
                .onErrorResume(IllegalArgumentException.class, 
                    e -> Mono.just(ResponseEntity.badRequest().build()))
                .onErrorResume(Exception.class, 
                    e -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()));
    }

    /**
     * 删除查询计划
     */
    @DeleteMapping("/query-plans/{id}")
    public Mono<ResponseEntity<Void>> deleteQueryPlan(@PathVariable Long id,
                                                      @RequestHeader("X-User-Id") Long userId) {
        return queryPlanService.deleteQueryPlan(id, userId)
                .then(Mono.just(ResponseEntity.noContent().<Void>build()))
                .onErrorResume(IllegalArgumentException.class, 
                    e -> Mono.just(ResponseEntity.notFound().build()))
                .onErrorResume(Exception.class, 
                    e -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()));
    }

    /**
     * 执行查询计划
     */
    @PostMapping("/query-plans/{id}/execute")
    public Mono<ResponseEntity<QueryResponse>> executeQueryPlan(@PathVariable Long id,
                                                               @RequestHeader("X-User-Id") Long userId) {
        return queryPlanService.getQueryPlanById(id, userId)
                .flatMap(queryPlan -> {
                    // 将查询计划转换为查询请求
                    QueryRequest request = new QueryRequest();
                    request.setTargetType(queryPlan.getTargetType());
                    
                    // 从查询配置中提取参数
                    Map<String, Object> config = queryPlan.getQueryConfig();
                    if (config != null) {
                        if (config.containsKey("nameFilter")) {
                            request.setNameFilter((String) config.get("nameFilter"));
                        }
                        if (config.containsKey("statusFilter")) {
                            request.setStatusFilter((List<String>) config.get("statusFilter"));
                        }
                        if (config.containsKey("tagFilters")) {
                            request.setTagFilters((List<String>) config.get("tagFilters"));
                        }
                        if (config.containsKey("sortBy")) {
                            request.setSortBy((String) config.get("sortBy"));
                        }
                        if (config.containsKey("sortOrder")) {
                            request.setSortOrder((String) config.get("sortOrder"));
                        }
                        if (config.containsKey("limit")) {
                            request.setSize((Integer) config.get("limit"));
                        }
                    }
                    
                    // 设置默认值
                    if (request.getPage() == null) request.setPage(0);
                    if (request.getSize() == null) request.setSize(20);
                    
                    return queryService.executeQuery(request);
                })
                .map(response -> ResponseEntity.ok(response))
                .onErrorResume(IllegalArgumentException.class, 
                    e -> Mono.just(ResponseEntity.badRequest().build()))
                .onErrorResume(Exception.class, 
                    e -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()));
    }

    /**
     * 通用查询接口
     */
    @PostMapping("/query/search")
    public Mono<ResponseEntity<QueryResponse>> executeQuery(@RequestBody QueryRequest request) {
        return queryService.executeQuery(request)
                .map(response -> ResponseEntity.ok(response))
                .onErrorResume(IllegalArgumentException.class, 
                    e -> Mono.just(ResponseEntity.badRequest().build()))
                .onErrorResume(Exception.class, 
                    e -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()));
    }

    /**
     * 获取支持的目标类型
     */
    @GetMapping("/query/target-types")
    public Mono<ResponseEntity<List<Map<String, Object>>>> getSupportedTargetTypes() {
        return Mono.just(ResponseEntity.ok(queryService.getSupportedTargetTypes()))
                .onErrorResume(Exception.class, 
                    e -> Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()));
    }
}
