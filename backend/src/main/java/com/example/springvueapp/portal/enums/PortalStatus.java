package com.example.springvueapp.portal.enums;

/**
 * 门户状态枚举
 */
public enum PortalStatus {
    
    ACTIVE("active", "激活"),
    INACTIVE("inactive", "停用"),
    ARCHIVED("archived", "归档");

    private final String code;
    private final String description;

    PortalStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举值
     */
    public static PortalStatus fromCode(String code) {
        for (PortalStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的门户状态代码: " + code);
    }

    @Override
    public String toString() {
        return code;
    }
}
