package com.example.springvueapp.portal.service;

import com.example.springvueapp.portal.entity.QueryPlanEntity;
import com.example.springvueapp.portal.enums.PortalStatus;
import com.example.springvueapp.portal.mapper.QueryPlanMapper;
import com.example.springvueapp.portal.model.QueryPlan;
import com.example.springvueapp.portal.repository.PortalRepository;
import com.example.springvueapp.portal.repository.QueryPlanRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 查询计划服务类
 * 提供查询计划管理的业务逻辑
 */
@Service
@Transactional()
public class QueryPlanService {

    private final QueryPlanRepository queryPlanRepository;
    private final PortalRepository portalRepository;
    private final QueryPlanMapper queryPlanMapper;

    @Autowired
    public QueryPlanService(QueryPlanRepository queryPlanRepository,
                           PortalRepository portalRepository,
                           QueryPlanMapper queryPlanMapper) {
        this.queryPlanRepository = queryPlanRepository;
        this.portalRepository = portalRepository;
        this.queryPlanMapper = queryPlanMapper;
    }

    /**
     * 创建查询计划
     */
    public Mono<QueryPlan> createQueryPlan(QueryPlan queryPlan, Long userId) {
        // 验证门户是否存在且用户有权限
        return portalRepository.findById(queryPlan.getPortalId())
                .switchIfEmpty(Mono.error(new IllegalArgumentException("门户不存在: " + queryPlan.getPortalId())))
                .flatMap(portalEntity -> {
                    if (!portalEntity.getUserId().equals(userId)) {
                        return Mono.error(new IllegalArgumentException("无权限在此门户下创建查询计划"));
                    }
                    
                    // 验证查询计划名称是否已存在
                    return queryPlanRepository.existsByPortalIdAndName(queryPlan.getPortalId(), queryPlan.getName())
                            .flatMap(exists -> {
                                if (exists) {
                                    return Mono.error(new IllegalArgumentException("查询计划名称已存在: " + queryPlan.getName()));
                                }
                                
                                // 创建查询计划实体
                                QueryPlanEntity entity = new QueryPlanEntity();
                                entity.setName(queryPlan.getName());
                                entity.setDescription(queryPlan.getDescription());
                                entity.setPortalId(queryPlan.getPortalId());
                                entity.setTargetType(queryPlan.getTargetType());
                                entity.setQueryConfig(queryPlanMapper.serializeQueryConfig(queryPlan.getQueryConfig()));
                                entity.setColor(queryPlan.getColor() != null ? queryPlan.getColor() : "#52c41a");
                                entity.setIcon(queryPlan.getIcon() != null ? queryPlan.getIcon() : "search");
                                entity.setStatus(PortalStatus.ACTIVE.getCode());
                                entity.setUserId(userId);
                                entity.setCreatedAt(LocalDateTime.now());
                                entity.setUpdatedAt(LocalDateTime.now());
                                
                                return queryPlanRepository.save(entity);
                            });
                })
                .map(queryPlanMapper::toDto);
    }

    /**
     * 更新查询计划
     */
    public Mono<QueryPlan> updateQueryPlan(Long id, QueryPlan queryPlan, Long userId) {
        return queryPlanRepository.findById(id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("查询计划不存在: " + id)))
                .flatMap(existingEntity -> {
                    // 检查权限
                    if (!existingEntity.getUserId().equals(userId)) {
                        return Mono.error(new IllegalArgumentException("无权限修改此查询计划"));
                    }
                    
                    // 检查名称是否与其他查询计划冲突
                    if (!existingEntity.getName().equals(queryPlan.getName())) {
                        return queryPlanRepository.existsByPortalIdAndNameAndIdNot(
                                existingEntity.getPortalId(), queryPlan.getName(), id)
                                .flatMap(exists -> {
                                    if (exists) {
                                        return Mono.error(new IllegalArgumentException("查询计划名称已存在: " + queryPlan.getName()));
                                    }
                                    return updateQueryPlanEntity(existingEntity, queryPlan);
                                });
                    } else {
                        return updateQueryPlanEntity(existingEntity, queryPlan);
                    }
                })
                .map(queryPlanMapper::toDto);
    }

    /**
     * 删除查询计划
     */
    public Mono<Void> deleteQueryPlan(Long id, Long userId) {
        return queryPlanRepository.findById(id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("查询计划不存在: " + id)))
                .flatMap(entity -> {
                    // 检查权限
                    if (!entity.getUserId().equals(userId)) {
                        return Mono.error(new IllegalArgumentException("无权限删除此查询计划"));
                    }
                    
                    return queryPlanRepository.deleteById(id);
                });
    }

    /**
     * 根据ID获取查询计划
     */
    public Mono<QueryPlan> getQueryPlanById(Long id, Long userId) {
        return queryPlanRepository.findById(id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("查询计划不存在: " + id)))
                .flatMap(entity -> {
                    // 检查权限
                    if (!entity.getUserId().equals(userId)) {
                        return Mono.error(new IllegalArgumentException("无权限访问此查询计划"));
                    }
                    
                    return Mono.just(queryPlanMapper.toDto(entity));
                });
    }

    /**
     * 获取门户下的查询计划列表
     */
    public Flux<QueryPlan> getQueryPlansByPortal(Long portalId, Long userId) {
        // 先验证门户权限
        return portalRepository.findById(portalId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("门户不存在: " + portalId)))
                .flatMap(portalEntity -> {
                    if (!portalEntity.getUserId().equals(userId)) {
                        return Mono.error(new IllegalArgumentException("无权限访问此门户"));
                    }
                    
                    return Mono.just(portalEntity);
                })
                .thenMany(queryPlanRepository.findByPortalIdOrderByCreatedAtDesc(portalId))
                .map(queryPlanMapper::toDto);
    }

    /**
     * 分页获取门户下的查询计划列表
     */
    public Flux<QueryPlan> getQueryPlansByPortal(Long portalId, Long userId, int page, int size) {
        // 先验证门户权限
        return portalRepository.findById(portalId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("门户不存在: " + portalId)))
                .flatMap(portalEntity -> {
                    if (!portalEntity.getUserId().equals(userId)) {
                        return Mono.error(new IllegalArgumentException("无权限访问此门户"));
                    }
                    
                    return Mono.just(portalEntity);
                })
                .thenMany(queryPlanRepository.findByPortalIdOrderByCreatedAtDesc(portalId)
                        .skip((long) page * size)
                        .take(size))
                .map(queryPlanMapper::toDto);
    }

    /**
     * 搜索门户下的查询计划
     */
    public Flux<QueryPlan> searchQueryPlans(Long portalId, Long userId, String keyword) {
        // 先验证门户权限
        return portalRepository.findById(portalId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("门户不存在: " + portalId)))
                .flatMap(portalEntity -> {
                    if (!portalEntity.getUserId().equals(userId)) {
                        return Mono.error(new IllegalArgumentException("无权限访问此门户"));
                    }
                    
                    return Mono.just(portalEntity);
                })
                .thenMany(keyword == null || keyword.trim().isEmpty() ?
                        queryPlanRepository.findByPortalIdOrderByCreatedAtDesc(portalId) :
                        queryPlanRepository.findByPortalIdAndNameContainingIgnoreCase(portalId, keyword.trim()))
                .map(queryPlanMapper::toDto);
    }

    /**
     * 统计门户下的查询计划数量
     */
    public Mono<Long> countQueryPlansByPortal(Long portalId, Long userId) {
        // 先验证门户权限
        return portalRepository.findById(portalId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("门户不存在: " + portalId)))
                .flatMap(portalEntity -> {
                    if (!portalEntity.getUserId().equals(userId)) {
                        return Mono.error(new IllegalArgumentException("无权限访问此门户"));
                    }
                    
                    return queryPlanRepository.countByPortalId(portalId);
                });
    }

    /**
     * 更新查询计划实体
     */
    private Mono<QueryPlanEntity> updateQueryPlanEntity(QueryPlanEntity entity, QueryPlan queryPlan) {
        entity.setName(queryPlan.getName());
        entity.setDescription(queryPlan.getDescription());
        entity.setTargetType(queryPlan.getTargetType());
        entity.setQueryConfig(queryPlanMapper.serializeQueryConfig(queryPlan.getQueryConfig()));
        entity.setColor(queryPlan.getColor());
        entity.setIcon(queryPlan.getIcon());
        entity.setStatus(queryPlan.getStatus());
        entity.setUpdatedAt(LocalDateTime.now());
        
        return queryPlanRepository.save(entity);
    }
}
