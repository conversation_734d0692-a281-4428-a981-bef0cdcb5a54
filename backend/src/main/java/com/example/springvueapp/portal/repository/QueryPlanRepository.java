package com.example.springvueapp.portal.repository;

import com.example.springvueapp.portal.entity.QueryPlanEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 查询计划数据访问接口
 */
@Repository
public interface QueryPlanRepository extends ReactiveCrudRepository<QueryPlanEntity, Long> {

    /**
     * 根据门户ID查找查询计划列表
     */
    Flux<QueryPlanEntity> findByPortalIdOrderByCreatedAtDesc(Long portalId);

    /**
     * 根据门户ID和状态查找查询计划列表
     */
    Flux<QueryPlanEntity> findByPortalIdAndStatusOrderByCreatedAtDesc(Long portalId, String status);

    /**
     * 根据用户ID查找查询计划列表
     */
    Flux<QueryPlanEntity> findByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 根据用户ID和目标类型查找查询计划列表
     */
    Flux<QueryPlanEntity> findByUserIdAndTargetTypeOrderByCreatedAtDesc(Long userId, String targetType);

    /**
     * 根据门户ID和名称模糊查询查询计划列表
     */
    @Query("SELECT * FROM query_plans WHERE portal_id = :portalId AND name LIKE CONCAT('%', :name, '%') ORDER BY created_at DESC")
    Flux<QueryPlanEntity> findByPortalIdAndNameContainingIgnoreCase(Long portalId, String name);

    /**
     * 根据门户ID和名称模糊查询查询计划列表（分页）
     */
    @Query("SELECT * FROM query_plans WHERE portal_id = :portalId AND name LIKE CONCAT('%', :name, '%') ORDER BY created_at DESC LIMIT :#{#pageable.pageSize} OFFSET :#{#pageable.offset}")
    Flux<QueryPlanEntity> findByPortalIdAndNameContainingIgnoreCase(Long portalId, String name, Pageable pageable);

    /**
     * 统计门户下的查询计划数量
     */
    Mono<Long> countByPortalId(Long portalId);

    /**
     * 统计门户下的查询计划数量（按状态）
     */
    Mono<Long> countByPortalIdAndStatus(Long portalId, String status);

    /**
     * 统计用户的查询计划数量
     */
    Mono<Long> countByUserId(Long userId);

    /**
     * 统计用户的查询计划数量（按目标类型）
     */
    Mono<Long> countByUserIdAndTargetType(Long userId, String targetType);

    /**
     * 统计门户下的查询计划数量（按名称模糊查询）
     */
    @Query("SELECT COUNT(*) FROM query_plans WHERE portal_id = :portalId AND name LIKE CONCAT('%', :name, '%')")
    Mono<Long> countByPortalIdAndNameContaining(Long portalId, String name);

    /**
     * 检查查询计划名称是否已存在（同一门户下）
     */
    Mono<Boolean> existsByPortalIdAndName(Long portalId, String name);

    /**
     * 检查查询计划名称是否已存在（同一门户下，排除指定ID）
     */
    @Query("SELECT COUNT(*) > 0 FROM query_plans WHERE portal_id = :portalId AND name = :name AND id != :excludeId")
    Mono<Boolean> existsByPortalIdAndNameAndIdNot(Long portalId, String name, Long excludeId);

    /**
     * 根据门户ID删除所有查询计划
     */
    Mono<Void> deleteByPortalId(Long portalId);

    /**
     * 根据用户ID删除所有查询计划
     */
    Mono<Void> deleteByUserId(Long userId);

    /**
     * 根据门户ID和状态删除查询计划
     */
    Mono<Void> deleteByPortalIdAndStatus(Long portalId, String status);
}
