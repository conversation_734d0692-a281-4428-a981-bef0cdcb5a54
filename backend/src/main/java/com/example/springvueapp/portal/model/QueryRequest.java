package com.example.springvueapp.portal.model;

import java.util.List;

/**
 * 查询请求的DTO类
 * 用于封装查询参数
 */
public class QueryRequest {

    private String targetType; // 目标对象类型

    private String nameFilter; // 名称过滤器

    private List<String> typeFilter; // 类型过滤器

    private List<String> tagFilters; // 标签过滤器

    private List<String> statusFilter; // 状态过滤器

    private String sortBy; // 排序字段

    private String sortOrder; // 排序方向：asc, desc

    private Integer page; // 页码

    private Integer size; // 页大小

    public QueryRequest() {
    }

    public QueryRequest(String targetType, String nameFilter, List<String> typeFilter,
                       List<String> tagFilters, List<String> statusFilter, String sortBy,
                       String sortOrder, Integer page, Integer size) {
        this.targetType = targetType;
        this.nameFilter = nameFilter;
        this.typeFilter = typeFilter;
        this.tagFilters = tagFilters;
        this.statusFilter = statusFilter;
        this.sortBy = sortBy;
        this.sortOrder = sortOrder;
        this.page = page;
        this.size = size;
    }

    // Getters and Setters
    public String getTargetType() {
        return targetType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    public String getNameFilter() {
        return nameFilter;
    }

    public void setNameFilter(String nameFilter) {
        this.nameFilter = nameFilter;
    }

    public List<String> getTypeFilter() {
        return typeFilter;
    }

    public void setTypeFilter(List<String> typeFilter) {
        this.typeFilter = typeFilter;
    }

    public List<String> getTagFilters() {
        return tagFilters;
    }

    public void setTagFilters(List<String> tagFilters) {
        this.tagFilters = tagFilters;
    }

    public List<String> getStatusFilter() {
        return statusFilter;
    }

    public void setStatusFilter(List<String> statusFilter) {
        this.statusFilter = statusFilter;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    @Override
    public String toString() {
        return "QueryRequest{" +
                "targetType='" + targetType + '\'' +
                ", nameFilter='" + nameFilter + '\'' +
                ", typeFilter=" + typeFilter +
                ", tagFilters=" + tagFilters +
                ", statusFilter=" + statusFilter +
                ", sortBy='" + sortBy + '\'' +
                ", sortOrder='" + sortOrder + '\'' +
                ", page=" + page +
                ", size=" + size +
                '}';
    }
}
