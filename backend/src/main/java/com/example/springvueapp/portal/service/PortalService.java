package com.example.springvueapp.portal.service;

import com.example.springvueapp.portal.entity.PortalEntity;
import com.example.springvueapp.portal.enums.PortalStatus;
import com.example.springvueapp.portal.mapper.PortalMapper;
import com.example.springvueapp.portal.model.Portal;
import com.example.springvueapp.portal.repository.PortalRepository;
import com.example.springvueapp.portal.repository.QueryPlanRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 门户服务类
 * 提供门户管理的业务逻辑
 */
@Service
@Transactional()
public class PortalService {

    private final PortalRepository portalRepository;
    private final QueryPlanRepository queryPlanRepository;
    private final PortalMapper portalMapper;

    @Autowired
    public PortalService(PortalRepository portalRepository, 
                        QueryPlanRepository queryPlanRepository,
                        PortalMapper portalMapper) {
        this.portalRepository = portalRepository;
        this.queryPlanRepository = queryPlanRepository;
        this.portalMapper = portalMapper;
    }

    /**
     * 创建门户
     */
    public Mono<Portal> createPortal(Portal portal, Long userId) {
        // 验证门户名称是否已存在
        return portalRepository.existsByUserIdAndName(userId, portal.getName())
                .flatMap(exists -> {
                    if (exists) {
                        return Mono.error(new IllegalArgumentException("门户名称已存在: " + portal.getName()));
                    }
                    
                    // 设置默认值
                    PortalEntity entity = new PortalEntity();
                    entity.setName(portal.getName());
                    entity.setDescription(portal.getDescription());
                    entity.setColor(portal.getColor() != null ? portal.getColor() : "#1890ff");
                    entity.setIcon(portal.getIcon() != null ? portal.getIcon() : "folder");
                    entity.setStatus(PortalStatus.ACTIVE.getCode());
                    entity.setUserId(userId);
                    entity.setCreatedAt(LocalDateTime.now());
                    entity.setUpdatedAt(LocalDateTime.now());
                    
                    return portalRepository.save(entity);
                })
                .map(portalMapper::toDto);
    }

    /**
     * 更新门户
     */
    public Mono<Portal> updatePortal(Long id, Portal portal, Long userId) {
        return portalRepository.findById(id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("门户不存在: " + id)))
                .flatMap(existingEntity -> {
                    // 检查权限
                    if (!existingEntity.getUserId().equals(userId)) {
                        return Mono.error(new IllegalArgumentException("无权限修改此门户"));
                    }
                    
                    // 检查名称是否与其他门户冲突
                    if (!existingEntity.getName().equals(portal.getName())) {
                        return portalRepository.existsByUserIdAndNameAndIdNot(userId, portal.getName(), id)
                                .flatMap(exists -> {
                                    if (exists) {
                                        return Mono.error(new IllegalArgumentException("门户名称已存在: " + portal.getName()));
                                    }
                                    return updatePortalEntity(existingEntity, portal);
                                });
                    } else {
                        return updatePortalEntity(existingEntity, portal);
                    }
                })
                .map(portalMapper::toDto);
    }

    /**
     * 删除门户
     */
    public Mono<Void> deletePortal(Long id, Long userId) {
        return portalRepository.findById(id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("门户不存在: " + id)))
                .flatMap(entity -> {
                    // 检查权限
                    if (!entity.getUserId().equals(userId)) {
                        return Mono.error(new IllegalArgumentException("无权限删除此门户"));
                    }
                    
                    // 先删除门户下的所有查询计划
                    return queryPlanRepository.deleteByPortalId(id)
                            .then(portalRepository.deleteById(id));
                });
    }

    /**
     * 根据ID获取门户
     */
    public Mono<Portal> getPortalById(Long id, Long userId) {
        return portalRepository.findById(id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("门户不存在: " + id)))
                .flatMap(entity -> {
                    // 检查权限
                    if (!entity.getUserId().equals(userId)) {
                        return Mono.error(new IllegalArgumentException("无权限访问此门户"));
                    }
                    
                    // 获取查询计划数量
                    return queryPlanRepository.countByPortalId(id)
                            .map(count -> portalMapper.toDtoWithQueryPlanCount(entity, count.intValue()));
                });
    }

    /**
     * 获取用户的门户列表
     */
    public Flux<Portal> getPortalsByUser(Long userId) {
        return portalRepository.findByUserIdOrderByCreatedAtDesc(userId)
                .flatMap(entity -> 
                    queryPlanRepository.countByPortalId(entity.getId())
                            .map(count -> portalMapper.toDtoWithQueryPlanCount(entity, count.intValue()))
                );
    }

    /**
     * 分页获取用户的门户列表
     */
    public Flux<Portal> getPortalsByUser(Long userId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return portalRepository.findByUserIdOrderByCreatedAtDesc(userId)
                .skip((long) page * size)
                .take(size)
                .flatMap(entity -> 
                    queryPlanRepository.countByPortalId(entity.getId())
                            .map(count -> portalMapper.toDtoWithQueryPlanCount(entity, count.intValue()))
                );
    }

    /**
     * 搜索用户的门户
     */
    public Flux<Portal> searchPortals(Long userId, String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getPortalsByUser(userId);
        }
        
        return portalRepository.findByUserIdAndNameContainingIgnoreCase(userId, keyword.trim())
                .flatMap(entity -> 
                    queryPlanRepository.countByPortalId(entity.getId())
                            .map(count -> portalMapper.toDtoWithQueryPlanCount(entity, count.intValue()))
                );
    }

    /**
     * 统计用户的门户数量
     */
    public Mono<Long> countPortalsByUser(Long userId) {
        return portalRepository.countByUserId(userId);
    }

    /**
     * 统计用户的门户数量（按搜索关键词）
     */
    public Mono<Long> countPortalsByUserAndKeyword(Long userId, String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return countPortalsByUser(userId);
        }
        
        return portalRepository.countByUserIdAndNameContaining(userId, keyword.trim());
    }

    /**
     * 更新门户实体
     */
    private Mono<PortalEntity> updatePortalEntity(PortalEntity entity, Portal portal) {
        entity.setName(portal.getName());
        entity.setDescription(portal.getDescription());
        entity.setColor(portal.getColor());
        entity.setIcon(portal.getIcon());
        entity.setStatus(portal.getStatus());
        entity.setUpdatedAt(LocalDateTime.now());
        
        return portalRepository.save(entity);
    }
}
