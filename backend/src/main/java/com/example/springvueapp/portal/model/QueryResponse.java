package com.example.springvueapp.portal.model;

import java.util.List;

/**
 * 查询响应的DTO类
 * 用于封装分页查询结果
 */
public class QueryResponse {

    private List<QueryableObject> items; // 查询结果项

    private Long total; // 总数量

    private Integer page; // 当前页码

    private Integer size; // 页大小

    private Boolean hasNext; // 是否有下一页

    private Boolean hasPrevious; // 是否有上一页

    public QueryResponse() {
    }

    public QueryResponse(List<QueryableObject> items, Long total, Integer page, Integer size,
                        Boolean hasNext, Boolean hasPrevious) {
        this.items = items;
        this.total = total;
        this.page = page;
        this.size = size;
        this.hasNext = hasNext;
        this.hasPrevious = hasPrevious;
    }

    // Getters and Setters
    public List<QueryableObject> getItems() {
        return items;
    }

    public void setItems(List<QueryableObject> items) {
        this.items = items;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Boolean getHasNext() {
        return hasNext;
    }

    public void setHasNext(Boolean hasNext) {
        this.hasNext = hasNext;
    }

    public Boolean getHasPrevious() {
        return hasPrevious;
    }

    public void setHasPrevious(Boolean hasPrevious) {
        this.hasPrevious = hasPrevious;
    }

    @Override
    public String toString() {
        return "QueryResponse{" +
                "items=" + items +
                ", total=" + total +
                ", page=" + page +
                ", size=" + size +
                ", hasNext=" + hasNext +
                ", hasPrevious=" + hasPrevious +
                '}';
    }
}
