package com.example.springvueapp.portal.repository;

import com.example.springvueapp.portal.entity.PortalEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 门户数据访问接口
 */
@Repository
public interface PortalRepository extends ReactiveCrudRepository<PortalEntity, Long> {

    /**
     * 根据用户ID查找门户列表
     */
    Flux<PortalEntity> findByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 根据用户ID和状态查找门户列表
     */
    Flux<PortalEntity> findByUserIdAndStatusOrderByCreatedAtDesc(Long userId, String status);

    /**
     * 根据用户ID和名称模糊查询门户列表
     */
    @Query("SELECT * FROM portals WHERE user_id = :userId AND name LIKE CONCAT('%', :name, '%') ORDER BY created_at DESC")
    Flux<PortalEntity> findByUserIdAndNameContainingIgnoreCase(Long userId, String name);

    /**
     * 根据用户ID和名称模糊查询门户列表（分页）
     */
    @Query("SELECT * FROM portals WHERE user_id = :userId AND name LIKE CONCAT('%', :name, '%') ORDER BY created_at DESC LIMIT :#{#pageable.pageSize} OFFSET :#{#pageable.offset}")
    Flux<PortalEntity> findByUserIdAndNameContainingIgnoreCase(Long userId, String name, Pageable pageable);

    /**
     * 统计用户的门户数量
     */
    Mono<Long> countByUserId(Long userId);

    /**
     * 统计用户的门户数量（按状态）
     */
    Mono<Long> countByUserIdAndStatus(Long userId, String status);

    /**
     * 统计用户的门户数量（按名称模糊查询）
     */
    @Query("SELECT COUNT(*) FROM portals WHERE user_id = :userId AND name LIKE CONCAT('%', :name, '%')")
    Mono<Long> countByUserIdAndNameContaining(Long userId, String name);

    /**
     * 检查门户名称是否已存在（同一用户下）
     */
    Mono<Boolean> existsByUserIdAndName(Long userId, String name);

    /**
     * 检查门户名称是否已存在（同一用户下，排除指定ID）
     */
    @Query("SELECT COUNT(*) > 0 FROM portals WHERE user_id = :userId AND name = :name AND id != :excludeId")
    Mono<Boolean> existsByUserIdAndNameAndIdNot(Long userId, String name, Long excludeId);

    /**
     * 根据用户ID删除所有门户
     */
    Mono<Void> deleteByUserId(Long userId);

    /**
     * 根据用户ID和状态删除门户
     */
    Mono<Void> deleteByUserIdAndStatus(Long userId, String status);
}
