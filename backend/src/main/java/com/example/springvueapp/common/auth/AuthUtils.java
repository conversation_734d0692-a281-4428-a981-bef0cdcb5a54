package com.example.springvueapp.common.auth;

import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

@Component
public class AuthUtils {
    public Long getUserIdFromAuthentication(Authentication authentication) {
        if (authentication == null || authentication.getName() == null) {
            return 1L; // 测试环境默认用户ID
        }
        try {
            return Long.parseLong(authentication.getName());
        } catch (NumberFormatException e) {
            return 1L;
        }
    }
}
