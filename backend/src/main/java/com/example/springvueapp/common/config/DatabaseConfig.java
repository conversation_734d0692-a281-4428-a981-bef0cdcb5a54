package com.example.springvueapp.common.config;

import io.r2dbc.spi.ConnectionFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories;
import org.springframework.r2dbc.connection.R2dbcTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.reactive.TransactionalOperator;

/**
 * 数据库配置类，同时支持 R2DBC 和 JDBC
 */
@Configuration
@EnableR2dbcRepositories(basePackages = {
        "com.example.springvueapp.common.repository",
        "com.example.springvueapp.mcp.repository",
        "com.example.springvueapp.devops.repository",
        "com.example.springvueapp.cloudplatform.repository",
        "com.example.springvueapp.portal.repository"
})
@EnableTransactionManagement
public class DatabaseConfig {
    @Bean
    public R2dbcTransactionManager transactionManager(ConnectionFactory connectionFactory) {
        return new R2dbcTransactionManager(connectionFactory);
    }

    @Bean
    public TransactionalOperator transactionalOperator(R2dbcTransactionManager transactionManager) {
        return TransactionalOperator.create(transactionManager);
    }
}
