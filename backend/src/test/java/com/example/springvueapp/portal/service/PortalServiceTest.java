package com.example.springvueapp.portal.service;

import com.example.springvueapp.portal.entity.PortalEntity;
import com.example.springvueapp.portal.mapper.PortalMapper;
import com.example.springvueapp.portal.model.Portal;
import com.example.springvueapp.portal.repository.PortalRepository;
import com.example.springvueapp.portal.repository.QueryPlanRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Portal服务单元测试
 */
@ExtendWith(MockitoExtension.class)
class PortalServiceTest {

    @Mock
    private PortalRepository portalRepository;

    @Mock
    private QueryPlanRepository queryPlanRepository;

    @Mock
    private PortalMapper portalMapper;

    @InjectMocks
    private PortalService portalService;

    private PortalEntity testPortalEntity;
    private Portal testPortal;
    private Long testUserId;

    @BeforeEach
    void setUp() {
        testUserId = 1L;
        
        testPortalEntity = new PortalEntity();
        testPortalEntity.setId(1L);
        testPortalEntity.setName("测试门户");
        testPortalEntity.setDescription("测试门户描述");
        testPortalEntity.setColor("#1890ff");
        testPortalEntity.setIcon("folder");
        testPortalEntity.setStatus("active");
        testPortalEntity.setUserId(testUserId);
        testPortalEntity.setCreatedAt(LocalDateTime.now());
        testPortalEntity.setUpdatedAt(LocalDateTime.now());

        testPortal = new Portal();
        testPortal.setId(1L);
        testPortal.setName("测试门户");
        testPortal.setDescription("测试门户描述");
        testPortal.setColor("#1890ff");
        testPortal.setIcon("folder");
        testPortal.setStatus("active");
        testPortal.setUserId(testUserId);
        testPortal.setQueryPlanCount(0);
    }

    @Test
    void createPortal_Success() {
        // Given
        Portal inputPortal = new Portal();
        inputPortal.setName("新门户");
        inputPortal.setDescription("新门户描述");
        inputPortal.setColor("#52c41a");
        inputPortal.setIcon("appstore");

        PortalEntity entityToSave = new PortalEntity();
        entityToSave.setName("新门户");
        entityToSave.setDescription("新门户描述");
        entityToSave.setColor("#52c41a");
        entityToSave.setIcon("appstore");
        entityToSave.setStatus("active");
        entityToSave.setUserId(testUserId);

        PortalEntity savedEntity = new PortalEntity();
        savedEntity.setId(2L);
        savedEntity.setName("新门户");
        savedEntity.setDescription("新门户描述");
        savedEntity.setColor("#52c41a");
        savedEntity.setIcon("appstore");
        savedEntity.setStatus("active");
        savedEntity.setUserId(testUserId);
        savedEntity.setCreatedAt(LocalDateTime.now());
        savedEntity.setUpdatedAt(LocalDateTime.now());

        Portal expectedPortal = new Portal();
        expectedPortal.setId(2L);
        expectedPortal.setName("新门户");
        expectedPortal.setDescription("新门户描述");
        expectedPortal.setColor("#52c41a");
        expectedPortal.setIcon("appstore");
        expectedPortal.setStatus("active");
        expectedPortal.setUserId(testUserId);
        expectedPortal.setQueryPlanCount(0);

        when(portalRepository.existsByUserIdAndName(testUserId, "新门户"))
                .thenReturn(Mono.just(false));
        when(portalRepository.save(any(PortalEntity.class))).thenReturn(Mono.just(savedEntity));
        when(portalMapper.toDto(savedEntity)).thenReturn(expectedPortal);

        // When & Then
        StepVerifier.create(portalService.createPortal(inputPortal, testUserId))
                .expectNext(expectedPortal)
                .verifyComplete();

        verify(portalRepository).existsByUserIdAndName(testUserId, "新门户");
        verify(portalRepository).save(any(PortalEntity.class));
    }

    @Test
    void createPortal_DuplicateName_ThrowsException() {
        // Given
        Portal inputPortal = new Portal();
        inputPortal.setName("重复门户");

        when(portalRepository.existsByUserIdAndName(testUserId, "重复门户"))
                .thenReturn(Mono.just(true));

        // When & Then
        StepVerifier.create(portalService.createPortal(inputPortal, testUserId))
                .expectErrorMatches(throwable -> 
                    throwable instanceof IllegalArgumentException &&
                    throwable.getMessage().contains("门户名称已存在"))
                .verify();

        verify(portalRepository).existsByUserIdAndName(testUserId, "重复门户");
        verify(portalRepository, never()).save(any(PortalEntity.class));
    }

    @Test
    void createPortal_EmptyName_ThrowsException() {
        // Given
        Portal inputPortal = new Portal();
        inputPortal.setName("");
        when(portalRepository.existsByUserIdAndName(testUserId, "")).thenReturn(Mono.just(false));
        when(portalRepository.save(any(PortalEntity.class))).thenReturn(Mono.just(testPortalEntity));
        when(portalMapper.toDto(testPortalEntity)).thenReturn(testPortal);

        // When & Then
        StepVerifier.create(portalService.createPortal(inputPortal, testUserId))
                .expectNext(testPortal)
                .verifyComplete();

        verify(portalRepository).existsByUserIdAndName(testUserId, "");
        verify(portalRepository).save(any(PortalEntity.class));
    }

    @Test
    void getPortalById_Success() {
        // Given
        when(portalRepository.findById(1L))
                .thenReturn(Mono.just(testPortalEntity));
        when(queryPlanRepository.countByPortalId(1L)).thenReturn(Mono.just(0L));
        when(portalMapper.toDtoWithQueryPlanCount(testPortalEntity, 0)).thenReturn(testPortal);

        // When & Then
        StepVerifier.create(portalService.getPortalById(1L, testUserId))
                .expectNext(testPortal)
                .verifyComplete();

        verify(portalRepository).findById(1L);
        verify(queryPlanRepository).countByPortalId(1L);
    }

    @Test
    void getPortalById_NotFound_ThrowsException() {
        // Given
        when(portalRepository.findById(999L))
                .thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(portalService.getPortalById(999L, testUserId))
                .expectErrorMatches(throwable ->
                    throwable instanceof IllegalArgumentException &&
                    throwable.getMessage().contains("门户不存在"))
                .verify();

        verify(portalRepository).findById(999L);
    }

    @Test
    void getPortalsByUser_Success() {
        // Given
        PortalEntity entity2 = new PortalEntity();
        entity2.setId(2L);
        entity2.setName("门户2");
        entity2.setUserId(testUserId);

        Portal portal2 = new Portal();
        portal2.setId(2L);
        portal2.setName("门户2");
        portal2.setUserId(testUserId);

        when(portalRepository.findByUserIdOrderByCreatedAtDesc(testUserId))
                .thenReturn(Flux.just(testPortalEntity, entity2));
        when(queryPlanRepository.countByPortalId(1L)).thenReturn(Mono.just(0L));
        when(queryPlanRepository.countByPortalId(2L)).thenReturn(Mono.just(0L));
        when(portalMapper.toDtoWithQueryPlanCount(testPortalEntity, 0)).thenReturn(testPortal);
        when(portalMapper.toDtoWithQueryPlanCount(entity2, 0)).thenReturn(portal2);

        // When & Then
        StepVerifier.create(portalService.getPortalsByUser(testUserId))
                .expectNext(testPortal)
                .expectNext(portal2)
                .verifyComplete();

        verify(portalRepository).findByUserIdOrderByCreatedAtDesc(testUserId);
        verify(queryPlanRepository).countByPortalId(1L);
        verify(queryPlanRepository).countByPortalId(2L);
    }

    @Test
    void updatePortal_Success() {
        // Given
        Portal updatePortal = new Portal();
        updatePortal.setName("更新后的门户");
        updatePortal.setDescription("更新后的描述");
        updatePortal.setColor("#faad14");

        PortalEntity updatedEntity = new PortalEntity();
        updatedEntity.setId(1L);
        updatedEntity.setName("更新后的门户");
        updatedEntity.setDescription("更新后的描述");
        updatedEntity.setColor("#faad14");
        updatedEntity.setIcon("folder");
        updatedEntity.setStatus("active");
        updatedEntity.setUserId(testUserId);
        updatedEntity.setCreatedAt(testPortalEntity.getCreatedAt());
        updatedEntity.setUpdatedAt(LocalDateTime.now());

        Portal expectedPortal = new Portal();
        expectedPortal.setId(1L);
        expectedPortal.setName("更新后的门户");
        expectedPortal.setDescription("更新后的描述");
        expectedPortal.setColor("#faad14");
        expectedPortal.setIcon("folder");
        expectedPortal.setStatus("active");
        expectedPortal.setUserId(testUserId);
        expectedPortal.setQueryPlanCount(0);

        when(portalRepository.findById(1L))
                .thenReturn(Mono.just(testPortalEntity));
        when(portalRepository.existsByUserIdAndNameAndIdNot(testUserId, "更新后的门户", 1L))
                .thenReturn(Mono.just(false));
        when(portalRepository.save(any(PortalEntity.class))).thenReturn(Mono.just(updatedEntity));
        when(portalMapper.toDto(updatedEntity)).thenReturn(expectedPortal);

        // When & Then
        StepVerifier.create(portalService.updatePortal(1L, updatePortal, testUserId))
                .expectNext(expectedPortal)
                .verifyComplete();

        verify(portalRepository).findById(1L);
        verify(portalRepository).existsByUserIdAndNameAndIdNot(testUserId, "更新后的门户", 1L);
        verify(portalRepository).save(any(PortalEntity.class));
    }

    @Test
    void deletePortal_Success() {
        // Given
        when(portalRepository.findById(1L))
                .thenReturn(Mono.just(testPortalEntity));
        when(queryPlanRepository.deleteByPortalId(1L)).thenReturn(Mono.empty());
        when(portalRepository.deleteById(1L)).thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(portalService.deletePortal(1L, testUserId))
                .verifyComplete();

        verify(portalRepository).findById(1L);
        verify(queryPlanRepository).deleteByPortalId(1L);
        verify(portalRepository).deleteById(1L);
    }

    @Test
    void searchPortals_Success() {
        // Given
        String keyword = "测试";
        when(portalRepository.findByUserIdAndNameContainingIgnoreCase(testUserId, keyword))
                .thenReturn(Flux.just(testPortalEntity));
        when(queryPlanRepository.countByPortalId(1L)).thenReturn(Mono.just(0L));
        when(portalMapper.toDtoWithQueryPlanCount(testPortalEntity, 0)).thenReturn(testPortal);

        // When & Then
        StepVerifier.create(portalService.searchPortals(testUserId, keyword))
                .expectNext(testPortal)
                .verifyComplete();

        verify(portalRepository).findByUserIdAndNameContainingIgnoreCase(testUserId, keyword);
        verify(queryPlanRepository).countByPortalId(1L);
    }

    @Test
    void countPortalsByUser_Success() {
        // Given
        when(portalRepository.countByUserId(testUserId)).thenReturn(Mono.just(5L));

        // When & Then
        StepVerifier.create(portalService.countPortalsByUser(testUserId))
                .expectNext(5L)
                .verifyComplete();

        verify(portalRepository).countByUserId(testUserId);
    }

    @Test
    void getAllPortals_Success() {
        // Given
        PortalEntity entity2 = new PortalEntity();
        entity2.setId(2L);
        entity2.setName("门户2");
        entity2.setUserId(testUserId);

        Portal portal2 = new Portal();
        portal2.setId(2L);
        portal2.setName("门户2");
        portal2.setUserId(testUserId);

        when(portalRepository.findByUserIdOrderByCreatedAtDesc(testUserId))
                .thenReturn(Flux.just(testPortalEntity, entity2));
        when(queryPlanRepository.countByPortalId(1L)).thenReturn(Mono.just(0L));
        when(queryPlanRepository.countByPortalId(2L)).thenReturn(Mono.just(0L));
        when(portalMapper.toDtoWithQueryPlanCount(testPortalEntity, 0)).thenReturn(testPortal);
        when(portalMapper.toDtoWithQueryPlanCount(entity2, 0)).thenReturn(portal2);

        // When & Then
        StepVerifier.create(portalService.getPortalsByUser(testUserId))
                .expectNext(testPortal)
                .expectNext(portal2)
                .verifyComplete();

        verify(portalRepository).findByUserIdOrderByCreatedAtDesc(testUserId);
        verify(queryPlanRepository).countByPortalId(1L);
        verify(queryPlanRepository).countByPortalId(2L);
    }
}
