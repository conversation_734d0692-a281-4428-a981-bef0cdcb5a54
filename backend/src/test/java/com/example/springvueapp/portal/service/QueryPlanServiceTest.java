package com.example.springvueapp.portal.service;

import com.example.springvueapp.portal.entity.QueryPlanEntity;
import com.example.springvueapp.portal.entity.PortalEntity;
import com.example.springvueapp.portal.mapper.QueryPlanMapper;
import com.example.springvueapp.portal.model.QueryPlan;
import com.example.springvueapp.portal.repository.QueryPlanRepository;
import com.example.springvueapp.portal.repository.PortalRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * QueryPlan服务单元测试
 */
@ExtendWith(MockitoExtension.class)
class QueryPlanServiceTest {

    @Mock
    private QueryPlanRepository queryPlanRepository;

    @Mock
    private PortalRepository portalRepository;

    @Mock
    private QueryPlanMapper queryPlanMapper;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private QueryPlanService queryPlanService;

    private QueryPlanEntity testQueryPlanEntity;
    private PortalEntity testPortalEntity;
    private QueryPlan testQueryPlan;
    private Long testUserId;
    private Long testPortalId;

    @BeforeEach
    void setUp() {
        testUserId = 1L;
        testPortalId = 1L;
        
        Map<String, Object> queryConfig = new HashMap<>();
        queryConfig.put("nameFilter", "test");
        queryConfig.put("limit", 10);
        
        testQueryPlanEntity = new QueryPlanEntity();
        testQueryPlanEntity.setId(1L);
        testQueryPlanEntity.setName("测试查询计划");
        testQueryPlanEntity.setDescription("测试查询计划描述");
        testQueryPlanEntity.setPortalId(testPortalId);
        testQueryPlanEntity.setTargetType("CiTask");
        testQueryPlanEntity.setQueryConfig("{\"nameFilter\":\"test\",\"limit\":10}");
        testQueryPlanEntity.setColor("#1890ff");
        testQueryPlanEntity.setIcon("search");
        testQueryPlanEntity.setStatus("active");
        testQueryPlanEntity.setUserId(testUserId);
        testQueryPlanEntity.setCreatedAt(LocalDateTime.now());
        testQueryPlanEntity.setUpdatedAt(LocalDateTime.now());

        testPortalEntity = new PortalEntity();
        testPortalEntity.setId(testPortalId);
        testPortalEntity.setName("测试门户");
        testPortalEntity.setUserId(testUserId);

        testQueryPlan = new QueryPlan();
        testQueryPlan.setId(1L);
        testQueryPlan.setName("测试查询计划");
        testQueryPlan.setDescription("测试查询计划描述");
        testQueryPlan.setPortalId(testPortalId);
        testQueryPlan.setTargetType("CiTask");
        testQueryPlan.setQueryConfig(queryConfig);
        testQueryPlan.setColor("#1890ff");
        testQueryPlan.setIcon("search");
        testQueryPlan.setStatus("active");
        testQueryPlan.setUserId(testUserId);
    }

    @Test
    void createQueryPlan_Success() throws Exception {
        // Given
        QueryPlan inputQueryPlan = new QueryPlan();
        inputQueryPlan.setName("新查询计划");
        inputQueryPlan.setDescription("新查询计划描述");
        inputQueryPlan.setPortalId(testPortalId);
        inputQueryPlan.setTargetType("CdTask");

        Map<String, Object> inputConfig = new HashMap<>();
        inputConfig.put("statusFilter", "active");
        inputQueryPlan.setQueryConfig(inputConfig);

        QueryPlanEntity entityToSave = new QueryPlanEntity();
        entityToSave.setName("新查询计划");
        entityToSave.setDescription("新查询计划描述");
        entityToSave.setPortalId(testPortalId);
        entityToSave.setTargetType("CdTask");
        entityToSave.setQueryConfig("{\"statusFilter\":\"active\"}");
        entityToSave.setColor("#52c41a");
        entityToSave.setIcon("search");
        entityToSave.setStatus("active");
        entityToSave.setUserId(testUserId);

        QueryPlanEntity savedEntity = new QueryPlanEntity();
        savedEntity.setId(2L);
        savedEntity.setName("新查询计划");
        savedEntity.setDescription("新查询计划描述");
        savedEntity.setPortalId(testPortalId);
        savedEntity.setTargetType("CdTask");
        savedEntity.setQueryConfig("{\"statusFilter\":\"active\"}");
        savedEntity.setColor("#52c41a");
        savedEntity.setIcon("search");
        savedEntity.setStatus("active");
        savedEntity.setUserId(testUserId);
        savedEntity.setCreatedAt(LocalDateTime.now());
        savedEntity.setUpdatedAt(LocalDateTime.now());

        QueryPlan expectedQueryPlan = new QueryPlan();
        expectedQueryPlan.setId(2L);
        expectedQueryPlan.setName("新查询计划");
        expectedQueryPlan.setDescription("新查询计划描述");
        expectedQueryPlan.setPortalId(testPortalId);
        expectedQueryPlan.setTargetType("CdTask");
        expectedQueryPlan.setQueryConfig(inputConfig);
        expectedQueryPlan.setColor("#52c41a");
        expectedQueryPlan.setIcon("search");
        expectedQueryPlan.setStatus("active");
        expectedQueryPlan.setUserId(testUserId);

        when(portalRepository.findById(testPortalId)).thenReturn(Mono.just(testPortalEntity));
        when(queryPlanRepository.existsByPortalIdAndName(testPortalId, "新查询计划"))
                .thenReturn(Mono.just(false));
        when(queryPlanMapper.serializeQueryConfig(inputConfig)).thenReturn("{\"statusFilter\":\"active\"}");
        when(queryPlanRepository.save(any(QueryPlanEntity.class))).thenReturn(Mono.just(savedEntity));
        when(queryPlanMapper.toDto(savedEntity)).thenReturn(expectedQueryPlan);

        // When & Then
        StepVerifier.create(queryPlanService.createQueryPlan(inputQueryPlan, testUserId))
                .expectNext(expectedQueryPlan)
                .verifyComplete();

        verify(portalRepository).findById(testPortalId);
        verify(queryPlanRepository).existsByPortalIdAndName(testPortalId, "新查询计划");
        verify(queryPlanRepository).save(any(QueryPlanEntity.class));
    }

    @Test
    void createQueryPlan_DuplicateName_ThrowsException() {
        // Given
        QueryPlan inputQueryPlan = new QueryPlan();
        inputQueryPlan.setName("重复查询计划");
        inputQueryPlan.setPortalId(testPortalId);

        when(portalRepository.findById(testPortalId)).thenReturn(Mono.just(testPortalEntity));
        when(queryPlanRepository.existsByPortalIdAndName(testPortalId, "重复查询计划"))
                .thenReturn(Mono.just(true));

        // When & Then
        StepVerifier.create(queryPlanService.createQueryPlan(inputQueryPlan, testUserId))
                .expectErrorMatches(throwable ->
                    throwable instanceof IllegalArgumentException &&
                    throwable.getMessage().contains("查询计划名称已存在"))
                .verify();

        verify(portalRepository).findById(testPortalId);
        verify(queryPlanRepository).existsByPortalIdAndName(testPortalId, "重复查询计划");
        verify(queryPlanRepository, never()).save(any(QueryPlanEntity.class));
    }

    @Test
    void getQueryPlanById_Success() {
        // Given
        when(queryPlanRepository.findById(1L))
                .thenReturn(Mono.just(testQueryPlanEntity));
        when(queryPlanMapper.toDto(testQueryPlanEntity)).thenReturn(testQueryPlan);

        // When & Then
        StepVerifier.create(queryPlanService.getQueryPlanById(1L, testUserId))
                .expectNext(testQueryPlan)
                .verifyComplete();

        verify(queryPlanRepository).findById(1L);
    }

    @Test
    void getQueryPlanById_NotFound_ThrowsException() {
        // Given
        when(queryPlanRepository.findById(999L))
                .thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(queryPlanService.getQueryPlanById(999L, testUserId))
                .expectErrorMatches(throwable ->
                    throwable instanceof IllegalArgumentException &&
                    throwable.getMessage().contains("查询计划不存在"))
                .verify();

        verify(queryPlanRepository).findById(999L);
    }

    @Test
    void getQueryPlansByPortal_Success() {
        // Given
        QueryPlanEntity entity2 = new QueryPlanEntity();
        entity2.setId(2L);
        entity2.setName("查询计划2");
        entity2.setPortalId(testPortalId);
        entity2.setUserId(testUserId);

        QueryPlan queryPlan2 = new QueryPlan();
        queryPlan2.setId(2L);
        queryPlan2.setName("查询计划2");
        queryPlan2.setPortalId(testPortalId);
        queryPlan2.setUserId(testUserId);

        when(portalRepository.findById(testPortalId)).thenReturn(Mono.just(testPortalEntity));
        when(queryPlanRepository.findByPortalIdOrderByCreatedAtDesc(testPortalId))
                .thenReturn(Flux.just(testQueryPlanEntity, entity2));
        when(queryPlanMapper.toDto(testQueryPlanEntity)).thenReturn(testQueryPlan);
        when(queryPlanMapper.toDto(entity2)).thenReturn(queryPlan2);

        // When & Then
        StepVerifier.create(queryPlanService.getQueryPlansByPortal(testPortalId, testUserId))
                .expectNext(testQueryPlan)
                .expectNext(queryPlan2)
                .verifyComplete();

        verify(portalRepository).findById(testPortalId);
        verify(queryPlanRepository).findByPortalIdOrderByCreatedAtDesc(testPortalId);
    }

    @Test
    void updateQueryPlan_Success() {
        // Given
        QueryPlan updateQueryPlan = new QueryPlan();
        updateQueryPlan.setName("更新后的查询计划");
        updateQueryPlan.setDescription("更新后的描述");
        updateQueryPlan.setTargetType("McpServer");
        
        Map<String, Object> updateConfig = new HashMap<>();
        updateConfig.put("typeFilter", "proxy");
        updateQueryPlan.setQueryConfig(updateConfig);

        QueryPlanEntity updatedEntity = new QueryPlanEntity();
        updatedEntity.setId(1L);
        updatedEntity.setName("更新后的查询计划");
        updatedEntity.setDescription("更新后的描述");
        updatedEntity.setPortalId(testPortalId);
        updatedEntity.setTargetType("McpServer");
        updatedEntity.setQueryConfig("{\"typeFilter\":\"proxy\"}");
        updatedEntity.setColor("#1890ff");
        updatedEntity.setIcon("search");
        updatedEntity.setStatus("active");
        updatedEntity.setUserId(testUserId);
        updatedEntity.setCreatedAt(testQueryPlanEntity.getCreatedAt());
        updatedEntity.setUpdatedAt(LocalDateTime.now());

        QueryPlan expectedQueryPlan = new QueryPlan();
        expectedQueryPlan.setId(1L);
        expectedQueryPlan.setName("更新后的查询计划");
        expectedQueryPlan.setDescription("更新后的描述");
        expectedQueryPlan.setPortalId(testPortalId);
        expectedQueryPlan.setTargetType("McpServer");
        expectedQueryPlan.setQueryConfig(updateConfig);
        expectedQueryPlan.setColor("#1890ff");
        expectedQueryPlan.setIcon("search");
        expectedQueryPlan.setStatus("active");
        expectedQueryPlan.setUserId(testUserId);

        when(queryPlanRepository.findById(1L))
                .thenReturn(Mono.just(testQueryPlanEntity));
        when(queryPlanRepository.existsByPortalIdAndNameAndIdNot(testPortalId, "更新后的查询计划", 1L))
                .thenReturn(Mono.just(false));
        when(queryPlanMapper.serializeQueryConfig(updateConfig)).thenReturn("{\"typeFilter\":\"proxy\"}");
        when(queryPlanRepository.save(any(QueryPlanEntity.class))).thenReturn(Mono.just(updatedEntity));
        when(queryPlanMapper.toDto(updatedEntity)).thenReturn(expectedQueryPlan);

        // When & Then
        StepVerifier.create(queryPlanService.updateQueryPlan(1L, updateQueryPlan, testUserId))
                .expectNext(expectedQueryPlan)
                .verifyComplete();

        verify(queryPlanRepository).findById(1L);
        verify(queryPlanRepository).existsByPortalIdAndNameAndIdNot(testPortalId, "更新后的查询计划", 1L);
        verify(queryPlanRepository).save(any(QueryPlanEntity.class));
    }

    @Test
    void deleteQueryPlan_Success() {
        // Given
        when(queryPlanRepository.findById(1L))
                .thenReturn(Mono.just(testQueryPlanEntity));
        when(queryPlanRepository.deleteById(1L)).thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(queryPlanService.deleteQueryPlan(1L, testUserId))
                .verifyComplete();

        verify(queryPlanRepository).findById(1L);
        verify(queryPlanRepository).deleteById(1L);
    }

    @Test
    void countQueryPlansByPortal_Success() {
        // Given
        when(portalRepository.findById(testPortalId)).thenReturn(Mono.just(testPortalEntity));
        when(queryPlanRepository.countByPortalId(testPortalId))
                .thenReturn(Mono.just(3L));

        // When & Then
        StepVerifier.create(queryPlanService.countQueryPlansByPortal(testPortalId, testUserId))
                .expectNext(3L)
                .verifyComplete();

        verify(portalRepository).findById(testPortalId);
        verify(queryPlanRepository).countByPortalId(testPortalId);
    }
}
