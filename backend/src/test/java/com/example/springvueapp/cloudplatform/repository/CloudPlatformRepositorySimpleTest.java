package com.example.springvueapp.cloudplatform.repository;

import com.example.springvueapp.cloudplatform.entity.CloudPlatform;
import com.example.springvueapp.cloudplatform.entity.ConnectionStatus;
import com.example.springvueapp.cloudplatform.entity.PlatformType;
import com.example.springvueapp.common.entity.UserEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.data.r2dbc.DataR2dbcTest;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.test.context.ActiveProfiles;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CloudPlatformRepository 简化单元测试
 * 测试基本的CRUD操作
 */
@DataR2dbcTest
@ActiveProfiles("test")
public class CloudPlatformRepositorySimpleTest {

    @Autowired
    private CloudPlatformRepository cloudPlatformRepository;

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    private CloudPlatform testPlatform;
    private UserEntity testUser;

    @BeforeEach
    void setUp() {
        // 清理数据
        r2dbcEntityTemplate.delete(CloudPlatform.class).all().block();
        r2dbcEntityTemplate.delete(UserEntity.class).all().block();

        // 创建测试用户
        testUser = new UserEntity();
        testUser.setUsername("testuser");
        testUser.setPassword("password");
        testUser.setEmail("<EMAIL>");
        testUser = r2dbcEntityTemplate.insert(UserEntity.class).using(testUser).block();

        // 创建测试云平台
        testPlatform = new CloudPlatform(
                "测试Kubernetes集群",
                PlatformType.KUBERNETES.name(),
                "https://k8s.example.com",
                "test-secret",
                "测试用的Kubernetes集群",
                ConnectionStatus.CONNECTED.name(),
                "华东-1",
                "v1.25.0",
                "[\"production\", \"k8s\"]",
                "testuser",
                testUser.getId()
        );

        // 保存测试数据
        testPlatform = cloudPlatformRepository.save(testPlatform).block();
    }

    @Test
    void testSaveAndFindById() {
        // 测试保存和根据ID查询
        assertNotNull(testPlatform.getId());
        
        Mono<CloudPlatform> result = cloudPlatformRepository.findById(testPlatform.getId());
        
        StepVerifier.create(result)
                .expectNextMatches(platform -> 
                    platform.getName().equals("测试Kubernetes集群") &&
                    platform.getType().equals(PlatformType.KUBERNETES.name()) &&
                    platform.getUserId().equals(testUser.getId())
                )
                .verifyComplete();
    }

    @Test
    void testFindByUserId() {
        // 测试根据用户ID查询
        Flux<CloudPlatform> result = cloudPlatformRepository.findByUserId(testUser.getId());
        
        StepVerifier.create(result)
                .expectNextCount(1)
                .verifyComplete();
    }

    @Test
    void testFindByUserIdAndType() {
        // 测试根据用户ID和平台类型查询
        Flux<CloudPlatform> result = cloudPlatformRepository.findByUserIdAndType(
                testUser.getId(), PlatformType.KUBERNETES.name());
        
        StepVerifier.create(result)
                .expectNextMatches(platform -> 
                    platform.getName().equals("测试Kubernetes集群")
                )
                .verifyComplete();
    }

    @Test
    void testFindByUserIdAndStatus() {
        // 测试根据用户ID和连接状态查询
        Flux<CloudPlatform> result = cloudPlatformRepository.findByUserIdAndStatus(
                testUser.getId(), ConnectionStatus.CONNECTED.name());
        
        StepVerifier.create(result)
                .expectNextMatches(platform -> 
                    platform.getStatus().equals(ConnectionStatus.CONNECTED.name())
                )
                .verifyComplete();
    }

    @Test
    void testCountByUserId() {
        // 测试统计用户的云平台数量
        Flux<CloudPlatform> result = cloudPlatformRepository.findByUserId(testUser.getId());

        StepVerifier.create(result.count())
                .expectNext(1L)
                .verifyComplete();
    }

    @Test
    void testDeleteById() {
        // 测试删除
        Mono<Void> deleteResult = cloudPlatformRepository.deleteById(testPlatform.getId());
        
        StepVerifier.create(deleteResult)
                .verifyComplete();
        
        // 验证删除成功
        Mono<CloudPlatform> findResult = cloudPlatformRepository.findById(testPlatform.getId());
        StepVerifier.create(findResult)
                .expectNextCount(0)
                .verifyComplete();
    }

    @Test
    void testUpdate() {
        // 测试更新
        testPlatform.setName("更新后的Kubernetes集群");
        testPlatform.setDescription("更新后的描述");
        
        Mono<CloudPlatform> updateResult = cloudPlatformRepository.save(testPlatform);
        
        StepVerifier.create(updateResult)
                .expectNextMatches(platform -> 
                    platform.getName().equals("更新后的Kubernetes集群") &&
                    platform.getDescription().equals("更新后的描述")
                )
                .verifyComplete();
    }
}
