package com.example.springvueapp.devops.controller;

import com.example.springvueapp.cloudplatform.model.CloudPlatformDTO;
import com.example.springvueapp.cloudplatform.service.CloudPlatformService;
import com.example.springvueapp.devops.service.TektonExecutorService;
import com.example.springvueapp.common.model.ApiResponse;
import com.example.springvueapp.common.model.PageResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;
import static org.junit.jupiter.api.Assertions.*;

/**
 * DevOps云平台集成控制器测试
 */
@ExtendWith(MockitoExtension.class)
class DevOpsCloudPlatformControllerTest {

    @Mock
    private CloudPlatformService cloudPlatformService;

    @Mock
    private TektonExecutorService tektonExecutorService;

    @Mock
    private Authentication authentication;

    @Mock
    private UserDetails userDetails;

    @InjectMocks
    private DevOpsCloudPlatformController controller;

    private CloudPlatformDTO testPlatform;
    private Long testUserId = 1L;

    @BeforeEach
    void setUp() {
        testPlatform = new CloudPlatformDTO();
        testPlatform.setId(1L);
        testPlatform.setName("测试Kubernetes集群");
        testPlatform.setType("KUBERNETES");
        testPlatform.setStatus("CONNECTED");
        testPlatform.setCreatedBy(testUserId.toString());

        lenient().when(authentication.getPrincipal()).thenReturn(userDetails);
        lenient().when(userDetails.getUsername()).thenReturn(testUserId.toString());
    }

    @Test
    void testGetAvailableCloudPlatforms_Success() {
        // 准备测试数据
        List<CloudPlatformDTO> platforms = Arrays.asList(testPlatform);
        PageResult<CloudPlatformDTO> pageResult = new PageResult<>(platforms, 0, 100, 1L);

        when(cloudPlatformService.findCloudPlatforms(
            eq(testUserId), eq(0), eq(100), isNull(), eq("KUBERNETES"), eq("CONNECTED"), isNull()))
            .thenReturn(Mono.just(pageResult));

        // 执行测试
        Mono<ResponseEntity<ApiResponse<Flux<CloudPlatformDTO>>>> result = 
            controller.getAvailableCloudPlatforms(authentication);

        // 验证结果
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(200, response.getStatusCodeValue());
                assertNotNull(response.getBody());
                assertTrue(response.getBody().isSuccess());
            })
            .verifyComplete();

        verify(cloudPlatformService).findCloudPlatforms(
            eq(testUserId), eq(0), eq(100), isNull(), eq("KUBERNETES"), eq("CONNECTED"), isNull());
    }

    @Test
    void testSelectCloudPlatform_Success() {
        // 准备测试数据
        Long platformId = 1L;
        when(tektonExecutorService.setCloudPlatform(testUserId, platformId))
            .thenReturn(Mono.just(true));

        // 执行测试
        Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> result = 
            controller.selectCloudPlatform(platformId, authentication);

        // 验证结果
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(200, response.getStatusCodeValue());
                assertNotNull(response.getBody());
                assertTrue(response.getBody().isSuccess());
                
                Map<String, Object> data = response.getBody().getData();
                assertEquals(true, data.get("success"));
                assertEquals(platformId, data.get("cloudPlatformId"));
                assertEquals("云平台切换成功", data.get("message"));
            })
            .verifyComplete();

        verify(tektonExecutorService).setCloudPlatform(testUserId, platformId);
    }

    @Test
    void testSelectCloudPlatform_Failure() {
        // 准备测试数据
        Long platformId = 1L;
        when(tektonExecutorService.setCloudPlatform(testUserId, platformId))
            .thenReturn(Mono.just(false));

        // 执行测试
        Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> result = 
            controller.selectCloudPlatform(platformId, authentication);

        // 验证结果
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(400, response.getStatusCodeValue());
                assertNotNull(response.getBody());
                assertFalse(response.getBody().isSuccess());
                
                Map<String, Object> data = response.getBody().getData();
                assertEquals(false, data.get("success"));
                assertEquals(platformId, data.get("cloudPlatformId"));
            })
            .verifyComplete();

        verify(tektonExecutorService).setCloudPlatform(testUserId, platformId);
    }

    @Test
    void testGetCurrentCloudPlatform_WithPlatform() {
        // 准备测试数据
        String currentPlatformId = "1";
        when(tektonExecutorService.getCurrentCloudPlatformId()).thenReturn(currentPlatformId);
        when(cloudPlatformService.findCloudPlatformById(testUserId, 1L))
            .thenReturn(Mono.just(testPlatform));

        // 执行测试
        Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> result = 
            controller.getCurrentCloudPlatform(authentication);

        // 验证结果
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(200, response.getStatusCodeValue());
                assertNotNull(response.getBody());
                assertTrue(response.getBody().isSuccess());
                
                Map<String, Object> data = response.getBody().getData();
                assertEquals(currentPlatformId, data.get("currentCloudPlatformId"));
                assertEquals(testPlatform, data.get("platform"));
            })
            .verifyComplete();

        verify(tektonExecutorService).getCurrentCloudPlatformId();
        verify(cloudPlatformService).findCloudPlatformById(testUserId, 1L);
    }

    @Test
    void testGetCurrentCloudPlatform_NoPlatform() {
        // 准备测试数据
        when(tektonExecutorService.getCurrentCloudPlatformId()).thenReturn(null);

        // 执行测试
        Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> result = 
            controller.getCurrentCloudPlatform(authentication);

        // 验证结果
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(200, response.getStatusCodeValue());
                assertNotNull(response.getBody());
                assertTrue(response.getBody().isSuccess());
                
                Map<String, Object> data = response.getBody().getData();
                assertNull(data.get("currentCloudPlatformId"));
                assertEquals("未设置云平台", data.get("message"));
            })
            .verifyComplete();

        verify(tektonExecutorService).getCurrentCloudPlatformId();
        verify(cloudPlatformService, never()).findCloudPlatformById(any(), any());
    }

    @Test
    void testTestCloudPlatformConnection_Success() {
        // 准备测试数据
        Long platformId = 1L;
        Map<String, Object> executorInfo = new HashMap<>();
        executorInfo.put("connected", true);
        executorInfo.put("platform", "kubernetes");
        executorInfo.put("kubernetesVersion", "v1.24.0");
        executorInfo.put("capabilities", new HashMap<>());

        when(tektonExecutorService.setCloudPlatform(testUserId, platformId))
            .thenReturn(Mono.just(true));
        when(tektonExecutorService.getExecutorInfo())
            .thenReturn(Mono.just(executorInfo));

        // 执行测试
        Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> result = 
            controller.testCloudPlatformConnection(platformId, authentication);

        // 验证结果
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(200, response.getStatusCodeValue());
                assertNotNull(response.getBody());
                assertTrue(response.getBody().isSuccess());
                
                Map<String, Object> data = response.getBody().getData();
                assertEquals(true, data.get("connected"));
                assertEquals("kubernetes", data.get("platform"));
                assertEquals("v1.24.0", data.get("kubernetesVersion"));
            })
            .verifyComplete();

        verify(tektonExecutorService).setCloudPlatform(testUserId, platformId);
        verify(tektonExecutorService).getExecutorInfo();
    }

    @Test
    void testTestCloudPlatformConnection_SetupFailure() {
        // 准备测试数据
        Long platformId = 1L;
        when(tektonExecutorService.setCloudPlatform(testUserId, platformId))
            .thenReturn(Mono.just(false));

        // 执行测试
        Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> result = 
            controller.testCloudPlatformConnection(platformId, authentication);

        // 验证结果
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(400, response.getStatusCodeValue());
                assertNotNull(response.getBody());
                assertFalse(response.getBody().isSuccess());
                
                Map<String, Object> data = response.getBody().getData();
                assertEquals(false, data.get("connected"));
                assertEquals("云平台连接设置失败", data.get("message"));
            })
            .verifyComplete();

        verify(tektonExecutorService).setCloudPlatform(testUserId, platformId);
        verify(tektonExecutorService, never()).getExecutorInfo();
    }

    @Test
    void testGetExecutorInfo_Success() {
        // 准备测试数据
        Map<String, Object> executorInfo = new HashMap<>();
        executorInfo.put("connected", true);
        executorInfo.put("platform", "kubernetes");

        when(tektonExecutorService.getExecutorInfo())
            .thenReturn(Mono.just(executorInfo));

        // 执行测试
        Mono<ResponseEntity<ApiResponse<Map<String, Object>>>> result = 
            controller.getExecutorInfo(authentication);

        // 验证结果
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(200, response.getStatusCodeValue());
                assertNotNull(response.getBody());
                assertTrue(response.getBody().isSuccess());
                assertEquals(executorInfo, response.getBody().getData());
            })
            .verifyComplete();

        verify(tektonExecutorService).getExecutorInfo();
    }

    @Test
    void testGetUserIdFromAuthentication_InvalidUserId() {
        // 准备测试数据
        when(authentication.getPrincipal()).thenReturn(userDetails);
        when(userDetails.getUsername()).thenReturn("invalid");

        // 执行测试并验证异常
        assertThrows(IllegalArgumentException.class, () -> {
            controller.getAvailableCloudPlatforms(authentication).block();
        });
    }

    @Test
    void testGetUserIdFromAuthentication_NoAuthentication() {
        // 执行测试并验证异常
        assertThrows(IllegalArgumentException.class, () -> {
            controller.getAvailableCloudPlatforms(null).block();
        });
    }
}
