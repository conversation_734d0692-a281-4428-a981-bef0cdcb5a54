package com.example.springvueapp.devops.service;

import com.example.springvueapp.devops.client.TektonClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * TektonExecutorService的单元测试
 */
@ExtendWith(MockitoExtension.class)
class TektonExecutorServiceTest {

    @Mock
    private TektonClient tektonClient;

    private TektonExecutorService tektonExecutorService;

    @BeforeEach
    void setUp() {
        tektonExecutorService = new TektonExecutorService(tektonClient);
        // 使用反射设置defaultNamespace字段
        try {
            java.lang.reflect.Field field = TektonExecutorService.class.getDeclaredField("defaultNamespace");
            field.setAccessible(true);
            field.set(tektonExecutorService, "default");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void testGetTaskStatus_WithValidTaskId_ShouldReturnStatus() {
        // Given
        String taskId = "deploy-app-12345678";
        
        Map<String, Object> mockStatus = new HashMap<>();
        mockStatus.put("taskId", taskId);
        mockStatus.put("status", "RUNNING");
        mockStatus.put("progress", 50);
        mockStatus.put("startTime", "2023-01-01T00:00:00Z");
        mockStatus.put("message", "Task is running");
        
        when(tektonClient.getTaskRunStatus(eq("default"), anyString()))
                .thenReturn(Mono.just(mockStatus));

        // When & Then
        StepVerifier.create(tektonExecutorService.getTaskStatus(taskId))
                .expectNextMatches(status -> {
                    return status.get("taskId").equals(taskId) &&
                           status.get("status").equals("RUNNING");
                })
                .verifyComplete();
    }

    @Test
    void testStopTask_WithValidTaskId_ShouldReturnTrue() {
        // Given
        String taskId = "deploy-app-12345678";
        
        when(tektonClient.deleteTaskRun(eq("default"), anyString()))
                .thenReturn(Mono.just(true));

        // When & Then
        StepVerifier.create(tektonExecutorService.stopTask(taskId))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testCancelTask_WithValidTaskId_ShouldReturnTrue() {
        // Given
        String taskId = "deploy-app-12345678";
        
        when(tektonClient.deleteTaskRun(eq("default"), anyString()))
                .thenReturn(Mono.just(true));

        // When & Then
        StepVerifier.create(tektonExecutorService.cancelTask(taskId))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testGetTaskLogs_WithValidTaskId_ShouldReturnLogs() {
        // Given
        String taskId = "deploy-app-12345678";
        
        when(tektonClient.getTaskRunLogs(eq("default"), anyString()))
                .thenReturn(Mono.just("Task logs content"));

        // When & Then
        StepVerifier.create(tektonExecutorService.getTaskLogs(taskId))
                .expectNext("Task logs content")
                .verifyComplete();
    }

    @Test
    void testGetTaskResult_WithValidTaskId_ShouldReturnResult() {
        // Given
        String taskId = "deploy-app-12345678";
        
        Map<String, Object> mockStatus = new HashMap<>();
        mockStatus.put("taskId", taskId);
        mockStatus.put("status", "COMPLETED");
        mockStatus.put("startTime", "2023-01-01T00:00:00Z");
        mockStatus.put("completionTime", "2023-01-01T00:05:00Z");
        
        when(tektonClient.getTaskRunStatus(eq("default"), anyString()))
                .thenReturn(Mono.just(mockStatus));

        // When & Then
        StepVerifier.create(tektonExecutorService.getTaskResult(taskId))
                .expectNextMatches(result -> {
                    return result.get("taskId").equals(taskId) &&
                           result.get("status").equals("COMPLETED");
                })
                .verifyComplete();
    }

    @Test
    void testListTasks_WithValidNamespace_ShouldReturnTaskList() {
        // Given
        String namespace = "default";
        
        Map<String, Object> task1 = new HashMap<>();
        task1.put("name", "task1");
        task1.put("status", "RUNNING");
        
        Map<String, Object> task2 = new HashMap<>();
        task2.put("name", "task2");
        task2.put("status", "COMPLETED");
        
        when(tektonClient.listTaskRuns(anyString(), isNull()))
                .thenReturn(Flux.just(task1, task2));
        
        when(tektonClient.listPipelineRuns(anyString(), isNull()))
                .thenReturn(Flux.empty());

        // When & Then
        StepVerifier.create(tektonExecutorService.listTasks(namespace))
                .expectNextCount(2)
                .verifyComplete();
    }

    @Test
    void testCleanupCompletedTasks_WithValidInput_ShouldReturnCleanedCount() {
        // Given
        String namespace = "default";
        int keepCount = 5;
        
        when(tektonClient.cleanupCompletedTaskRuns(anyString(), isNull(), anyInt()))
                .thenReturn(Mono.just(2));
        
        when(tektonClient.cleanupCompletedPipelineRuns(anyString(), isNull(), anyInt()))
                .thenReturn(Mono.just(1));

        // When & Then
        StepVerifier.create(tektonExecutorService.cleanupCompletedTasks(namespace, keepCount))
                .expectNext(3)
                .verifyComplete();
    }

    @Test
    void testCreateNamespace_WithValidNamespace_ShouldReturnTrue() {
        // Given
        String namespace = "test-namespace";
        
        when(tektonClient.createNamespace(anyString()))
                .thenReturn(Mono.just(true));

        // When & Then
        StepVerifier.create(tektonExecutorService.createNamespace(namespace))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testDeleteNamespace_WithValidNamespace_ShouldReturnTrue() {
        // Given
        String namespace = "test-namespace";
        
        when(tektonClient.deleteNamespace(anyString()))
                .thenReturn(Mono.just(true));

        // When & Then
        StepVerifier.create(tektonExecutorService.deleteNamespace(namespace))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testListNamespaces_ShouldReturnNamespaceList() {
        // Given
        when(tektonClient.listNamespaces())
                .thenReturn(Flux.just("default", "tekton-pipelines", "devops-prod", "devops-staging"));

        // When & Then
        StepVerifier.create(tektonExecutorService.listNamespaces())
                .expectNext("default")
                .expectNext("tekton-pipelines")
                .expectNext("devops-prod")
                .expectNext("devops-staging")
                .verifyComplete();
    }

    @Test
    void testCheckConnection_ShouldReturnTrue() {
        // Given
        when(tektonClient.checkConnection())
                .thenReturn(Mono.just(true));

        // When & Then
        StepVerifier.create(tektonExecutorService.checkConnection())
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testGetExecutorInfo_ShouldReturnExecutorDetails() {
        // Given
        Map<String, Object> clusterInfo = new HashMap<>();
        clusterInfo.put("platform", "Tekton");
        clusterInfo.put("connected", true);
        clusterInfo.put("kubernetesVersion", "v1.24.0");
        
        when(tektonClient.getClusterInfo())
                .thenReturn(Mono.just(clusterInfo));

        // When & Then
        StepVerifier.create(tektonExecutorService.getExecutorInfo())
                .expectNextMatches(info -> {
                    return "Tekton".equals(info.get("executor")) &&
                           Boolean.TRUE.equals(info.get("connected"));
                })
                .verifyComplete();
    }

    @Test
    void testValidateDeploymentCommand_WithValidCommand_ShouldReturnTrue() {
        // Given
        String validCommand = "kubectl apply -f deployment.yaml";

        // When & Then
        StepVerifier.create(tektonExecutorService.validateDeploymentCommand(validCommand))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testValidateDeploymentCommand_WithInvalidCommand_ShouldReturnFalse() {
        // Given
        String invalidCommand = "invalid command";

        // When & Then
        StepVerifier.create(tektonExecutorService.validateDeploymentCommand(invalidCommand))
                .expectNext(false)
                .verifyComplete();
    }

    @Test
    void testValidateDeploymentCommand_WithNullCommand_ShouldReturnFalse() {
        // When & Then
        StepVerifier.create(tektonExecutorService.validateDeploymentCommand(null))
                .expectNext(false)
                .verifyComplete();
    }

    @Test
    void testValidateDeploymentCommand_WithEmptyCommand_ShouldReturnFalse() {
        // When & Then
        StepVerifier.create(tektonExecutorService.validateDeploymentCommand(""))
                .expectNext(false)
                .verifyComplete();
    }

    @Test
    void testGetSupportedCommandTypes_ShouldReturnCommandTypes() {
        // When & Then
        StepVerifier.create(tektonExecutorService.getSupportedCommandTypes())
                .expectNext("tekton-task")
                .expectNext("tekton-pipeline")
                .expectNext("kubectl")
                .expectNext("shell")
                .verifyComplete();
    }

    @Test
    void testMonitorTaskProgress_WithValidTaskId_ShouldReturnProgressStream() {
        // Given
        String taskId = "deploy-app-12345678";
        
        Map<String, Object> mockStatus = new HashMap<>();
        mockStatus.put("taskId", taskId);
        mockStatus.put("status", "RUNNING");
        
        when(tektonClient.getTaskRunStatus(eq("default"), anyString()))
                .thenReturn(Mono.just(mockStatus));

        // When & Then
        StepVerifier.create(tektonExecutorService.monitorTaskProgress(taskId))
                .expectNextCount(1)
                .verifyComplete();
    }

    @Test
    void testGetTaskMetrics_WithValidTaskId_ShouldReturnMetrics() {
        // Given
        String taskId = "deploy-app-12345678";
        
        Map<String, Object> mockStatus = new HashMap<>();
        mockStatus.put("taskId", taskId);
        mockStatus.put("status", "RUNNING");
        mockStatus.put("startTime", "2023-01-01T00:00:00Z");
        mockStatus.put("completionTime", "2023-01-01T00:05:00Z");
        
        when(tektonClient.getTaskRunStatus(eq("default"), anyString()))
                .thenReturn(Mono.just(mockStatus));

        // When & Then
        StepVerifier.create(tektonExecutorService.getTaskMetrics(taskId))
                .expectNextMatches(metrics -> {
                    return metrics.get("taskId").equals(taskId) &&
                           metrics.containsKey("status") &&
                           metrics.containsKey("startTime");
                })
                .verifyComplete();
    }

    @Test
    void testRetryTask_WithValidTaskId_ShouldReturnRetryInfo() {
        // Given
        String taskId = "deploy-app-12345678";
        
        Map<String, Object> mockStatus = new HashMap<>();
        mockStatus.put("taskId", taskId);
        mockStatus.put("status", "COMPLETED");
        
        when(tektonClient.getTaskRunStatus(eq("default"), anyString()))
                .thenReturn(Mono.just(mockStatus));

        // When & Then
        StepVerifier.create(tektonExecutorService.retryTask(taskId))
                .expectNextMatches(result -> {
                    return result.get("originalTaskId").equals(taskId) &&
                           result.containsKey("message");
                })
                .verifyComplete();
    }

    @Test
    void testPauseTask_WithValidTaskId_ShouldReturnTrue() {
        // Given
        String taskId = "deploy-app-12345678";

        // When & Then
        StepVerifier.create(tektonExecutorService.pauseTask(taskId))
                .expectNext(false) // Tekton不支持暂停任务
                .verifyComplete();
    }

    @Test
    void testResumeTask_WithValidTaskId_ShouldReturnTrue() {
        // Given
        String taskId = "deploy-app-12345678";

        // When & Then
        StepVerifier.create(tektonExecutorService.resumeTask(taskId))
                .expectNext(false) // Tekton不支持恢复任务
                .verifyComplete();
    }
}