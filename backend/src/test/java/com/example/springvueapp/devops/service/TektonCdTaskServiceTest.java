package com.example.springvueapp.devops.service;

import com.example.springvueapp.devops.entity.DevOpsCdTaskEntity;
import com.example.springvueapp.devops.entity.DevOpsApplicationEntity;
import com.example.springvueapp.devops.mapper.DevOpsCdTaskMapper;
import com.example.springvueapp.devops.model.DevOpsCdTask;
import com.example.springvueapp.devops.repository.DevOpsCdTaskRepository;
import com.example.springvueapp.devops.repository.DevOpsApplicationRepository;
import com.example.springvueapp.devops.service.TektonExecutorService;
import com.example.springvueapp.devops.util.TektonYamlConfigBuilder;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TektonCdTaskService的单元测试
 */
@ExtendWith(MockitoExtension.class)
class TektonCdTaskServiceTest {

    @Mock
    private DevOpsCdTaskRepository cdTaskRepository;

    @Mock
    private DevOpsApplicationRepository applicationRepository;

    @Mock
    private DevOpsCdTaskMapper cdTaskMapper;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private TektonExecutorService tektonExecutorService;

    @Mock
    private TektonYamlConfigBuilder yamlConfigBuilder;

    private TektonCdTaskService tektonCdTaskService;

    @BeforeEach
    void setUp() {
        tektonCdTaskService = new TektonCdTaskService(
                cdTaskRepository,
                applicationRepository,
                cdTaskMapper,
                objectMapper,
                tektonExecutorService,
                yamlConfigBuilder
        );
    }

    @Test
    void testCreateCdTask_WithValidInput_ShouldCreateTask() throws JsonProcessingException {
        // Given
        Long applicationId = 1L;
        Long userId = 10L;
        
        DevOpsApplicationEntity application = DevOpsApplicationEntity.builder()
                .id(applicationId)
                .name("test-application")
                .userId(userId)
                .build();

        Map<String, String> componentVersions = new HashMap<>();
        componentVersions.put("component1", "v1.0.0");
        
        Map<String, Object> configuration = new HashMap<>();
        configuration.put("deploymentStrategy", "rolling");

        DevOpsCdTask cdTask = DevOpsCdTask.builder()
                .name("test-cd-task")
                .description("测试CD任务")
                .componentVersions(componentVersions)
                .configuration(configuration)
                .build();

        DevOpsCdTaskEntity savedEntity = DevOpsCdTaskEntity.builder()
                .id(1L)
                .name("test-cd-task")
                .description("测试CD任务")
                .applicationId(applicationId)
                .status("INACTIVE")
                .userId(userId)
                .componentVersions("{\"component1\":\"v1.0.0\"}")
                .configuration("{\"deploymentStrategy\":\"rolling\"}")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        DevOpsCdTask expectedTask = DevOpsCdTask.builder()
                .id(1L)
                .name("test-cd-task")
                .description("测试CD任务")
                .applicationId(applicationId)
                .componentVersions(componentVersions)
                .configuration(configuration)
                .status("ACTIVE")
                .userId(userId)
                .build();

        // When
        when(applicationRepository.findByUserIdAndId(userId, applicationId))
                .thenReturn(Mono.just(application));
        when(cdTaskRepository.existsByApplicationIdAndName(applicationId, "test-cd-task"))
                .thenReturn(Mono.just(false));
        when(objectMapper.writeValueAsString(componentVersions))
                .thenReturn("{\"component1\":\"v1.0.0\"}");
        when(objectMapper.writeValueAsString(configuration))
                .thenReturn("{\"deploymentStrategy\":\"rolling\"}");
        when(cdTaskRepository.save(any(DevOpsCdTaskEntity.class)))
                .thenReturn(Mono.just(savedEntity));
        when(cdTaskMapper.toModel(savedEntity))
                .thenReturn(expectedTask);

        // Then
        StepVerifier.create(tektonCdTaskService.createCdTask(cdTask, applicationId, userId))
                .expectNext(expectedTask)
                .verifyComplete();

        verify(applicationRepository).findByUserIdAndId(userId, applicationId);
        verify(cdTaskRepository).existsByApplicationIdAndName(applicationId, "test-cd-task");
        verify(objectMapper).writeValueAsString(componentVersions);
        verify(objectMapper).writeValueAsString(configuration);
        verify(cdTaskRepository).save(any(DevOpsCdTaskEntity.class));
        verify(cdTaskMapper).toModel(savedEntity);
    }

    @Test
    void testCreateCdTask_WithInvalidApplication_ShouldThrowException() {
        // Given
        Long applicationId = 1L;
        Long userId = 10L;
        
        DevOpsCdTask cdTask = DevOpsCdTask.builder()
                .name("test-cd-task")
                .build();

        // When
        when(applicationRepository.findByUserIdAndId(userId, applicationId))
                .thenReturn(Mono.empty());

        // Then
        StepVerifier.create(tektonCdTaskService.createCdTask(cdTask, applicationId, userId))
                .expectErrorMatches(throwable -> 
                    throwable instanceof IllegalArgumentException &&
                    throwable.getMessage().equals("应用不存在或无权限访问"))
                .verify();

        verify(applicationRepository).findByUserIdAndId(userId, applicationId);
        verifyNoInteractions(cdTaskMapper, cdTaskRepository);
    }

    @Test
    void testGetAllCdTasks_ShouldReturnAllTasks() {
        // Given
        Long userId = 10L;
        
        DevOpsCdTaskEntity entity1 = DevOpsCdTaskEntity.builder()
                .id(1L)
                .name("task1")
                .userId(userId)
                .build();
                
        DevOpsCdTaskEntity entity2 = DevOpsCdTaskEntity.builder()
                .id(2L)
                .name("task2")
                .userId(userId)
                .build();

        DevOpsCdTask task1 = DevOpsCdTask.builder()
                .id(1L)
                .name("task1")
                .userId(userId)
                .build();
                
        DevOpsCdTask task2 = DevOpsCdTask.builder()
                .id(2L)
                .name("task2")
                .userId(userId)
                .build();

        // When
        when(cdTaskRepository.findByUserId(userId))
                .thenReturn(Flux.just(entity1, entity2));
        when(cdTaskMapper.toModel(entity1))
                .thenReturn(task1);
        when(cdTaskMapper.toModel(entity2))
                .thenReturn(task2);

        // Then
        StepVerifier.create(tektonCdTaskService.getAllCdTasks(userId))
                .expectNext(task1)
                .expectNext(task2)
                .verifyComplete();

        verify(cdTaskRepository).findByUserId(userId);
        verify(cdTaskMapper).toModel(entity1);
        verify(cdTaskMapper).toModel(entity2);
    }

    @Test
    void testGetCdTaskById_WithValidId_ShouldReturnTask() {
        // Given
        Long taskId = 1L;
        Long userId = 10L;
        
        DevOpsCdTaskEntity entity = DevOpsCdTaskEntity.builder()
                .id(taskId)
                .name("test-task")
                .userId(userId)
                .build();

        DevOpsCdTask expectedTask = DevOpsCdTask.builder()
                .id(taskId)
                .name("test-task")
                .userId(userId)
                .build();

        // When
        when(cdTaskRepository.findByUserIdAndId(userId, taskId))
                .thenReturn(Mono.just(entity));
        when(cdTaskMapper.toModel(entity))
                .thenReturn(expectedTask);

        // Then
        StepVerifier.create(tektonCdTaskService.getCdTaskById(taskId, userId))
                .expectNext(expectedTask)
                .verifyComplete();

        verify(cdTaskRepository).findByUserIdAndId(userId, taskId);
        verify(cdTaskMapper).toModel(entity);
    }

    @Test
    void testGetCdTaskById_WithInvalidId_ShouldThrowException() {
        // Given
        Long taskId = 1L;
        Long userId = 10L;

        // When
        when(cdTaskRepository.findByUserIdAndId(userId, taskId))
                .thenReturn(Mono.empty());

        // Then
        StepVerifier.create(tektonCdTaskService.getCdTaskById(taskId, userId))
                .expectErrorMatches(throwable -> 
                    throwable instanceof IllegalArgumentException &&
                    throwable.getMessage().equals("CD任务不存在或无权限访问"))
                .verify();

        verify(cdTaskRepository).findByUserIdAndId(userId, taskId);
        verifyNoInteractions(cdTaskMapper);
    }

    @Test
    void testDeleteCdTask_WithValidId_ShouldDeleteTask() {
        // Given
        Long taskId = 1L;
        Long userId = 10L;
        
        DevOpsCdTaskEntity entity = DevOpsCdTaskEntity.builder()
                .id(taskId)
                .name("test-task")
                .userId(userId)
                .build();

        // When
        when(cdTaskRepository.findByUserIdAndId(userId, taskId))
                .thenReturn(Mono.just(entity));
        when(cdTaskRepository.delete(entity))
                .thenReturn(Mono.empty());

        // Then
        StepVerifier.create(tektonCdTaskService.deleteCdTask(taskId, userId))
                .expectNext(true)
                .verifyComplete();

        verify(cdTaskRepository).findByUserIdAndId(userId, taskId);
        verify(cdTaskRepository).delete(entity);
    }

    @Test
    void testDeleteCdTask_WithInvalidId_ShouldReturnFalse() {
        // Given
        Long taskId = 1L;
        Long userId = 10L;

        // When
        when(cdTaskRepository.findByUserIdAndId(userId, taskId))
                .thenReturn(Mono.empty());

        // Then
        StepVerifier.create(tektonCdTaskService.deleteCdTask(taskId, userId))
                .expectNext(false)
                .verifyComplete();

        verify(cdTaskRepository).findByUserIdAndId(userId, taskId);
        verify(cdTaskRepository, never()).delete(any());
    }

    @Test
    void testStartCdTask_WithValidId_ShouldStartTask() {
        // Given
        Long taskId = 1L;
        Long userId = 10L;
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("version", "v1.0.0");
        
        DevOpsCdTaskEntity entity = DevOpsCdTaskEntity.builder()
                .id(taskId)
                .name("test-task")
                .status("ACTIVE")
                .userId(userId)
                .build();

        DevOpsCdTaskEntity updatedEntity = DevOpsCdTaskEntity.builder()
                .id(taskId)
                .name("test-task")
                .status("DEPLOYING")
                .userId(userId)
                .updatedAt(LocalDateTime.now())
                .build();

        // When
        when(cdTaskRepository.findByUserIdAndId(userId, taskId))
                .thenReturn(Mono.just(entity));
        when(cdTaskRepository.save(any(DevOpsCdTaskEntity.class)))
                .thenReturn(Mono.just(updatedEntity));
        when(yamlConfigBuilder.buildCdPipelineConfig(any(String.class), any(Map.class)))
                .thenReturn(new HashMap<String, Object>());
        when(yamlConfigBuilder.buildCdPipelineRunConfig(any(String.class), any(String.class), any(Map.class)))
                .thenReturn(new HashMap<String, Object>());
        when(tektonExecutorService.createPipelineRun(any(String.class), any(String.class), any(Map.class)))
                .thenReturn(Mono.just(new HashMap<String, Object>()));

        // Then
        StepVerifier.create(tektonCdTaskService.startCdTask(taskId, userId, parameters))
                .expectNextMatches(result -> {
                    return result.containsKey("taskId") && 
                           result.containsKey("deploymentId") &&
                           result.containsKey("status") &&
                           "DEPLOYING".equals(result.get("status"));
                })
                .verifyComplete();

        verify(cdTaskRepository).findByUserIdAndId(userId, taskId);
        verify(cdTaskRepository).save(any(DevOpsCdTaskEntity.class));
    }
}
